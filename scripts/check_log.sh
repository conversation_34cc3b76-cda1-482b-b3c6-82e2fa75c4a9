#!/usr/bin/env bash
regexp="socinext|cdns|trilinear|fail|error|timeout|underrun|phy err on|PVR|PowerVR|IMG|imagination|call trace|unable to handle|apollo|i2c reset done|Invalid"
#echo $regexp
except_reg="local error reg|mtgpu: module verification failed:|Server Errors: 0|MUSA Errors: WGP:0, TRP:0|MULTICORE_AXI_ERROR|pvr_component_ops|gpu device suspend enter|gpu device suspend exit|gpu device resume enter|gpu device resume exit"
check_type=$1
log_type=$2

display_card_id=`lspci -nn|grep 1ed5|grep -v -E \[1ed5:[0-9]+ff\]|sed "s/.*\[1ed5://"|head -n 1|sed "s/\].*//"`
if [[ "$display_card_id" == "a101" || "$display_card_id" == "" ]];then
    check_type=all
fi
if [[ "$check_type" == all ]];then
    if [[ "$log_type" != "kern.log" ]];then
        #sudo dmesg -c -T|grep -i -E "$regexp"|grep -v "$except_reg"
        sudo dmesg -T|grep -i -E "$regexp"|grep -Ev "$except_reg"
    else
        sudo grep -aiE "$regexp" /var/log/$log_type|grep -Ev "$except_reg"
    fi
else
    if [[ "$log_type" != "kern.log" ]];then
        #sudo dmesg -c -T|grep -i -E "mtgpu|mtsnd|MUSA|PVR"|grep -i -E "$regexp"|grep -v "$except_reg"
        sudo dmesg -T|grep -i -E "mtgpu|mtsnd|MUSA|PVR|mtvpu"|grep -i -E "$regexp"|grep -Ev "$except_reg"
    else
        sudo grep -aiE "$regexp" /var/log/$log_type|grep -Ev "$except_reg"
    fi
fi
