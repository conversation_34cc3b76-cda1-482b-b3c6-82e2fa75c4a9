# SWQA CI

CI pipelines

## Style Guide

[Groovy](https://groovy-lang.org/style-guide.html)
[Shell](https://google.github.io/styleguide/shellguide.html)
[Python](https://google.github.io/styleguide/pyguide.html)

## Pre commit check

os requirements: python, node

install pre-commit: `bash ./scripts/init_pre-commit.sh`

## vscode plugin recommendations

* Groovy Lint, Format and Fix
* Groovy Beautify
* shell-format
* ShellCheck
* Error Lens

## Project structure

jenkinsfile 🪝 related to a jenkins job

lib 💭 [Jenkins shared library](https://sh-code.mthreads.com/sw/swqa-ci)

### filename instruction

triggerd by vcs should be named as 👉 **[repo_name].groovy**

repo specific functional pipeline should be named as 👉 **[action]/[repo_name].groovy**

test pipeline goes to 👉 **test/**

---

## How to define a pipeline

1. put this at beginning:

    ```groovy
    @Library('swqa-ci')
    ```

2. import lib if needed:

    ```groovy
    import org.swqa.tools.git
    import org.swqa.drivers.musa_toolkits
    ```

3. define your workflows:

    ```groovy
    def workflow = [
        'build': [job: 'build(gr-kmd)', parameters: buildParameters, handler: 'job', setGitlabStatus: true],
        'build-no-wait': [job: 'build(gr-kmd)-no-wait', handler: 'job', setGitlabStatus: true, wait: false],
        'test-cts': [job: 'test(cts)', parameters: testCtsParameters, depends: ['build'], handler: 'job', setGitlabStatus: true],
        'test-piglit': [job: 'test(piglit)', parameters: testPiglitParameters, depends: ['build'], 'handler': 'job', setGitlabStatus: true],
    ]
    ```

4. call runPipeline:

    ```groovy
    runPipeline(workflow)
    ```

---

## Job configuration

parameters supported by this framework:

* repo -> ❗**required** for repo CI
* runChoice ->❗**required**, node | pod
* nodeLabel -> required if runChoice is node, will run workflow on this node
* containerImage -> if runChoice is node, running env will be node + container, if runChoice is pod, this is for pod container
* vmImage -> for running jobs on VM
* podNodeSelector -> node selector like: `In=PLATFORM=gpu;In=GPU_CHIP=qy2`
* podResources -> container resources: `requests=cpu=6;requests=memory=20Gi;limits=cpu=10;limits=memory=32Gi;limits=mthreads.com/gpu=1`
* cluster -> for runChoice == pod|vm

if a job will be triggered by gitlab hooks, add an env: gitlabTargetBranch, CI will use this to determine if package path and code pull method
