@Library('swqa-ci')

import org.swqa.tools.git

/*
 * parameters
 * branch (String) - master
 * commitId (String) - null
 * buildcmd (String) -
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * containerImage (String) -
*/

gitLib = new git()

def fetchCode() {
    env.commitId = new git().fetchCode(env.repo, env.branch, env.commitId, [updateBuildDescription: true])
}

def build() {
    dir(env.repo) {
        utils.runCommand(env.clangTidyCmd ?: env.cmd)
    }
}

def upload() {
    dir(env.repo) {
        artifact.upload(env.repo, env.branch, env.commitId, 'build/clang-tidy/clang.html')
        artifact.upload(env.repo, env.branch, env.commitId, 'build/clang-tidy/tidy.log')
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'build': [closure: { build() } ]
    ]
    runPipeline(workflow, [
        post: {
            if (env.clangTidyCmd) {
                upload()
            }
        }
    ])
}
