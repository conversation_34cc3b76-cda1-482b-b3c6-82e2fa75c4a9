@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * parentBranch (String) - master
 * parentCommitId (String) - null
 * ddkBranch (String) - master
 * repo (String) - null
 * branch (String) - develop
 * commitId (String) - null
 * artifacts (String) - null
 * packRepoBranch (String) - vgpu_allinOne
 * cmd (String) - ./ddk_build.sh
 * os (String) - Ubuntu
 * arch (String) - x86_64
 * version (String) - ''
 * submoduleConfig (Multiline String):
    {
        linux-ddk: head,
        mtgpu_snapshot: head,
        mtvgm: head,
        shared_include: head,
        vgpu_daemon: head
    }
 * exports (Multiline String): null
 * buildChoice (Choice) - deb (deb|tar.gz)
 * uploadCase (Choice) - false (false|true)
 * runChoice (Choice) - node
 * nodeLabel (Choice) - Linux_build
 * containerImage (String) - sh-harbor.mthreads.com/qa/linux-ddk:v21
*/

env.parentRepo = 'mt-vgpu'

def isSubmoduleExists(String submodule) {
    return utils.runCommandWithStatus("git config --file .gitmodules --get-regexp path | grep '${submodule}'") == 0
}

def fetchCode() {
    if ([env.gitlabSourceRepoName, env.repo].contains(env.parentRepo)) {
        env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, [disableSubmodules: true, noTags: true, updateBuildDescription: true])
    } else {
        env.parentCommitId = gitLib.fetchCode(env.parentRepo, env.parentBranch, env.parentCommitId, [disableSubmodules: true, noTags: true, updateBuildDescription: true])
        if (env.gitlabSourceRepoName) {
            dir(env.parentRepo) { env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, [submoduleShallow: true, updateBuildDescription: true]) }
        }
    }
    drvCommit = env.commitId ?: env.parentCommitId
    // commitFile = "${env.WORKSPACE}/${drvCommit}_commitInfo.txt"
    // if (env.parentCommitId) { sh "echo '${env.parentRepo}: ${env.parentCommitId}' >> ${commitFile}" }
    Map submoduleConfig = readJSON text: env.submoduleConfig
    credentials.runWithCredential('SSH_GITLAB') {
        dir(env.parentRepo) {
            gitLib.installGitLfs()
            submoduleConfig.each { submodule, commit ->
                if (!isSubmoduleExists(submodule)) {
                    println("No submodule named ${submodule} exists, skip")
                    return
                }
                def newCommit = commonLib.findMrDependency(submodule, (env.gitlabMergeRequestTitle ?: '') + (env.gitlabMergeRequestDescription ?: ''))
                commit = newCommit ?: commit
                if (!commit.contains(constants.mrDependencyDelimiter)) {
                    sh "git submodule update --init --depth 1 ${submodule}"
                }
                dir(submodule) {
                    if (commit.contains(constants.mrDependencyDelimiter)) {
                        def (preCommit, mergeBranch) = commit.split(constants.mrDependencyDelimiter)
                        // Do not use --depth 1 to preserve historical commit records to ensure successful premerge
                        sh """
                            git remote set-branches origin ${preCommit} ${mergeBranch}
                            git fetch origin
                            git checkout ${preCommit}
                            git merge origin/${mergeBranch}
                        """
                    } else if (commit != 'head') {
                        sh """
                            git remote set-branches origin ${commit}
                            git fetch origin --depth 1
                            git checkout ${commit}
                        """
                    }
                    sh '''
                        git clean -dffx
                        git submodule update --init --recursive --depth 1
                    '''
                }
                subCommit = gitLib.getCurrentCommitID(submodule)
                // sh "echo '${submodule}: ${subCommit}' >> '${commitFile}'"
                currentBuild.description += "${submodule} | ${subCommit}<br>"
            }
        }
        sh "rm -rf /home/<USER>/home/<USER>"
    }
}

def build() {
    String envExport = env.exports ? commonLib.parseExportVars(env.exports).join(';') : ''
    credentials.runWithCredential('SSH_GITLAB') {
        apt.installPackage('patchelf')
        sh """
            cd /home/<USER>
            ${envExport}
            ${env.cmd}
            cd ${env.WORKSPACE}
            mv /home/<USER>
        """
    }
    if (env.gitlabActionType == 'PUSH') {
        catchError(buildResult: null, stageResult: null) { utils.uploadDbgSym('mt-vgpu/linux-ddk/build/mt_video', 'oss/debug-symbols/scripts/muti-media/upload_video_dbgsym') }
    }
}

// TO DO:
def packAllInOne() {
    dir(env.parentRepo) {
        gitLib.fetchCode('musa_package', env.packRepoBranch)
        def exportStr = []
        // Load config from file if path is provided and file exists
        if (env.configFilePath && fileExists(env.configFilePath)) {
            println("Loading configuration from ${env.configFilePath}")
            readFile(env.configFilePath).split('\n').each { line ->
                if (line.trim() && !line.startsWith('#') && line.contains('=')) {
                    exportStr.add("export ${line.trim()}")
                }
            }
        } else {
            // Default configuration
            exportStr = [
                "export is_ci_build=${env.isReleasePkg == 'true' ? 'false' : 'true'}",
                "export ddkBranch=${env.ddkBranch ?: 'master'}",
                'export glvnd_enable=Y',
                'export without_xorg=Y',
                'export install_fw=true',
                'export install_ddk_kmd=true',
                'export install_ddk_umd=true',
                'export install_libdrm=true',
                'export install_m3d=true',
                'export install_ddk_video=true',
                'export install_gmi=true',
                'export install_ogl=true',
                'export install_daemon=true'
            ]
        }
        // Handle deviceType - check if it's already in exportStr, otherwise add default
        def hasDeviceType = exportStr.any { it.startsWith('export deviceType=') }
        if (!hasDeviceType) {
            deviceType = env.deviceType ?: 'pc'
            exportStr.add("export deviceType=${deviceType}")
        }
        dir('musa_package') {
            tmp_date = new Date().format('yyyy.MM.dd')
            fullVersion = env.debVersion ?: "${tmp_date}-${env.build_ID}-${env.os}+${drvCommit}"
            suffix = env.packageName.split('\\.')[-1]
            packCmd = "./build_package.sh -t ${env.os} -p ${suffix.toUpperCase()} -a ${env.arch} -v ${env.version ?: fullVersion}"
            sh """
                ${exportStr.join(';')}
                ${packCmd}
                mv musa*.${suffix} ${env.WORKSPACE}/${env.parentRepo}/build/${drvCommit}_${env.packageName}
            """
        }
    }
}

def upload() {
    def artifacts = readJSON(text: env.artifacts)
    artifact.upload(artifacts)
// artifact.upload(env.repo, env.branch, drvCommit, "${drvCommit}_commitInfo.txt")
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'build': [closure: { build() }],
    ]
    if (env.artifacts) {
        if (env.packRepoBranch) {
            workflow['packAllInOne'] = [closure: { packAllInOne() }]
        }
        workflow['upload'] = [closure: { upload() }, setGitlabStatus: true, statusName: env.testLabel]
    }
    runPipeline(workflow, [post: { deleteDir() }])
}
