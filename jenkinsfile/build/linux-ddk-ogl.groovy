@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

commonLib = new common()
gitLib = new git()
oglRepo = 'ogl'
linuxDdkRepo = 'linux-ddk'

/*
 * parameters
 * oglBranch ogl branch - default 'master'
 * oglCommitId ogl oglCommitId - default 'master'
 * linuxDdkBranch linux-ddk branch - default 'mt-ddk-2.0'
 * packagePath - default ''
 * buildcmd build cmd -default './ddk_build.sh -a 0 -o 1'
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * containerImage container image - default 'sh-harbor.mthreads.com/qa/linux-ddk:v4'
*/

def fetchCode() {
    // pull linux-ddk code
    linuxDdkCommitId = gitLib.fetchCode(linuxDdkRepo, env.linuxDdkBranch, null, [disableSubmodules: true])
    // update submodule m3d ogl and shared_inlude
    gitLib.updateSubmodule(linuxDdkRepo, null, 'm3d shared_include')
    // replace ogl dir to linux-ddk/ogl
    dir(linuxDdkRepo) {
        sh "rm -rf ${env.WORKSPACE}/${linuxDdkRepo}/ogl"
        env.oglCommitId = gitLib.fetchCode(oglRepo, env.oglBranch, env.oglCommitId)
    }
}

def compile() {
    dir(linuxDdkRepo) {
        commonLib.runRelyNetwork {
            sh 'apt install ccache -y'
        }
        withCredentials([sshUserPrivateKey(credentialsId: 'sh-code-ssh', keyFileVariable: 'PKEY')]) {
            withEnv(['GIT_SSH_COMMAND=ssh -i $PKEY -o StrictHostKeyChecking=no']) {
                sh "${buildcmd}"
            }
        }
    }
    packageName = "${env.oglCommitId}_linux-ddk_ogl.tar.gz"
    sh "tar -czvf ${packageName} ${linuxDdkRepo}"
}

def upload() {
    if (env.packagePath) {
        oss.install()
        oss.cp("${packageName}", "oss/${env.packagePath}")
    } else {
        artifact.upload(oglRepo, env.oglBranch, env.oglCommitId, "${packageName}")
    }
    utils.catchErrorContinue { deleteDir() }
}

runner.start(env.runChoice) {
    runPipeline([
        'checkout': [closure: { fetchCode() }],
        'compile linux-ddk-ogl': [closure: { compile() }],
        'upload linux-ddk-ogl': [closure: { upload() }]
    ])
}
