@Library('swqa-ci')

import org.swqa.tools.git

/*
 * parameters
 * branch (String) - develop
 * commitId (String) - null
 * packageName (String) - x86_64-mtgpu_linux-xorg-release-hw
 * cmd (String) - ./kmd_build.sh -w xorg -p mtgpu_linux -d 0 -o hw -b release -g deb -j8
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/build-env/gr-umd:v57
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=8;requests=memory=8Gi;limits=cpu=8;limits=memory=16Gi
*/

gitLib = new git()
env.repo = 'mtvgm'

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, [updateBuildDescription: true])
}

def build() {
    dir(env.repo) {
        sh '''
            ./build.sh
            ./pack.sh
        '''
    }
}

def upload() {
    if (env.needUpload == 'true') {
        dir("${env.repo}/release") {
            sh """
                cp mtvgm_0.1.0_amd64.deb ${env.commitId}_mtvgm_amd64.deb
                cp mtvgm_0.1.0_amd64.rpm ${env.commitId}_mtvgm_amd64.rpm
                cp mtvgmi_0.1.0_amd64.deb ${env.commitId}_mtvgmi_amd64.deb
                cp mtvgmi_0.1.0_amd64.rpm ${env.commitId}_mtvgmi_amd64.rpm
                ls -l
            """
            artifact.upload(env.repo, env.branch, env.commitId, "${env.commitId}_mtvgm_amd64.deb")
            artifact.upload(env.repo, env.branch, env.commitId, "${env.commitId}_mtvgm_amd64.rpm")
            artifact.upload(env.repo, env.branch, env.commitId, "${env.commitId}_mtvgmi_amd64.deb")
            artifact.upload(env.repo, env.branch, env.commitId, "${env.commitId}_mtvgmi_amd64.rpm")
        }
    } else {
        log.info('No need to upload.')
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'build': [closure: { build() }],
        'upload': [closure: { upload() }]
    ]

    runPipeline(workflow)
}
