@Library('swqa-ci')

import org.swqa.tools.git

/*
 * parameters
 * branch (String) - master
 * commitId (String) - null
 * cmd (String) -
 * output (String) -
 * runChoice (Choice) - node [node | pod]
 * nodeLabel (Choice) - Linux_build
 * containerImage (String) -
*/

gitLib = new git()

def fetchCode() {
    env.commitId = new git().fetchCode(env.repo, env.branch, env.commitId, [submoduleShallow: false, noTags: true, updateBuildDescription: true])
}

def build() {
    dir(env.repo) {
        sh """
            ${env.cmd}
        """
    }
}

def upload() {
    String distPath = "${env.WORKSPACE}/${env.repo}/${env.output}"
    sh "tar czf ${env.pkgName} -C ${distPath} ."
    artifact.upload(env.repo, env.branch, env.commitId, "${env.pkgName}")
    sh "rm -rf ${env.WORKSPACE}/*"
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'build': [closure: { build() } ],
        'upload': [closure: { upload() } ]
    ]

    runPipeline(workflow)
}
