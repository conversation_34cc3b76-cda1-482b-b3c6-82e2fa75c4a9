@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

// 只保留你需要的参数
// branch commitId exports runChoice nodeLabel cluster containerImage podNodeSelector podResources pkgName packagePath cmd triggerInfo

gitLib = new git()
commonLib = new common()

env.repo = 'MUSA-Runtime'

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    packageName = env.packagePath ? env.packageName : "${env.gitlabMergeRequestLastCommit ? env.commitId : ''}_${env.packageName}"
}

def build() {
    String envExport = env.exports ? 'export ' + env.exports.split().join(' && export ') : ''
    dir(env.repo) {
        sh """
            ${envExport}
            ${env.cmd}
        """
    }
}

def upload() {
    sh "tar -czvf ${packageName} ${env.repo}/build/ ${env.repo}/install.sh"
    if (env.packagePath) {
        artifact.upload("${packageName}", env.packagePath)
    } else {
        artifact.upload(env.repo, env.branch, env.commitId, "${packageName}")
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'fetchCode': [closure: { fetchCode() }],
        'build': [closure: { build() }],
    ]
    if (env.packageName) {
        workflow['upload'] = [closure: { upload() }]
    }
    runPipeline(workflow)
}
