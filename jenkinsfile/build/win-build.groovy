@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

def fetchCode() {
    env.commitId = new git().fetchCode(env.repo, env.branch, env.commitId, [updateBuildDescription: true])
}

def build() {
    dir("${env.buildPath}") {
        bat "${env.cmd}"
    }
}

def upload() {
    if (env.needUpload == 'true') {
        def pkgName = "${env.commitId}_${env.packageName}"
        bat "tar -cvzf ${pkgName} -C ${env.distPath} *"
        artifact.upload(env.repo, env.branch, env.commitId, "${pkgName}")
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }]
    ]
    def buildClosure = { build() }
    workflow['build'] = [closure: buildClosure, setGitlabStatus: true, statusName: env.testLabel]
    workflow['upload'] = [closure: { upload() }]

    runPipeline(workflow)
}
