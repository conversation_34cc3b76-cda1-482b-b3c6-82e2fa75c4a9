@Library('swqa-ci')

import org.swqa.tools.git

/*
 * parameters
 * repo (String) - FFmpeg
 * branch (String) - master
 * commitId (String) - null
 * cmd (String) - mkdir build && ./ffmpeg_docker_build.sh
 * distPath (String) - mt_video/mt_video_dist
 * packageNames (Multiline String)
    'ffmpeg_arm64.deb'
    'ffmpeg_x86_64.deb'
    'ffmpeg_loongarch64.deb'
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (String) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/build-env/gr-umd:v60
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=8;requests=memory=8Gi;limits=cpu=8;limits=memory=16Gi
*/

gitLib = new git()

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, [updateBuildDescription: true])
}

def build() {
    credentials.runWithCredential('SSH_GITLAB') {
        dir(env.repo) {
            utils.runCommand(env.cmd)
        }
    }
}

def upload() {
    dir(env.distPath) {
        env.packageNames.split().each { packageName ->
            sh "mv ${packageName} ${env.commitId}_${packageName}"
            artifact.upload(env.repo, env.branch, env.commitId, "${env.commitId}_${packageName}")
        }
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'build': [closure: { build() }, setGitlabStatus: true, statusName: env.testLabel],
        'upload': [closure: { upload() }]
    ]

    runPipeline(workflow)
}
