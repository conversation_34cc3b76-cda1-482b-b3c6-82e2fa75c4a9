@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

env.repo = 'mtgpu_snapshot'

// runChoice cmd uploadPackage cluster podNodeSelector podResources containerImage triggerInfo

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
}

def compile() {
    dir(env.repo) {
        sh "${env.cmd}"
    }
}

def upload() {
    dir("${env.repo}/output") {
        sh """
            mv *mtgpu_snapshot*.rpm ${env.commitId}_mtgpu_snapshot.rpm
            mv *mtgpu_snapshot*.deb ${env.commitId}_mtgpu_snapshot.deb
        """
        artifact.upload(env.repo, env.branch, env.commitId, "${env.commitId}_mtgpu_snapshot.rpm")
        artifact.upload(env.repo, env.branch, env.commitId, "${env.commitId}_mtgpu_snapshot.deb")
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'build': [closure: { compile() }]
    ]
    if (env.uploadPackage.toBoolean()) { workflow['upload'] = [closure: { upload() }] }
    runPipeline(workflow)
}
