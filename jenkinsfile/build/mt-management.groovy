@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

// mtmlBranch(develop) repo branch commitId mtmlPackageName isX86 cmd clangTidyCmd

env.repo = 'mt-management'
repoPath = env.mtmlPackageName ? "${env.mtmlPackageName}/${env.repo}" : "buildDir/${env.repo}"

def fetchCode() {
    dir(env.mtmlPackageName ?: 'buildDir') {
        if (env.gitlabSourceRepoName == 'mt-management') {
            gitLib.fetchCode(env.repo, env.branch, env.commitId)
        } else {
            gitLib.fetchCode(env.repo, env.mtmlBranch)
        }
    }
}

def build() {
    credentials.runWithCredential('SSH_GITLAB') {
        sh 'mkdir sdk_build_pkg'
        dir(repoPath) {
            def version = utils.runCommandWithStdout("egrep -A1 'mtml_version' config.ini | grep 'version = ' | awk -F '=' '{print \$2}'")
            if (gitLib.triggeredByMR()) {
                def buildCmd = env.clangTidyCmd ?: "${env.cmd} ${version} ${env.isX86}"
                timeout(time: env.timeout ?: 15, unit: 'MINUTES') {
                    sh """
                        ${buildCmd}
                        sleep 3
                    """
                }
            } else {
                def date = env.date ?: new Date().format('yyyy.MM.dd')
                version = env.gitlabTargetBranch == 'develop' ? date : version
                def architectures = ['sw64', 'loongarch64', 'i386', 'aarch64', 'arm32', 'x86_64']
                def buildTypes = ['RELEASE', 'DEBUG']
                architectures.each { arch ->
                    buildTypes.each { type ->
                        sh """
                            set -e
                            ./build_ci.sh ${type} ${arch} ${version} NO
                            mv sdk ${env.WORKSPACE}/sdk_build_pkg/${type}_${arch}_sdk
                        """
                    }
                }
                sh "cp -rf install.sh ${env.WORKSPACE}/sdk_build_pkg/"
            }
        }
    }
}

def upload() {
    if (env.clangTidyCmd) {
        dir(repoPath) {
            artifact.upload(env.repo, env.branch, env.commitId, 'build/clang-tidy/clang.html')
            artifact.upload(env.repo, env.branch, env.commitId, 'build/clang-tidy/tidy.log')
        }
    } else if (env.isX86 == 'YES') {
        dir(repoPath) {
            def fullPackageName = "${constants.formatCommitID(env.commitId)}_${env.mtmlPackageName}.tar.gz"
            sh "tar czf ${fullPackageName} build sdk"
            artifact.upload(env.gitlabSourceRepoName ?: env.repo, env.branch, env.commitId, fullPackageName)
        }
    } else if (!gitLib.triggeredByMR()) {
        def fullPackageName = "${env.mtmlPackageName}_${constants.formatCommitID(env.commitId)}.tar.gz"
        sh "cd ${env.WORKSPACE}; tar czf ${fullPackageName} sdk_build_pkg"
        def packagePath = env.packagePath ?: "release-ci/management/${env.gitlabTargetBranch}"
        def packageUrl = "https://oss.mthreads.com/${packagePath}/${fullPackageName}"
        artifact.upload(fullPackageName, packagePath)
        sh """
            echo ${packageUrl} > latest.txt
            mc cp latest.txt oss/${packagePath}
        """
    }
    utils.catchErrorContinue { deleteDir() }
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'build': [closure: { build() } ],
        'upload': [closure: { upload() }]
    ]

    runPipeline(workflow)
}
