@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

env.repo = 'mt-dcgm'

def fetchCode() {
    gitLib.fetchCode(env.repo, env.branch, env.commitId)
}

def updateDockerimage() {
    sh """
        docker rmi dcgmbuild ||:
        docker rmi dcgmbuild-x86_64 ||:
        docker pull ${env.buildImage}
        docker pull ${env.relyImage}
        docker tag ${env.buildImage} dcgmbuild-x86_64:latest
        docker tag ${env.relyImage} dcgmbuild:base-gcc11-b3ae23-x86_64
        rm -rf /workspace ||: && mkdir -p /workspace
    """
}

def buildDcgm() {
    fetchCode()
    credentials.runWithCredential('SSH_GITLAB') {
        sh "cp -rf ${env.repo} /workspace; cd /workspace/${env.repo}; ${env.buildCmd}"
    }
}

runner.start(env.runChoice, {
    runPipeline([
        'update dockerImage': [ closure: { updateDockerimage() } ],
        'build': [ closure: { buildDcgm() }, setGitlabStatus: true, statusName: "${env.testLabel}" ]
    ])
})
