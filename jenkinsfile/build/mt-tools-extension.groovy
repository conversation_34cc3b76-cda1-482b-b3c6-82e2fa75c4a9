@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
}

def build() {
    String envExport = env.exports ? 'export ' + env.exports.split().join(' && export ') : ''
    credentials.runWithCredential('SSH_GITLAB') {
        dir(env.repo) {
            sh """
                ${envExport}
                ${env.cmd}
            """
            if (env.coverageReportPath) {
                commonLib.publishHTML(env.coverageReportPath, '*.html', 'Coverage Report')
            }
        }
    }
}

def upload() {
    String distPath = "${env.WORKSPACE}/${env.repo}/${env.output}"
    //pkgName = env.pkgName ? "${env.pkgName}_${env.relVersion}" : "${env.commitId}_${env.pkgName}_${env.relVersion}"
    pkgName = "${env.commitId}_${env.pkgName}_${env.relVersion}"
    sh "tar czf ${pkgName}.tar.gz -C ${distPath} ."

    if (env.packagePath) {
        artifact.upload("${pkgName}.tar.gz", env.packagePath)
    } else {
        artifact.upload(env.repo, env.branch, env.commitId, "${pkgName}.tar.gz")
    }
    //utils.catchErrorContinue { deleteDir() }
    sh "rm -rf ${env.WORKSPACE}/*"
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'build': [closure: { build() }]
    ]
    if (!env.coverageReportPath && env.pkgName) {
        workflow['upload'] = [closure: { upload() }, setGitlabStatus: true, statusName: "${env.testLabel}"]
    }

    runPipeline(workflow)
}
