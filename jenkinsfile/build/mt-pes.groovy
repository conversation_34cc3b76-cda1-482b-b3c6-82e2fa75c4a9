@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

env.repo = 'mt-pes'

// runChoice cluster podNodeSelector podResources containerImage branch commitId triggerInfo
def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
}

def build() {
    dir("${env.repo}/Coding/script") {
        sh """
            ${env.buildScript}
        """
    }
}

def outBag() {
    dir("${env.repo}/Coding/script") {
        sh """
            ${env.outBagScript}
        """
    }
}

def upload() {
    dir("${env.repo}/release") {
        artifact.upload(env.repo, env.gitlabTargetBranch, env.commitId, env.packageName)
    }
}

runner.start(env.runChoice) {
    def workFlow = [
        'checkout': [closure: { fetchCode() }],
        'build': [closure: { build() }, setGitlabStatus: true, statusName: "${env.testLabel}"]
    ]
    if (!env.buildScript =~ 'clang-tidy') {
        workFlow['outBag'] = [closure: { outBag() }, setGitlabStatus: true, statusName: "${env.testLabel}"]
        workFlow['upload'] = [closure: { upload() }]
    }
    runPipeline(workFlow)
}
