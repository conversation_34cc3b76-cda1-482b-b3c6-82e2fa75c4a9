@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

/*
 * parameters
 * repo (String) - mt-media-driver
 * branch (String) - develop
 * commitId (String) - null
 * umdPackageUrls (String) - null
 * cmd (String) - ./mm_build.sh -a all -v all
 * packageName (String) - video_ci
 * packagePath (String) - null
 * testLabel (String) - jenkins/build
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (String) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/build-env/gr-umd:v60
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=8;requests=memory=8Gi;limits=cpu=8;limits=memory=16Gi
 * triggerInfo
*/

commonLib = new common()
gitLib = new git()
env.mediaRepo = 'mt-media-driver'

def fetchCode() {
    //env.mediaBranch is for submodule MR with multi-repository dependencies
    env.mediaBranch = commonLib.findMrDependency(env.mediaRepo, (env.gitlabMergeRequestTitle ?: '') + (env.gitlabMergeRequestDescription ?: '')) ?: env.mediaBranch
    if (env.gitlabSourceRepoName == 'mt-media-driver') {
        env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, [updateBuildDescription: true])
    } else {
        gitLib.fetchCode(env.mediaRepo, env.mediaBranch, env.mediaCommit, [updateBuildDescription: true])
        if (env.gitlabSourceRepoName) {
            sh "rm -rf ${env.gitlabSourceRepoName}"
            dir(env.mediaRepo) { env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, [updateBuildDescription: true]) }
        }
    }
}

def build() {
    for (umdUrl in env.umdPackageUrls.split(',')) {
        constants.downloadAndUnzipPackage(umdUrl, 'gr_umd_dist')
    }
    credentials.runWithCredential('SSH_GITLAB') {
        def fwCommit = utils.runCommandWithStdout('cd mt-media-driver/mt_video_drv/firmware; git rev-parse HEAD')
        currentBuild.description += "firmwareCommit: ${fwCommit[0..8]} <br>"
        dir(env.mediaRepo) {
            sh "${env.cmd}"
        }
    }
}

def upload() {
    dir(env.mediaRepo) {
        def packageName = "${env.commitId}_${env.packageName}.tar.gz"
        sh """
            cd mt_video_release
            mv mt_video_dist mt_video
            tar czf ${packageName} mt_video
            cp ${packageName} ../
        """
        if (gitLib.triggeredByMR()) {
            artifact.upload(env.repo, env.branch, env.commitId, packageName)
        } else {
            def packagePath = env.packagePath ?: "release-ci/video/${env.gitlabTargetBranch}"
            def packageUrl = "https://oss.mthreads.com/${packagePath}/${packageName}"
            artifact.upload(packageName, packagePath)
            sh """
                echo ${packageUrl} > latest.txt
                mc cp latest.txt oss/${packagePath}
            """
        }
    }
}

runner.start(env.runChoice) {
    def workFlow = [
        'checkout': [closure: { fetchCode() }],
        'build': [closure: { build() }, setGitlabStatus: true, statusName: env.testLabel]
    ]
    if (env.packageName) {
        workFlow['upload'] = [closure: { upload() }]
    }
    runPipeline(workFlow)
}
