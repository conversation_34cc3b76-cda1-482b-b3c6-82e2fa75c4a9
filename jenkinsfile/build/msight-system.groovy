@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
}

def build() {
    credentials.runWithCredential('SSH_GITLAB') {
        dir(env.repo) {
            sh """
                ${env.cmd}
            """
            if (env.coverageReportPath) {
                commonLib.publishHTML(env.coverageReportPath, '*.html', 'Coverage Report')
            }
        }
    }
}

def upload() {
    String distPath = "${env.WORKSPACE}/${env.repo}/${env.output}"

    if (fileExists(distPath)) {
        artifact.upload(env.repo, env.branch, env.commitId, "${distPath}/*")
    } else {
        echo "The directory ${distPath} does not exist, skipping upload steps."
    }

    sh "rm -rf ${env.WORKSPACE}/*"
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'build': [closure: { build() }],
        'upload': [closure: { upload() }, setGitlabStatus: true, statusName: "${env.testLabel}"]
    ]

    runPipeline(workflow)
}
