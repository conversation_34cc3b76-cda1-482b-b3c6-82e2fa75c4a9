@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

/*
 * parameters
 * branch (String) - master
 * buildBranch (Choice) - master [master | release_KUAE_2.0_for_PH1_M3D]
 * commitId (String) - ''
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (String) - Linux_build
 * cluster (String) - shfarm
 * exports (Multiline String) - LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu/musa:/usr/local/musa/lib:\${LD_LIBRARY_PATH};
 * linuxDdkPackageUrl (String) - https://oss.mthreads.com/sw-build/linux-ddk/release_KUAE_2.0_for_PH1_M3D/f0a99fc9e/xxx.deb
 * packageName (String) - MUPTI
 * compileParallel (String) - 16
 * compileArgs (String) - -DCMAKE_BUILD_TYPE=Release
 * compileTargetArch (Choice) - x86 [x86 | arm]
 * packagePath (String) - ''
 * triggerInfo (String) - ''
 * containerImage (String) - sh-harbor.mthreads.com/qa/musa_compile:v1
 * podNodeSelector (String) - In=mt=buildserver
 * podResources (String) - requests=cpu=20;requests=memory=64Gi;limits=cpu=20;limits=memory=64Gi
*/

gitLib = new git()
commonLib = new common()

env.repo = 'MUPTI'

List envs = env.exports ? env.exports.split('\n') : []
envExport = envs ? 'export ' + envs.join(' && export ') : ''

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    packageName = "${env.commitId}_${env.packageName}"
}

def deploy() {
    sh '''
        apt update ||:
        apt install dkms -y ||:
    '''
    if (env.linuxDdkPackageUrl.startsWith('oss')) {
        linuxDdkPackageUrl = env.linuxDdkPackageUrl.replaceFirst('oss', 'https://oss.mthreads.com')
    }
    ddk.installLinuxDdk(linuxDdkPackageUrl)

    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(constants.ossPathToUrl(env.musaToolkitsPackageUrl))
    }
    else {
        if (env.musaRuntimePackageUrl) {
            musa.installMusaRuntime(env.musaRuntimePackageUrl)
        }
        else { error('no musa-runtime pkg!') }
    }

    if (env.usingLatestMtperf == 'true') {
        latest_mt_perf = sh(returnStdout: true, script: 'wget -qO- http://oss.mthreads.com/release-ci/mt-perf/develop/latest.txt --no-check-certificate').trim()
        def branchInfo = env.gitlabTargetBranch ?: env.buildBranch
        if (branchInfo == 'master' && env.compileTargetArch == 'x86') {
            latest_mt_perf += '_linux_x86_64.tar.gz'
        } else if (branchInfo == 'master' && env.compileTargetArch == 'arm') {
            latest_mt_perf += '_linux_aarch64.tar.gz'
        } else if (branchInfo == 'release_KUAE_2.0_for_PH1_M3D' && env.compileTargetArch == 'x86') {
            latest_mt_perf += '_linux_x86_64_kuae.tar.gz'
        } else if (branchInfo == 'release_KUAE_2.0_for_PH1_M3D' && env.compileTargetArch == 'arm') {
            latest_mt_perf += '_linux_aarch64_kuae.tar.gz'
        } else {
            print('failed get mt-perf')
        }
        print(latest_mt_perf)
        sh "wget ${latest_mt_perf} --no-check-certificate"
        sh 'tar -xvf ./*mt-perf*.tar.gz'
        sh 'mkdir /usr/local/mt-perf'
        sh 'cp -r ./*mt-perf*/* /usr/local/mt-perf'

        compileArgs = env.compileArgs +  ' -DMT_PERF_PATH=/usr/local/mt-perf/'
    }
}

def compile() {
    dir(env.repo) {
        credentials.runWithCredential('SSH_GITLAB') {
                sh """
                    ${envExport}
                    mkdir build
                    cd build
                    cmake .. ${compileArgs}
                    make package -j16
                """
        }

        sh "cd build && mv ./MUPTI*.deb ${packageName}"
        sh "mv ./build/${packageName} ${env.WORKSPACE}/"
    }
}

def upload() {
    if (env.packagePath) {
        artifact.upload(packageName, env.packagePath)
    } else {
        artifact.upload(env.repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, packageName)
    }
    utils.catchErrorContinue { deleteDir() }
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'deploy': [closure: { deploy() }],
        'build': [closure: { compile() }],
        'upload-package': [closure: { upload() }],
    ]

    runPipeline(workflow)
}
