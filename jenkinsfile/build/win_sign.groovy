@Library('swqa-ci')

//author: liya
//email: <EMAIL>

def downloadAndStashDriver(String downloadUrl, String dirName = 'wddm_driver') {
    def pkgName = downloadUrl.split('/')[-1]
    dir(dirName) {
        sh """
            wget -q ${downloadUrl} -O ${pkgName} --no-check-certificate
            tar xvzf ${pkgName}
            rm -f ${pkgName}
        """
    }
}

def generateDdfFile(String driverDirPath, String driver_url, String ddfFileName = 'mtgpu.ddf', String cabName = 'mtgpu.cab', String destDir = 'mtgpu') {
    def driverDir = "${env.WORKSPACE}\\${driverDirPath}"
    def ddfFilePath = "${env.WORKSPACE}\\${ddfFileName}"

    def ddfHeader = """
    ;*** Generated mtgpu.ddf ***
    .OPTION EXPLICIT
    .Set CabinetFileCountThreshold=0
    .Set FolderFileCountThreshold=0
    .Set FolderSizeThreshold=0
    .Set MaxCabinetSize=0
    .Set MaxDiskFileCount=0
    .Set MaxDiskSize=0
    .Set CompressionType=MSZIP
    .Set Cabinet=on
    .Set Compress=on
    .Set CabinetNameTemplate=${cabName}
    .Set DestinationDir=${destDir}
    ;Specify files to be included in cab file
    """.stripIndent()

    writeFile(file: ddfFilePath, text: ddfHeader)

    def batScript = """
        @echo off
        set DRIVER_DIR=${driverDir}
        set DDF_FILE=${ddfFilePath}

        echo DRIVER_DIR=%DRIVER_DIR%
        echo DDF_FILE=%DDF_FILE%

        for /r "%DRIVER_DIR%" %%F in (*) do (
            echo %%~dpF | findstr /i "symbols" >nul
            if errorlevel 1 (
                echo %%~nxF >> "%DDF_FILE%"
            )
        )

        echo DDF file has been updated successfully: %DDF_FILE%
    """

    if (driver_url.contains('release')) {
        batScript = """
            @echo off
            set DRIVER_DIR=${driverDir}
            set DDF_FILE=${ddfFilePath}

            echo DRIVER_DIR=%DRIVER_DIR%
            echo DDF_FILE=%DDF_FILE%

            for /r "%DRIVER_DIR%" %%F in (*) do (
                rem Exclude paths containing "symbols" directory
                echo %%~dpF | findstr /i "symbols" >nul
                if errorlevel 1 (
                    rem Check if the file has an excluded extension (.win, .dict, .bin)
                    echo %%~xF | findstr /i ".win" >nul
                    if errorlevel 1 (
                        echo %%~xF | findstr /i ".dict" >nul
                        if errorlevel 1 (
                            echo %%~xF | findstr /i ".bin" >nul
                            if errorlevel 1 (
                                rem Write the file name to the DDF file
                                echo %%~nxF >> "%DDF_FILE%"
                            )
                        )
                    )
                )
            )

            echo DDF file has been updated successfully: %DDF_FILE%
        """
    }

    def result = bat(script: batScript, returnStdout: true)

    println result
    println "DDF file has been generated successfully at: ${ddfFilePath}"
}

def sign() {
    node(env.iTrusSign_Nodes) {
        deleteDir()
        def ossBranchPath = constants.genOssPath(env.gitlabSourceRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit)
        def commitIdForPkg = ossBranchPath.tokenize('/')[-1]
        def prefix = constants.genOssPrefix(ossBranchPath)
        def ossAlias = constants.genOssAlias(ossBranchPath)
        def useDriverUrl = env.driver_url ?: "${prefix}/${ossBranchPath}/${commitIdForPkg}_mt-vgpu_win_vdi.tar.gz"
        def useSignedDriverUrl = env.driver_url ?: "${prefix}/${ossBranchPath}/${commitIdForPkg}_mt-vgpu_win_vdi_Signed.zip"
        def ossSavePath = env.oss_save_path ?: "${ossBranchPath}"
        downloadAndStashDriver(useDriverUrl)
        generateDdfFile('wddm_driver', useDriverUrl)
        def pkgName = useDriverUrl.tokenize('/').last().replace('.tar.gz', '')

        def signtoolSha1 = env.NODE_NAME.contains('114.121') ? constants.signtoolSha1ChengDu : constants.signtoolSha1

        dir('wddm_driver') {
            stage('make cab') {
                def ddfFilename = 'mtgpu.ddf'

                bat"""
                    signtool sign /fd sha256 /sha1 ${signtoolSha1} /t http://timestamp.globalsign.com/?signature=sha2 ${WORKSPACE}\\wddm_driver\\*.dll
                    signtool sign /fd sha256 /sha1 ${signtoolSha1} /t http://timestamp.globalsign.com/?signature=sha2 ${WORKSPACE}\\wddm_driver\\*.sys
                    signtool sign /fd sha256 /sha1 ${signtoolSha1} /t http://timestamp.globalsign.com/?signature=sha2 ${WORKSPACE}\\wddm_driver\\*.cat
                    makecab /f ${env.WORKSPACE}\\${ddfFilename}
                """
            }

            stage('sign cab') {
                bat """
                    signtool sign /fd sha256 /sha1 ${signtoolSha1} /t http://timestamp.globalsign.com/?signature=sha2 ${WORKSPACE}\\wddm_driver\\disk1\\mtgpu.cab
                """
            }

            retry(3) {
                stage('sign driver') {
                    bat """
                        chcp 65001
                        set LANG=en_US.UTF-8
                        REM Clear Chrome cache before signing
                        taskkill /f /im chrome.exe /t 2>nul || echo "Chrome not running"
                        REM Clear Chrome cache
                        rd /s /q "%LOCALAPPDATA%\\Google\\Chrome\\User Data\\Default\\Cache" 2>nul || echo "Chrome cache cleared"
                        rd /s /q "%LOCALAPPDATA%\\Google\\Chrome\\User Data\\Default\\Code Cache" 2>nul || echo "Chrome code cache cleared"
                        sleep 2
                        python C:\\tools\\wddm_signature\\wddmdriver_signature.py ${pkgName} ${WORKSPACE}\\wddm_driver\\disk1\\mtgpu.cab ${env.signatureWaitingStatus}
                        sleep 2
                    """
                }

                if (env.signatureWaitingStatus == 'yes') {
                    stage('upload') {
                        oss.setUp()
                        def sign_driver_map = readJSON file: 'C:\\tools\\wddm_signature\\wddmdriver.json' , returnPojo: true
                        def sign_driver_id = sign_driver_map["${pkgName}"]
                        bat """
                        chcp 65001
                        set LANG=en_US.UTF-8
                        cd /D "%USERPROFILE%\\Downloads
                        move /y Signed_${sign_driver_id}.zip ${pkgName}_Signed.zip
                        mc cp ${pkgName}_Signed.zip ${ossAlias}/${ossSavePath}/
                    """
                    }

                    runPipeline.runJob([
                        job: 'pes_build_tool',
                        wait:false,
                        parameters: [
                            wddm_driver_url: useSignedDriverUrl,
                            wddm_branch: env.gitlabSourceBranch,
                            pes_branch: env.pesBranch,
                            oss_save_path: ossSavePath,
                            pes_version: env.pesVersion,
                            install_ver: env.installVersion,
                            driver_ver: env.driverVersion,
                            package_platform_type: env.packagePlatformType,
                            buildAllinone: 'false'
                        ]
                    ])
                }
            }
        }
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'sign': [closure: { sign() }]
    ]

    runPipeline(workflow, [disablePost:true])
}
