@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

/*
 * parameters
 * repo (String) - gr-kmd
 * branch (String) - vgpu_2.6.5_release
 * commitId (String) - null
 * packRepoBranch (String) - pack_rpm_by_url
 * exports (Multiline String):
    {
        "glvnd_enable" : "Y",
        "without_xorg" : "Y",
    }
 * artifactConfig (Multiline String):
    {
        "kmd": {prefix: "https://oss.mthreads.com/release-ci/VDI/XC-VDI/${branch}/repoPackages/guest_gr-kmd/", commit: "${latestKmdCommit}", packageName: "mtgpu-2.6.5.amd64.deb"},
        "umd": {prefix: "https://oss.mthreads.com/release-ci/VDI/XC-VDI/${branch}/repoPackages/guest_gr-umd/", commit: "${latestUmdCommit}", packageName: "x86_64-mtgpu_linux-xorg-release-umd.tar.gz"},
        "video": {prefix: "https://oss.mthreads.com/release-ci/VDI/XC-VDI/${branch}/repoPackages/guest_mt-media-driver/", commit: "${latestMediaCommit}", packageName: "video_release_vgpu.tar.gz"},
        "directstream": {prefix: "https://oss.mthreads.com/release-ci/DirectStream/${branch}/linux/", commit: "", packageName: "directstream.tar.gz"},
        "umd_path_wine_separate": {prefix: "https://oss.mthreads.com/release-ci/VDI/XC-VDI/${branch}/repoPackages/guest_gr-umd_i386/", commit: "${latestUmdCommit}", packageName: "i386-mtgpu_linux-xorg-release-umd.tar.gz"},
        "host": {prefix: "https://oss.mthreads.com/release-ci/VDI/XC-VDI/${branch}/repoPackages/host_gr-kmd_deb/", commit: "", packageName: "mtgpu-2.6.5.amd64.deb"},
        "guest": {prefix: "https://oss.mthreads.com/release-ci/VDI/XC-VDI/${branch}/drivers/", commit: "", packageName: "musa_2.6.5-release-uos_amd64.deb"},
        "mtvgm": {prefix: "https://oss.mthreads.com/sw-build/mtvgm/master/", commit: "", packageName: "mtvgm.deb"},
        "snapshot": {prefix: "https://oss.mthreads.com/sw-build/mtgpu_snapshot/master/", commit: "", packageName: "mtgpu_snapshot.deb"},
        "vgpu_daemon": {prefix: "https://oss.mthreads.com/release-ci/vgpu_daemon/${branch}/", pkgFilter: "*_vgpu_daemon_linux_amd64_Release.tar.gz"},
        "gmi": {prefix: "https://oss.mthreads.com/release-ci/gmi/release_1.12/", pkgFilter: "*_mthreads-gmi_1.12.2*.tar.gz"}
    }
 * packCmd (String) - ./build_package.sh -t uos -v 2.6.5-release-uos -p DEB -s false -b false -a x86_64
 * packageName (String) - musa_2.6.5-release-uos_amd64.deb
 * ossPkgPath (String) - release-ci/VDI/XC-VDI/${branch}/drivers/
 * updateLatest (Choice) - true
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/build-env/gr-umd:v53
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=8;requests=memory=8Gi;limits=cpu=8;limits=memory=16Gi
 * triggerInfo
*/

gitLib = new git()
commonLib = new common()
commitInfos = [:]
env.commitId = constants.formatCommitID(env.commitId)
exports = env.exports?.trim() ? commonLib.parseExportVars(env.exports) : []

def collectArtifactInfo() {
    Map artifactConfig = readJSON text: env.artifactConfig
    artifactConfig.each { artifact, info ->
        boolean isPesPush = (!gitLib.triggeredByMR() && artifact == 'pes')
        def pkgUrl = ''
        def exportName = artifact + '_pkg_url'
        if (info?.commit) {
            pkgUrl = info.prefix + info.commit + '_' + info.packageName
        } else if (info?.pkgFilter) {
            def ossPath = info.prefix.replace('https://oss.mthreads.com', 'oss')
            def packageName = commonLib.findLatestOssPkg(ossPath, info.pkgFilter)
            pkgUrl = info.prefix + packageName
        } else {
            def latestUrl = info.prefix + 'latest.txt'
            def latestContent = utils.runCommandWithStdout("curl --insecure ${latestUrl}").with {
                it.endsWith('_') ? it[0..-2] : it
            }
            pkgUrl = ['.deb', '.rpm', '.tar.gz', '.ko'].any {
                latestContent.endsWith(it)
            } ? latestContent : "${latestContent}_${info.packageName}"
        }
        if (isPesPush) {
            pkgUrl = constants.waitPackageUrl(pkgUrl, env.pesWaitTime ? env.pesWaitTime.toInteger() : 240)
        } else if (artifact == 'pes') {
            return
        }
        exports << "export ${exportName}=${pkgUrl}"
        def matcher = pkgUrl =~ /repoPackages\/([^\/]+)/
        def targetName = matcher?.find() ? matcher[0][1] : artifact
        commitInfos[targetName] = pkgUrl
    }
}

def packAllInOne() {
    String fullPkgName = "${env.commitId}_${env.packageName}"
    gitLib.fetchCode('musa_package', env.packRepoBranch ?: 'pack_rpm_by_url')
    dir('musa_package') {
        if (['.deb', '.rpm'].any {
            packageName.endsWith(it)
        }) {
            sh """
                ${exports.join(';')}
                ${env.packCmd}
                find . -type f -regextype posix-extended -regex '.*/(musa|mtvgpu).*(arm64|amd64)\\.(deb|rpm)\$' -exec mv {} ${fullPkgName} \\;
            """
        } else {
            def relyPackages = commitInfos.values().toList()
            sh """
                wget -q --no-check-certificate ${relyPackages.join(' ')}
                ls *.tar.gz | xargs -n1 tar xzvf

                mkdir -p xc-kmd_mtgpu_Ubuntu_release; mkdir -p xc-kmd_mtgpu_Ubuntu_release/firmware
                cp -rf mt_video/firmware/mtvpu*.bin xc-kmd_mtgpu_Ubuntu_release/firmware
                cp -rf *mtgpu_linux*/lib/firmware/mthreads/musa.fw.*.vz.linux xc-kmd_mtgpu_Ubuntu_release/firmware
                cp -rf *mtgpu_linux*/lib/firmware/mthreads/musa.sh.1.0.0.0 xc-kmd_mtgpu_Ubuntu_release/firmware

                cp -rf *mtgpu.ko xc-kmd_mtgpu_Ubuntu_release/mtgpu.ko
                tar cvzf ${fullPkgName} xc-kmd_mtgpu_Ubuntu_release
            """
        }
        commitInfos.each { targetName, pkgUrl ->
            sh "echo '${targetName}: ${pkgUrl}' >> ${commitFile}"
        }
    }
}

def upload() {
    env.ossPkgPath = env.ossPkgPath ?: "release-ci/VDI/XC-VDI/${env.branch}/drivers/"
    String fullPkgName = "${env.commitId}_${env.packageName}"
    dir('musa_package') {
        artifact.upload(fullPkgName, env.ossPkgPath)
        sh "cat ${commitFile}"
    // artifact.upload(commitFile, env.ossPkgPath)
    }
    if (env.updateLatest.toBoolean()) {
        sh """
            echo https://oss.mthreads.com/${env.ossPkgPath + env.commitId}_ > latest.txt
            mc cp latest.txt oss/${env.ossPkgPath}
        """
    }
}

runner.start(env.runChoice) {
    commitFile = "${env.WORKSPACE}/${env.commitId}_commitInfo.txt"
    def workflow = [
        'collectArtifactInfo': [closure: { collectArtifactInfo() }],
        'packAllInOne': [closure: { packAllInOne() }],
        'upload': [closure: { upload() }],
    ]

    runPipeline(workflow)
}
