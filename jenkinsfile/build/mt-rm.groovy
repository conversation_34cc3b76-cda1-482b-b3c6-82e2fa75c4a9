@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

/*
 * parameters
 * branch (String) - master
 * commitId (String) - null
 * rmInKmd (Boolean) - default false
 * kmdBranch (String) - develop
 * cmd (String) - ./kmd_build.sh -r 1.0.0.0 -p mtgpu_linux -w xorg -o hw -j32 -b release -d 0 -g deb RM_ENABLE=1
 * exports (Multiline String)
    ''
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/build-env/gr-umd:v45
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=8;requests=memory=8Gi;limits=cpu=8;limits=memory=16Gi
*/

commonLib = new common()
gitLib = new git()
env.repo = 'mt-rm'

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, [updateBuildDescription: true])
    if (env.rmInKmd.toBoolean()) {
        env.kmdBranch = commonLib.findMrDependency('gr-kmd', (env.gitlabMergeRequestTitle ?: '') + (env.gitlabMergeRequestDescription ?: '')) ?: env.kmdBranch
        def submoduleShallow = !(env.ddkCommitId && env.updateShareInclude == 'true')
        env.kmdCommitId = gitLib.fetchCode('gr-kmd', env.kmdBranch ?: 'develop', null, [updateBuildDescription: true, submoduleShallow: submoduleShallow])
        if (env.ddkCommitId && env.updateShareInclude == 'true') {
            def shared_include_commitid = ''
            credentials.runWithCredential('SSH_GITLAB') {
                gitLib.fetchCode('linux-ddk', 'master', env.ddkCommitId, [disableSubmodules: true])
                dir('linux-ddk') {
                    shared_include_commitid = sh(script: "git ls-tree ${env.ddkCommitId} shared_include | awk '{print \$3}'", returnStdout: true).trim()
                    println "shared_include_commitid: ${shared_include_commitid}"
                }
                sh """
                    cd gr-kmd/shared_include
                    git checkout ${shared_include_commitid}
                """
            }
        }
        sh """
            rm -rf gr-kmd/${env.repo}
            cp -rf ${env.repo} gr-kmd/
            cp -rf gr-kmd /root/
            ls -l /root ||:
        """
    }
}

def build() {
    credentials.runWithCredential('SSH_GITLAB') {
        sh """
            cd ${env.repo}/tests/
            mkdir build && cd build
            cmake ..
            cd .. && ./run_alltests.sh
        """
    }
}

def buildInKmd() {
    String envExport = env.exports ? 'export ' + env.exports.split().join(' && export ') : ''
    credentials.runWithCredential('SSH_GITLAB') {
        def buildCmd = env.clangTidyCmd ? "bear ${env.cmd}" : env.cmd
        def pkgName = env.packageName ?: 'mtgpu-1.0.0.0.deb'
        sh """
            cd /root/gr-kmd
            ${envExport}
            ${buildCmd}
        """
        if (env.needUpload == 'true') {
            sh """
                cd /root/gr-kmd/binary_*/target_*/
                cp mtgpu*.deb ${env.WORKSPACE}/${env.commitId}_${pkgName}
                ls -l ${env.WORKSPACE}
            """
            artifact.upload(env.repo, env.branch, env.commitId, "${env.WORKSPACE}/${env.commitId}_${pkgName}")
        }
        if (env.clangTidyCmd) { sh "cd /root/gr-kmd; ${env.clangTidyCmd}" }
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }]
    ]
    def buildClosure = env.rmInKmd?.toBoolean() ? { buildInKmd() } : { build() }
    workflow['build'] = [closure: buildClosure, setGitlabStatus: true, statusName: env.testLabel]

    runPipeline(workflow)
}
