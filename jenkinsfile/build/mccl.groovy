@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

env.repo = 'mccl'
packageName = env.packageName
env.mcclBranch = env.mcclBranch ?: env.branch

def fetchCode() {
    env.mcclCommitId = gitLib.fetchCode(env.repo, env.mcclBranch, env.mcclCommitId)
    packageName =  env.packageName ?: "${env.commitId}_${env.packageName}"

    try {
        if (env.packagePath && !env.packagePath.endsWith('others')) {
            utils.appendVersionInfo(env.repo, env.mcclBranch, env.mcclCommitId, "${env.packagePath}/others", 'musa_sdk_commit.txt')
        }
    } catch (e) {
        println "追加版本信息文件到OSS失败: ${e.getMessage()}"
    }
}

def envSetUp() {
    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(env.musaToolkitsPackageUrl)
    } else {
        sh '''
            apt update ||:
            apt install libcunit1 -y ||:
        '''
        ddk.installLinuxDdk(env.linuxDdkPackageUrl)
        if (env.musaRuntimePackageUrl) {
            musa.installMusaRuntime(env.musaRuntimePackageUrl)
        }
        def dependencies = ['mtcc': env.mtccPackageUrl]
        installDependency(dependencies)
    }
}

def build_old() {
    envExport = env.exports ? 'export ' + env.exports.split().join(' && export ') : ''
    // envExport = env.gcov == 'ON' ? env.exports + '&& export GCOV_TEST=ON' : envExport
    dir(env.repo) {
        sh """
            ${envExport}
            make src.build ${compileArgs} TRACE=1 -j ${compileParallel}
            cd pkg/ && make -j
        """
        sh '''
            cd build/pkg/txz/
            ls *.txz | xargs tar -Jxvf && rm *.txz
            chmod 777 mccl*/install.sh
            mv mccl* mccl
        '''
    }
    sh """
        cd ${env.repo}/build/pkg/txz/
        tar czf ${env.WORKSPACE}/${packageName} ./*
    """
}

def mccl_build() {
    envExport = env.exports ? 'export ' + env.exports.split().join(' && export ') : ''
    dir(env.repo) {
        sh """
            ${envExport}
            bash setup.sh -l -b -p -a ${mtgpu_arch_id}  -s "${build_args}"
        """
        sh '''
            cd build/pkg/txz/
            ls *.txz | xargs tar -Jxvf && rm *.txz
            chmod 777 mccl*/install.sh
            mv mccl* mccl
        '''
    }
    sh """
        cd ${env.repo}/build/pkg/txz/
        tar czf ${env.WORKSPACE}/${packageName} ./*
    """
}

def upload() {
    def repo = env.gitlabSourceRepoName ?: env.repo
    def branch = env.gitlabSourceBranch ?: env.mcclBranch
    def commitId = env.gitlabMergeRequestLastCommit ?: env.mcclCommitId
    if (env.packagePath) {
        artifact.upload("${packageName}", env.packagePath)
    } else {
        artifact.upload(repo, branch, commitId, "${packageName}")
    }
}

runner.start(env.runChoice) {
    def buildClosure = env.use_build_scripts == 'true' ? { mccl_build() } : { build_old() }

    def workflow = [
        'fetchCode': [closure: { fetchCode() }],
        'envSetUp': [closure: { envSetUp() }],
        'build': [closure: buildClosure],
        'upload': [closure: { upload() }],
    ]

    runPipeline(workflow)
}
