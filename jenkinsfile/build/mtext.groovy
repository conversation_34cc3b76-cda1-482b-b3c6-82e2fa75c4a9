@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

def fetchCode() {
    gitLib.fetchCode('mtext', env.branch, env.commitId)
    if (env.wddmCommitId) {
        gitLib.fetchCode('wddm', null, env.wddmCommitId, [disableSubmodules: true])
        credentials.runWithCredential('SSH_GITLAB') {
            dir('wddm') {
                utils.runCommand('git submodule update --init shared_include')
            }
        }
        bat '''
            rm -rf mtext/shared_include
            cp -rf wddm/shared_include mtext
        '''
    } else if (env.ddkBranch) {
        gitLib.fetchCode('linux-ddk', env.ddkBranch, null, [disableSubmodules: true])
        credentials.runWithCredential('SSH_GITLAB') {
            dir('linux-ddk') {
                utils.runCommand('git submodule update --init shared_include')
            }
        }
        sh '''
            rm -rf mtext/shared_include
            cp -rf linux-ddk/shared_include mtext
        '''
    }
}

def runBuild() {
    dir('mtext') {
        // bat 'vs_build.bat'
        utils.runCommand(env.cmd)
        utils.runCommand('ls -l')
    }
}

def upload() {
    def artifacts = readJSON(text: env.artifacts)
    artifact.upload(artifacts)
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [ closure: { fetchCode() } ],
        'build': [ closure: { runBuild() } ]
    ]

    if (env.artifacts) {
        workflow['upload'] = [ closure: { upload() } ]
    }

    runPipeline(workflow)
}
