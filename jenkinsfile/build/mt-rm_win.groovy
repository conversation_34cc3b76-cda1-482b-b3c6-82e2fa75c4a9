@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, [updateBuildDescription: true])
    gitLib.updateSubmodule(env.repo)

    if (env.dependencyRepo) {
        def repoSettings = readJSON text: env.dependencyRepo
        gitLib.fetchCode(repoSettings.name, repoSettings.branch, env.dependencyRepoCommitid, [preBuildMerge: false, disableSubmodules: true, shallow: false, updateBuildDescription: true])
        gitLib.updateSubmodule(repoSettings.name, 1, '', repoSettings.skip_submodules)

        dir("${repoSettings.name}\\${repoSettings.linkPath}") {
            deleteDir()
        }
        bat "mklink /J ${repoSettings.name}\\${repoSettings.linkPath} ${env.repo}"
    }
}

def build() {
    def shouldRun = true

    dir(env.repo) {
        def changedFiles = sh(script: "git diff origin/${env.gitlabTargetBranch} --name-only --merge-base", returnStdout: true).trim()
        def changedFilesList = changedFiles.split('\n').findAll { it != '' }

        if (env.whitelist) {
            def whitelistArray = env.whitelist.contains(',') ?
                /* groovylint-disable-next-line UnnecessaryCollectCall */
                env.whitelist.split(',').collect { it.trim() } :
                [env.whitelist.trim()]
            shouldRun = commonLib.filterWithList(whitelistArray, changedFilesList, 'whitelist')
        }
    }

    if (shouldRun) {
        echo 'Running build command...'
        dir("${env.buildPath}") {
            bat "${env.cmd}"
        }
    } else {
        echo 'Skipping build - whitelist filter did not pass'
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }]
    ]
    def buildClosure = { build() }
    workflow['build'] = [closure: buildClosure, setGitlabStatus: true, statusName: env.testLabel]

    runPipeline(workflow)
}
