@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * branch (String) - develop
 * commitId (String) - ''
 * exports (Multiline String) default 'usual export', split by ';'
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/build-env/gr-umd-uos:v26
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=9;requests=memory=96Gi;limits=cpu=18;limits=memory=96Gi
*/

env.repo = 'muSOLVER'
dependentDir = "${env.WORKSPACE}/relyPkgDir"
env.branch = env.muSOLVERBranch ? env.muSOLVERBranch : env.branch
env.commitId = env.muSOLVERBranch ? '' : env.commitId

// this pipeline can be triggered in other repos's CI
if (env.gitlabSourceRepoName == env.repo) {
    env.branch = env.gitlabSourceBranch
    env.commitId = ''
} else {
    env.branch = commonLib.findMrDependency(env.repo, (env.gitlabMergeRequestTitle ?: '') + (env.gitlabMergeRequestDescription ?: '')) ?: env.branch
}

envExport = utils.generateEnvExport(env, true)

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    packageName = env.packagePath ? env.packageName : "${env.gitlabMergeRequestLastCommit ? constants.formatCommitID(env.gitlabMergeRequestLastCommit) : env.commitId}_${env.packageName}"
}

def envSetUp() {
    sh '''
        apt update ||:
        apt install libcunit1 -y ||:
    '''
    ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    if (env.musaRuntimePackageUrl) {
        musa.installMusaRuntime(env.musaRuntimePackageUrl)
    }
    def dependencies = ['mtcc': env.mtccPackageUrl]
    installDependency(dependencies)
    constants.downloadAndUnzipPackage(env.muBLASPackageUrl)
    sh 'cd muBLAS && chmod +x install.sh . && ./install.sh'
}

def build() {
    dir(env.repo) {
        if (env.cmd) {
            sh """
                ${envExport}
                ${env.cmd}
            """
        }else {
            String buildPath = "${env.WORKSPACE}/${env.repo}"
            sh """
                ${envExport}
                chmod +x ./build.sh
                ${env.buildcmd} -d ${buildPath}/build/install
            """
        }
    }
    sh "tar czf ${packageName} ${env.repo}/build ${env.repo}/install.sh"
}

def upload() {
    def repo = env.gitlabSourceRepoName ?: env.repo
    def branch = env.gitlabSourceBranch ?: env.branch
    def commitId = env.gitlabMergeRequestLastCommit ?: env.commitId
    if (env.packagePath) {
        artifact.upload("${packageName}", env.packagePath)
    } else {
        artifact.upload(repo, branch, commitId, "${packageName}")
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'fetchCode': [closure: { fetchCode() }, handler: 'closure', async: false],
        'envSetUp': [closure: { envSetUp() }, handler: 'closure', depends: ['checkout'], async: false],
        'build': [closure: { build() }, handler: 'closure', depends: ['env-setup'], async: false],
        'upload': [closure: { upload() }, handler: 'closure', depends: ['build'], async: false],
    ]
    runPipeline(workflow)
}
