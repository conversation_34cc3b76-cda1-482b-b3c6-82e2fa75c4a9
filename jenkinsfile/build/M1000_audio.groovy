@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
}

def build() {
    dir(env.repo) {
        sh """
            ${env.cmd}
        """
    }
}

def upload() {
    sh "tar czvf ${env.packageName} ${env.output}"
    artifact.upload(env.repo, env.gitlabTargetBranch, env.commitId, env.packageName)
}

runner.start(env.runChoice) {
    def workFlow = [
        'checkout': [closure: { fetchCode() }],
        'build': [closure: { build() }],
        'upload': [closure: { upload() }]
    ]
    runPipeline(workFlow)
}
