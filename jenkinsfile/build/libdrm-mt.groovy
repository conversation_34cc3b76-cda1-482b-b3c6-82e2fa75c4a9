@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

env.repo = 'libdrm-mt'

// runChoice cluster podNodeSelector podResources envExport gcov coverity containerImage triggerInfo
envExport = (env.envExport && env.envExport != 'null') ? env.envExport : ''
def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
}

def installDependency() {
    sh '''
        apt install -y libpciaccess-dev ||:
        apt install -y libcunit1-dev ||:
    '''
}

def compile() {
    dir(env.repo) {
        // 是否执行代码覆盖率统计
        if (env.gcov == 'true') {
            envExport = "export GCOV_TEST=ON;${envExport}"
        }
        // 是否执行代码静态扫描
        if (env.coverity == 'true') {
            sh """
                ${envExport}
                wget -q --no-check-certificate  https://oss.mthreads.com/backup/coverity/cov-analysis-linux64-2022.3.1.tar.gz && tar xf cov-analysis-linux64-2022.3.1.tar.gz
                cd cov-analysis-linux64-2022.3.1/bin && mv sample-license.config license.config && echo 'license-server @**************' >>license.config && cat license.config && cd -
                echo './mt_build.sh -p'|cov-analysis-linux64-2022.3.1/bin/cov-build --dir idir bash
                cov-analysis-linux64-2022.3.1/bin/cov-manage-emit --dir idir reset-host-name
                cov-analysis-linux64-2022.3.1/bin/cov-import-scm --scm git --dir idir
                cov-analysis-linux64-2022.3.1/bin/cov-analyze --security-file=cov-analysis-linux64-2022.3.1/bin/license.config --wait-for-license --dir idir --all # --strip-path ${libdrm_dir} --tu-pattern \"! file('.')\"
                tar czf ${env.WORKSPACE}/${env.commitId}_libdrm_coverity_idir.tar.gz idir
            """
        } else {
            sh """
                ${envExport}
                meson builddir --prefix=/usr/local/lib/musa
                ninja -C builddir install
            """
        }
        sh """
            cd /usr/local/lib
            printf 'project:${env.repo}\nbranch:${env.branch}\ncommitId:${env.commitId}' > ${env.BUILD_TIMESTAMP + env.repo + '.txt'}
            tar -czf ${env.WORKSPACE}/${env.commitId}_libdrm_mt.tar.gz ./musa ${env.BUILD_TIMESTAMP + env.repo + '.txt'}
        """
    }
}

def upload() {
    artifact.upload(env.repo, env.branch, env.commitId, "${env.commitId}_libdrm_mt.tar.gz")
    if (coverity == 'true') {
        artifact.upload(env.repo, env.branch, env.commitId, "${env.commitId}_libdrm_coverity_idir.tar.gz")
    }
    if (env.updateLatestFile == 'true') {
        constants.updateLatestTxt(env.repo, env.branch, env.commitId)
    }
    utils.catchErrorContinue { deleteDir() }
}

runner.start(env.runChoice) {
    runPipeline([
        'checkout': [closure: { fetchCode() }],
        'install dependency': [closure: { installDependency() }],
        'compile libdrm': [closure: { compile() }],
        'upload libdrm': [closure: { upload() }]
    ])
}
