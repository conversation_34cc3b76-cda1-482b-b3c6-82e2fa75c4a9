@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

/*
 * parameters
 * branch (String) - develop
 * commitId (String) - ''
*/

env.repo = 'diagsys'

def build() {
    runnerHttp.runJob('diagsys_build', ['Shared_Include_Commit_Id':env.gitlabMergeRequestLastCommit, 'upload':'false'])
}

runner.start(env.runChoice) {
    def workflow = [
        'build': [closure: { build() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
    ]
    runPipeline(workflow)
}
