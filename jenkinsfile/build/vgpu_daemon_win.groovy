@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

env.repo = 'vgpu_daemon'

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)

    if (!env.cmd) {
        def config = [:]
        dir(env.repo) {
            config = commonLib.loadPipelineConfig('.ciConfig.yaml', '', [:], 'vgpu_daemon/default.yaml')
        }
        def builds = config.builds.find { it.job == env.JOB_NAME }
        if (builds && builds.parameters) {
            env.cmd = builds.parameters.cmd
            env.lasterMtml = builds.parameters.lasterMtml
        } else {
            error "Build parameters not found for job: ${env.JOB_NAME}"
        }
    }
}

def fetchMtml() {
    def mtmlProductsUrl = sh(script: "curl --insecure ${env.lasterMtml} -k", returnStdout: true).trim()
    def mtmlProductsPkgName = mtmlProductsUrl.split('/')[-1]

    dir('vgpu_daemon\\deps') {
        bat """
            wget -q ${mtmlProductsUrl} --no-check-certificate && tar xzf ${mtmlProductsPkgName}
            rm -rf ${mtmlProductsPkgName}
        """
    }
}

def buildAndUpload() {
    dir(env.repo) {
        oss.install()
        oss.cp('oss/release-ci/vgpu_daemon/icon.ico', './deps/')
        bat "${env.cmd}"
        stash name: 'daemon_signature_dir', includes: 'output/'
    }

    node(env.iTrusSign_Nodes) {
        deleteDir()
        stage('unstash daemon_signature_dir') {
            unstash 'daemon_signature_dir'
        }

        timeout(2) {
            stage('iTrusSign') {
                bat """
                    for %%f in (output\\*.exe) do (
                        signtool sign /fd sha256 /sha1 ${constants.signtoolSha1} /t http://timestamp.globalsign.com/?signature=sha2 "%%f"
                    )
                """
                sleep 15
            }
        }

        stage('stash daemon_dir_signed') {
            stash name: 'daemon_dir_signed', includes: 'output/'
        }
    }

    dir('signed') {
        stage('unstash daemon_dir_signed') {
            unstash 'daemon_dir_signed'
        }
        def pkgName = "Signed_${env.commitId}_${env.packageName}"
        if (env.needUpload == 'true') {
            bat "tar -cvzf ${pkgName} -C output *"
            artifact.upload(env.repo, env.branch, env.commitId, "${pkgName}")
        }
    }
}

runner.start(env.runChoice) {
    runPipeline([
        'fetch code': [closure: { fetchCode() }],
        'fetch mtml': [closure: { fetchMtml() }],
        'build': [closure: { buildAndUpload() }, setGitlabStatus: true, statusName: 'jenkins/win_build']
    ])
}
