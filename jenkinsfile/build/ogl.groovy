@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

env.repo = 'ogl'

// runChoice cluster podNodeSelector podResources containerImage branch commitId triggerInfo
def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
}

def compile() {
    dir(env.repo) {
        sh """
            cmake --preset linux_release -DGLVND=true -DGLVND_VENDOR_NAME=mtgpu -DDRI_SEARCH_PATH=/usr/local/lib/musa/lib/x86_64-linux-gnu -DGBM_BACKENDS_PATH=/usr/local/lib/musa/lib/x86_64-linux-gnu
            cmake --build --preset linux_release -- -j12
            mkdir ogl_mtgpu_dri
            mv build/x86_64-release/*.so* ogl_mtgpu_dri
            tar -czf ${env.WORKSPACE}/${env.commitId}_ogl_mtgpu_dri.tgz ogl_mtgpu_dri
        """
    }
}

def upload() {
    artifact.upload(env.repo, env.branch, env.commitId, "${env.commitId}_ogl_mtgpu_dri.tgz")
    utils.catchErrorContinue { deleteDir() }
}

runner.start(env.runChoice) {
    runPipeline([
        'checkout': [closure: { fetchCode() }],
        'compile ogl': [closure: { compile() }],
        'upload ogl': [closure: { upload() }]
    ])
}
