@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * branch (String) - master
 * musaToolkitsPackageUrl (String) - ''
 * muAlgPackageUrl (String) - ''
 * muThrustPackageUrl (String) - ''
 */

repoName = 'AlphaCore'

def fetchCode() {
    gitLib.fetchCode(repoName, env.branch)
}

def setupDepends() {
    musa.installMusaToolkits(env.musaToolkitsPackageUrl)
    musa.installmuAlg(env.muAlgPackageUrl)
    musa.installmuThrust(env.muThrustPackageUrl)
}

def build() {
    dir(repoName) {
        credentials.runWithCredential('SSH_GITLAB') {
            sh 'cmake -DUSE_MUSA=ON \
                -DUSE_CUDA=OFF \
                -DCUSTOM_HOUDINI_VERSION=19.5.303 \
                -DHDK_OUTPUT_PATH="./houdini19.5.303/dso" \
                -DENABLE_TEST=ON \
                -DFETCH_TEST_ASSETS=OFF \
                -DUSE_OPENGL=OFF \
                -DUSE_OPENVDB=OFF \
                -DCMAKE_BUILD_TYPE:STRING=Release \
                -B ./build -G Ninja'

            sh 'cmake --build build --config Release --target AlphaCoreUnitTest -j $(grep -c ^processor /proc/cpuinfo)'
            sh 'cmake --build ./build --config Release --target ALL_HDK -j $(grep -c ^processor /proc/cpuinfo)'

            sh 'rm -rf build/CMakeCache.txt'
        }
    }
}

def upload() {
    dir(repoName) {
        sh 'python3 tools/Publish/HoudiniPluginPackage.py "19.5.303" "Musa"'
        sh 'mv build/AlphaCoreHoudini*.zip build/AlphaCoreHoudini.zip'
        commonLib.retryByRandomTime({
            artifact.upload('build/test/AlphaCoreUnitTest', env.packagePath)
            artifact.upload('build/AlphaCoreHoudini.zip', env.packagePath)
        }, 20)
    }
}

runner.start(env.runChoice) {
    def workflow = [:]
    workflow['fetchCode'] = [ closure: { fetchCode() } ]
    workflow['setupDepends'] = [ closure: { setupDepends() }]
    workflow['build'] = [ closure: { build() }]
    workflow['upload'] = [ closure: { upload() }]
    runPipeline(workflow)
}
