@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

commonLib = new common()
gitLib = new git()

/*
 * parameters
 * ddkBranch (String) - master
 * ddkCommitId (String) - null
 * branch (String) - develop
 * commitId (String) - null
 * packageName (String) - ddk2.0
 * packagePath (String) - null
 * packRepoBranch (String) - ddk2.0_deb
 * cmd (String) - ./ddk_build.sh
 * os (String) - Ubuntu
 * arch (String) - x86_64
 * version (String) - ''
 * submoduleConfig (Multiline String):
    {
        MUSA-Runtime: head,
        fec-linux: head,
        fec-trusted-firmware-a: head,
        gpu-fw: head,
        gr-kmd: head,
        gr-umd: head,
        libdrm-mt: head,
        m3d: head,
        media-driver: head,
        ogl: head,
        shared_include: head
    }
 * exports (Multiline String)
    'KERNELVER=5.4.0-42-generic'
    'KERNELDIR=/lib/modules/5.4.0-42-generic/build'
 * buildChoice (Choice) - deb (deb|tar.gz)
 * uploadCase (Choice) - false (false|true)
 * runChoice (Choice) - node
 * nodeLabel (Choice) - Linux_build
 * containerImage (String) - sh-harbor.mthreads.com/qa/linux-ddk:v6
*/

env.ddkRepo = 'linux-ddk'
// remove ccachedCmd in pod env
// ccachePath = '/home/<USER>/ddk_ccache'
// env.mountParms = "-v ${ccachePath}:${ccachePath}"

env.packageName = env.packageName ?: env.pkgName
env.needSeperateRuntime = false
// default runtime build
if (env.cmd.contains('-r 1')) {
    env.needSeperateRuntime = true
}
// other runtime build para
else if (env.cmd.contains('-r')) {
    env.needSeperateRuntime = false
}
// default ddk build para contains default runtime
else if (env.cmd.contains('-a 0')) {
    env.needSeperateRuntime = false
} else {
    env.needSeperateRuntime = true
}
env.runtimeName = "musa_runtime_for_build.${env.arch}.tar.gz".replaceAll('x86_64', 'amd64').replaceAll('aarch64', 'arm64')
def isValidPackageSuffix(String packageName) {
    return ['.deb', '.rpm', '.tar.gz'].any {
        packageName.endsWith(it)
    }
}

if (env.packageName) {
    if (!isValidPackageSuffix(env.packageName)) {
        env.packageName += env.buildChoice ? ".${env.buildChoice}" : '.deb'
    } else if (env.buildChoice && !env.packageName.endsWith(env.buildChoice)) {
        env.packageName = env.packageName.replaceAll(/(deb|rpm|tar\.gz)$/, env.buildChoice)
    }
}

def isSubmoduleExists(String submodule) {
    return utils.runCommandWithStatus("git config --file .gitmodules --get-regexp path | grep '${submodule}'") == 0
}

def fetchCode() {
    if (env.gitlabSourceRepoName == 'linux-ddk') {
        env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, [submoduleShallow: true, noTags: true, updateBuildDescription: true])
        env.ddkBranch = gitLib.triggeredByMR() ? env.gitlabTargetBranch : env.branch
    } else {
        env.ddkCommitId = gitLib.fetchCode(env.ddkRepo, env.ddkBranch, env.ddkCommitId, [submoduleShallow: true, noTags: true, updateBuildDescription: true])
        if (env.gitlabSourceRepoName) {
            dir(env.ddkRepo) { env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, [submoduleShallow: true, updateBuildDescription: true]) }
        }
    }
    drvCommit = env.commitId ?: env.ddkCommitId
    commitFile = "${env.WORKSPACE}/${drvCommit}_commitInfo.txt"
    if (env.ddkCommitId) { sh "echo '${env.ddkRepo}: ${env.ddkCommitId}' >> ${commitFile}" }
    Map submoduleConfig = readJSON text: env.submoduleConfig
    credentials.runWithCredential('SSH_GITLAB') {
        dir('linux-ddk') {
            gitLib.installGitLfs()
            submoduleConfig.each { submodule, commit ->
                if (!isSubmoduleExists(submodule)) {
                    println("No submodule named ${submodule} exists, skip")
                    return
                }
                def newCommit = commonLib.findMrDependency(submodule, (env.gitlabMergeRequestTitle ?: '') + (env.gitlabMergeRequestDescription ?: ''))
                commit = newCommit ?: commit
                // if (!commit.contains(constants.mrDependencyDelimiter)) {
                //     sh "git submodule update --init --depth 1 ${submodule}"
                // }
                dir(submodule) {
                    String gitOps = ''
                    if (commit.contains(constants.mrDependencyDelimiter)) {
                        def (preCommit, mergeBranch) = commit.split(constants.mrDependencyDelimiter)
                        // Do not use --depth 1 to preserve historical commit records to ensure successful premerge
                        gitOps = """
                            git remote set-branches origin ${preCommit} ${mergeBranch}
                            git fetch origin
                            git checkout ${preCommit}
                            git merge origin/${mergeBranch}
                        """
                    } else if (commit != 'head') {
                        gitOps = """
                            git fetch origin --depth 1 ${commit}
                            git checkout ${commit}
                        """
                    }
                    if (gitOps) {
                        sh """
                            ${gitOps}
                            git clean -dffx
                            git submodule update --init --recursive --depth 1
                        """
                    }
                }
                subCommit = gitLib.getCurrentCommitID(submodule)
                sh "echo '${submodule}: ${subCommit}' >> '${commitFile}'"
                currentBuild.description += "${submodule} | ${subCommit}<br>"
            }
        }
        sh 'rm -rf /home/<USER>/home/<USER>'
    }
}

def build() {
    String envExport = env.exports ? 'export ' + env.exports.split().join(' && export ') : ''
    // def ccachedCmd = commonLib.ccachedCmd(ccachePath)
    credentials.runWithCredential('SSH_GITLAB') {
        sh """
            cd /home/<USER>
            ${envExport}
            ${env.cmd}
            cd ${env.WORKSPACE}
            mv /home/<USER>
        """
    }
    if (env.gitlabActionType == 'PUSH') {
        catchError(buildResult: null, stageResult: null) { utils.uploadDbgSym('linux-ddk/build/mt_video', 'oss/debug-symbols/scripts/muti-media/upload_video_dbgsym') }
    }
}

def sign() {
    def COOKIE_FILE = 'login.cookie'
    credentials.runWithCredential('LDAP-ZY') {
        mtbiosSign.login(USERNAME, PASSWORD, 'SW', COOKIE_FILE)
    }
    dir('linux-ddk') {
        def results = mtbiosSign.signFirmwareInDirectory(
            'lib/firmware/mthreads',     // searchPath
            'mtfw-gen*.bin',          // pattern
            'model',                  // excludePattern
            '../temp-signed-output',  // tempOutputDir
            "../${COOKIE_FILE}"       // cookieFile
        )

        echo 'Signing completed!'
        echo "Total files: ${results.totalFiles}"
        echo "Successfully signed: ${results.signedCount}"
        echo "Failed: ${results.failedFiles.size()}"

        if (results.failedFiles.size() > 0 && results.signedCount == 0) {
            error 'All firmware signing operations failed!'
        }
    }
}

def packTarPkg() {
    sh """
        tar czf ${drvCommit}_${env.packageName} linux-ddk/build/
        mv ${drvCommit}_${env.packageName} ${env.WORKSPACE}/linux-ddk/build/
    """
}

// TO DO:
def packAllInOne() {
    gitLib.fetchCode('musa_package', env.packRepoBranch)
    def exportStr = [
        "export is_ci_build=${env.isReleasePkg == 'true' ? 'false' : 'true'}",
        "export ddkBranch=${env.ddkBranch ?: env.gitlabTargetBranch}",
        'export glvnd_enable=Y',
        'export without_xorg=Y',
        'export install_libdrm=true',
        'export install_fw=true',
        'export install_fec=true',
        'export install_fec_trusted_fw=true',
        'export install_ddk_kmd=true',
        'export install_ddk_umd=true',
        'export install_musa_runtime=true',
        'export install_ogl=true',
        'export install_m3d=true',
        'export install_ddk_video=true',
        'export install_gmi=true',
    ]
    def toRemove = [
        '-r 0': 'export install_musa_runtime=true',
        '-f 0': 'export install_fec=true',
        '-t 0': 'export install_fec_trusted_fw=true',
        '-g 0': 'export install_fw=true',
        '-k 0': 'export install_ddk_kmd=true',
        '-u 0': 'export install_ddk_umd=true',
        '-l 0': 'export install_libdrm=true',
        '-m 0': 'export install_m3d=true',
        '-o 0': 'export install_ogl=true',
        '-v 0': 'export install_ddk_video=true',
        '-G 0': 'export install_gmi=true'
    ]
    def masterBranches = ['master', 'release_musa_4.2.0']
    if ((env.gitlabSourceRepoName == 'linux-ddk' && masterBranches.contains(env.gitlabTargetBranch)) || masterBranches.contains(env.ddkBranch)) {
        exportStr.add('export install_musa=true')
        toRemove['-m 0'] = 'export install_musa=true'
        toRemove['--m3d 0'] = 'export install_m3d=true'
        env.needSeperateRuntime = false
    }
    toRemove.each { flag, item ->
        if (env.cmd.contains(flag)) {
            exportStr.remove(item)
        }
    }
    boolean containsUmd = exportStr.any { it.contains('install_ddk_umd') }
    if (containsUmd) {
        exportStr.add('export install_ddk_umd_ut=true')
    }
    deviceType = env.deviceType ?: 'pc'
    exportStr.add("export deviceType=${deviceType}")
    dir('musa_package') {
        tmp_date = new Date().format('yyyy.MM.dd')
        fullVersion = env.debVersion ?: "${tmp_date}-${env.build_ID}-${env.os}+${drvCommit}"
        suffix = env.packageName.split('\\.')[-1]
        packCmd = "./build_package.sh -t ${env.os} -p ${suffix.toUpperCase()} -a ${env.arch} -v ${env.version ?: fullVersion}"
        sh """
            ${exportStr.join(';')}
            ${packCmd}
            mv musa*.${suffix} ${env.WORKSPACE}/linux-ddk/build/${drvCommit}_${env.packageName}
        """
        if (env.needSeperateRuntime == 'true') {
            sh """
                mv musa_runtime*.tar.gz ${env.WORKSPACE}/linux-ddk/build/${drvCommit}_${env.runtimeName}
            """
        }
    }
}

def upload() {
    String packageName = "${drvCommit}_${env.packageName}"
    String runtimeName = "${drvCommit}_${env.runtimeName}"
    dir('linux-ddk/build') {
        if (env.packagePath) {
            artifact.upload(packageName, env.packagePath)
            if (env.packageName) {
                if (['.deb', '.rpm'].any {
                    env.packageName.endsWith(it)
                }) {
                    if (env.needSeperateRuntime == 'true') {
                        artifact.upload(runtimeName, env.packagePath)
                    }
                }
            }
        } else {
            artifact.upload(env.repo, env.branch, env.commitId, packageName)
            if (env.packageName) {
                if (['.deb', '.rpm'].any {
                    env.packageName.endsWith(it)
                }) {
                    if (env.needSeperateRuntime == 'true') {
                        artifact.upload(env.repo, env.branch, env.commitId, runtimeName)
                    }
                }
            }
        }
    }
    if (env.packagePath) {
        artifact.upload("${drvCommit}_commitInfo.txt", env.packagePath)
    } else {
        artifact.upload(env.repo, env.branch, drvCommit, "${drvCommit}_commitInfo.txt")
    }
}

def shouldUpdateCaseList(caseListPath) {
    def submoduleName = caseListPath.split('/')[0]
    def shouldUpdate = false
    credentials.runWithCredential('SSH_GITLAB') {
        def submoduleExists = isSubmoduleExists(submoduleName)
        if (fileExists(caseListPath)) {
            shouldUpdate = true
        } else if (submoduleExists) {
            sh "git submodule update --init --depth 1 ${submoduleName}"
            shouldUpdate = fileExists(caseListPath)
        } else {
            println "⚠️ Submodule ${submoduleName} does not exist, skipping update for ${caseListPath}"
        }
    }
    return shouldUpdate
}

def uploadCase() {
    gitLib.fetchCode('mt-gfx-test', 'master', null, [disableSubmodules: true, updateBuildDescription: true])
    oss.install()
    def caseOssPath = "oss/sw-build/linux-ddk/${env.branch}/test/cts/"
    dir('mt-gfx-test/VK-GL-CTS_caselist/linux') {
        oss.cp('gles/gles_cts_pr_ddk2.txt', caseOssPath)
        oss.cp('gles/gles_cts_pr_ddk2_m3d.txt', caseOssPath)
        oss.cp('gles/gles_cts_pr_newapi_mtcc.txt', caseOssPath)
        oss.cp('vulkan/vk_cts_pr_ddk2.txt', caseOssPath)
    }
    dir('linux-ddk') {
        if (shouldUpdateCaseList('gr-umd/unittests/pytest')) {
            oss.cp('gr-umd/unittests/pytest/gr_umd_test_list.txt', "oss/sw-build/linux-ddk/${env.branch}/test/")
        }
        if (shouldUpdateCaseList('vulkan/ci/linux')) {
            oss.cp('vulkan/ci/linux/cts_ci_linux.txt', caseOssPath)
        }
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'build': [closure: { build() }],
    ]
    if (env.packageName) {
        if (['.deb', '.rpm'].any {
            env.packageName.endsWith(it)
        }) {
            workflow['packAllInOne'] = [closure: { packAllInOne() }]
        } else {
            workflow['packTarPkg'] = [closure: { packTarPkg() }]
        }
        workflow['upload'] = [closure: { upload() }, setGitlabStatus: true, statusName: env.testLabel]
    }
    if (env.uploadCase.toBoolean()) { workflow['uploadCase'] = [closure: { uploadCase() }] }
    runPipeline(workflow, [post: { deleteDir() }])
}
