@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

// mtmlBranch(develop) mtmlVersion targetArch packRepoBranch containerImage

def fetchCode() {
    gitLib.fetchCode('musa_package', env.packRepoBranch)
}

def resolveTag(String branch) {
    switch (branch) {
        case 'develop':
            return '-D'
        case 'master':
            return '-M'
        default:
            return branch?.contains('release') ? '-R' : ''
    }
}

def resolveMtmlVersion(String mtmlBranch, String mtmlVersion = null) {
    if (mtmlVersion) {
        return mtmlVersion.replace(' ', '')
    }
    if (mtmlBranch == 'develop') {
        return new Date().format('yyyy.MM.dd')
    }
    if (mtmlBranch.contains('_')) {
        def parts = mtmlBranch.split('_')
        if (parts.size() > 1) {
            return parts[1]
        }
    }
    return '1.0.0'
}

def build() {
    dir('musa_package') {
        def mtmlVersion = resolveMtmlVersion(env.mtmlBranch, env.mtmlVersion)
        def tag = resolveTag(env.mtmlBranch)
        apt.installPackage('rpm')
        oss.install()
        sh """
            set -e
            ./build_deb.sh  ${mtmlVersion}-linux${tag}  ${env.BUILD_ID} ${env.mtmlBranch} ${env.targetArch} ${env.mtmlCommit ?: ''}
        """
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'build': [closure: { build() } ]
    ]

    runPipeline(workflow)
}
