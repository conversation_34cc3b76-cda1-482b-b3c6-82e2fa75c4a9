@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

def rebuildDDK() {
    def ddkName = env.linuxDdkPackageUrl.split('/')[-1]
    constants.downloadPackage(env.linuxDdkPackageUrl)
    sh """
        dpkg-deb -R ${ddkName} ${env.packageName}
        ${env.rebuildCmd}
        dpkg-deb -b ${env.packageName} ${env.packageName}.deb
    """
    artifact.upload(env.gitlabSourceRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, "${env.packageName}.deb")
}

runner.start(env.runChoice) {
    runPipeline([
        'rebuildDDK': [closure: { rebuildDDK() }]
    ])
}
