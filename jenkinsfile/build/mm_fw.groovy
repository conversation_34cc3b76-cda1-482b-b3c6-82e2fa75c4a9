@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

/*
 * parameters
 * repo (String) - wave517_fw|wave627_fw
 * branch (String) - master
 * commitId (String) - null
 * cmd (String) - ./build.sh
 * distPath (String) - ./firmware/system_fw/vincent.bin
 * fwPackageName (String) - mtvpu-01-1.0.bin
 * runCppcheck (String) - yes
 * convertToolUrl(String) - https://oss-swci-sh.mthreads.com/dependency/SWQA-CI/Linux/htmlreport.tar.gz
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (String) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/build-env/fw:v6
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=8;requests=memory=8Gi;limits=cpu=8;limits=memory=16Gi
*/

gitLib = new git()
commonLib = new common()

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, [updateBuildDescription: true])
}

def build() {
    dir(env.repo) {
        utils.runCommand('chmod +x -R ./')
        utils.runCommand(env.cmd)
        if (env.runCppcheck == 'yes') {
            gitLib.setGitlabStatus('cppcheck', 'running')
            def result = utils.runCommandWithStatus('cppcheck ./ -q -j 8 --inline-suppr --enable=warning  --suppressions-list=suppress_list.txt --error-exitcode=1 --xml --xml-version=2 --output-file=cppcheck.xml')
            if (result.toInteger() == 1) {
                gitLib.setGitlabStatus('cppcheck', 'failed')
                constants.downloadAndUnzipPackage(env.convertToolUrl)
                utils.runCommand('htmlreport/cppcheck-htmlreport --file=cppcheck.xml --report-dir=html --source-dir=.')
                commonLib.publishHTML('html', 'index.html', "Cppcheck Results ${env.repo}")
                error 'Cppcheck fail!'
            } else {
                gitLib.setGitlabStatus('cppcheck', 'success')
            }
        }
    }
}

def upload() {
    dir(env.repo) {
        sh "mv ${env.distPath} ${env.fwPackageName}"
        artifact.upload(env.repo, env.branch, env.commitId, env.fwPackageName)
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'build': [closure: { build() }, setGitlabStatus: true, statusName: env.testLabel]
    ]
    if (env.fwPackageName) {
        workflow['upload'] = [closure: { upload() }]
    }

    runPipeline(workflow)
}
