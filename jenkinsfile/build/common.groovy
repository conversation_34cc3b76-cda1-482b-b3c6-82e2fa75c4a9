@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

def fetchCode() {
    gitLib.fetchCode(env.repo, env.branch, env.commitId)
}

def runBuild() {
    credentials.runWithCredential('SSH_GITLAB') {
        dir(env.repo) {
            utils.runCommand(env.cmd)
        }
    }
}

def upload() {
    def artifacts = readJSON(text: env.artifacts)
    artifact.upload(artifacts)
}

def updateLatestTxt() {
    constants.updateLatestTxt(env.gitlabSourceRepoName, env.gitlabTargetBranch, env.gitlabMergeRequestLastCommit)
}

runner.start(env.runChoice) {
    env.repo = env.repo ?: env.gitlabSourceRepoName
    def workflow = [
        'checkout': [ closure: { fetchCode() } ],
        'build': [ closure: { runBuild() } ]
    ]
    if (env.artifacts) {
        workflow['upload'] = [ closure: { upload() } ]
        if (env.gitlabActionType == 'PUSH') {
            workflow['updateLatestTxt'] = [ closure: { updateLatestTxt() } ]
        }
    }
    runPipeline(workflow)
}
