@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

env.repo = 'm3d_cts'

runner.start(env.runChoice) {
    def config = commonLib.loadConfig('m3d_cts/master.yaml')

    def workflow = [:]
    def tests = config.tests
    for (testConfig in tests) {
        def _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def parameters = [
            triggerInfo: env.triggerInfo,
            umdPackageUrl: constants.genLatestPackageUrl('gr-umd', 'develop', 'x86_64-mtgpu_linux-xorg-release-hw-m3d.tar.gz')
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }

        workflow["${_testConfig.name}"] = [
            job: _testConfig.job,
            parameters: parameters,
            setGitlabStatus: true
        ]
    }

    runPipeline(workflow)
}
