@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

def linux_build(config) {
    def buildTasks = [:]
    def builds = config.builds ?: []
    for (buildConfig in builds) {
        def _buildConfig = buildConfig
        def defaultParameters = _buildConfig.parameters ?: [:]
        def name = _buildConfig.name ?: _buildConfig.job
        def parameters = [
            repo: env.gitlabTargetRepoName,
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            triggerInfo: env.triggerInfo,
            testLabel: name
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }

        if (_buildConfig.name.startsWith('linux/')) {
            buildTasks["${_buildConfig.name ?: _buildConfig.job}"] = {
                runPipeline.runJob([
                    job: "${_buildConfig.job}",
                    parameters: parameters,
                    setGitlabStatus: true,
                    async: true
                ])
            }
        }
    }

    parallel buildTasks
}

def windows_build(config) {
    def buildTasks = [:]
    def builds = config.builds ?: []
    for (buildConfig in builds) {
        def _buildConfig = buildConfig
        def defaultParameters = _buildConfig.parameters ?: [:]
        def name = _buildConfig.name ?: _buildConfig.job
        def parameters = [
            repo: env.gitlabTargetRepoName,
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            triggerInfo: env.triggerInfo,
            testLabel: name
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }

        if (_buildConfig.name.startsWith('windows/')) {
            buildTasks["${_buildConfig.name ?: _buildConfig.job}"] = {
                runPipeline.runJob([
                    job: "${_buildConfig.job}",
                    parameters: parameters,
                    setGitlabStatus: true,
                    async: true
                ])
            }
        }
    }

    parallel buildTasks
}

runner.start(env.runChoice) {
    def currentDate = new Date().format('yyyy.MM.dd')
    def config = null
    env.commitId = gitLib.fetchCode(env.gitlabTargetRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])

    dir(env.gitlabTargetRepoName) {
        config = commonLib.loadPipelineConfig('.ciConfig.yaml', "${env.gitlabTargetRepoName}/${env.gitlabTargetBranch}.yaml", [
            branch: env.gitlabSourceBranch,
            commitId: env.commitId,
            gitlabActionType: env.gitlabActionType,
            currentDate: currentDate
        ], "${env.gitlabTargetRepoName}/default.yaml")
    }

    def workflow = [
        'linux-build': [ closure: { linux_build(config) }, setGitlabStatus: true, statusName: 'jenkins/linux-build'],
        'windows-build': [closure: { windows_build(config) }, setGitlabStatus: true, statusName: 'jenkins/windows-build']
    ]

    runPipeline(workflow)
}
