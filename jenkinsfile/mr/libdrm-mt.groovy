@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()
env.buildLabel = 'jenkins/linux-ddk-build'

def build(config) {
    def builds = config.builds
    def buildTasks = [:]
    for (buildConfig in builds) {
        Map _buildConfig = buildConfig
        def name = _buildConfig.name ?: _buildConfig.job
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            repo: env.gitlabTargetRepoName,
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            uploadCase: 'true',
            triggerInfo: env.triggerInfo,
            testLabel: name
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        buildTasks["${_buildConfig.name ?: _buildConfig.job}"] = {
            runPipeline.runJob([
                job: "${_buildConfig.job}",
                parameters: parameters
            ])
        }
    }
    parallel buildTasks
}

// use POD instead
runner.start(env.runChoice) {
    def config = null
    // get config from repo libdrm-mt
    // def config = gitLib.getFileContentByApi('libdrm-mt', env.branch, 'buildConfig.yaml', 'yaml')
    gitLib.fetchCode('libdrm-mt', env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])

    dir('libdrm-mt') {
        config = commonLib.loadPipelineConfig('.ciConfig.yaml', "libdrm-mt/${env.gitlabTargetBranch}.yaml", [
            branch: env.gitlabSourceBranch
        ], 'libdrm-mt/default.yaml')
    }

    def workflow = [:]
    if (config.builds && config.builds.size() > 0) {
        workflow['build'] = [ closure: { build(config) }, setGitlabStatus: true, statusName: env.buildLabel ]
        if (config.separateBuildAndTest == 'true' && env.gitlabActionType == 'MERGE') {
            gitLib.addCommentForMR('Only the build tasks will be excuted, add the comment "runtest" if you want to run the test tasks.')
        }
    }
    def tests = config.tests ?: []
    for (testConfig in tests) {
        def _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def name = _testConfig.name ?: _testConfig.job
        def testLabel = defaultParameters.testLabel ?: name
        if (env.gitlabActionType == 'MERGE' && config.separateBuildAndTest == 'true') {
            gitLib.setGitlabStatus(testLabel, 'canceled')
            gitLib.setGitlabStatus(testLabel, 'pending')
        } else {
            def parameters = [
                linuxDdkPackageUrl: constants.genPackageUrl('libdrm-mt', env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, defaultParameters.packageName ?: 'ddk2.0.deb'),
                musaRuntimePackageUrl: constants.genLatestPackageUrl('MUSA-Runtime', 'master', 'musaRuntime.tar.gz'),
                triggerInfo: env.triggerInfo,
                testLabel: name
            ]
            if (defaultParameters.compatDdkBranch) {
                parameters.compatDdkPackageUrl = constants.genLatestPackageUrl('linux-ddk', defaultParameters.compatDdkBranch, defaultParameters.packageName ?: 'ddk2.0.deb')
            }
            defaultParameters.each { key, value ->
                parameters[key] = value
            }

            workflow[name] = [
                job: _testConfig.job,
                parameters: parameters,
                setGitlabStatus: true,
                async: true
            ]
        }
    }

    runPipeline(workflow)
}
