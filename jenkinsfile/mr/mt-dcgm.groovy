@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

// def build(config) {
//     def builds = config.builds
//     def buildTasks = [:]
//     for (buildConfig in builds) {
//         Map _buildConfig = buildConfig
//         def name = _buildConfig.name ?: _buildConfig.job
//         def defaultParameters = _buildConfig.parameters ?: [:]
//         def parameters = [
//             repo: env.gitlabTargetRepoName,
//             branch: env.gitlabSourceBranch,
//             commitId: env.gitlabMergeRequestLastCommit,
//             uploadCase: 'true',
//             triggerInfo: env.triggerInfo,
//             testLabel: name
//         ]
//         defaultParameters.each { key, value ->
//             parameters[key] = value
//         }
//         buildTasks["${_buildConfig.name ?: _buildConfig.job}"] = {
//             runPipeline.runJob([
//                 job: "${_buildConfig.job}",
//                 parameters: parameters
//             ])
//         }
//     }
//     parallel buildTasks
// }

// use POD instead
runner.start(env.runChoice) {
    def config = null
    gitLib.fetchCode('mt-dcgm', env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])

    dir('mt-dcgm') {
        config = commonLib.loadPipelineConfig('.ciConfig.yaml', "mt-dcgm/${env.gitlabTargetBranch}.yaml", [
            branch: env.gitlabSourceBranch,
            gitlabTargetBranch: env.gitlabTargetBranch,
            gitlabMergeRequestIid: env.gitlabMergeRequestIid,
            BUILD_ID: env.build_ID
        ], 'mt-dcgm/default.yaml')
    }

    def workflow = [:]
    def builds = config.builds ?: []
    if (builds.size() > 0) {
        for (buildConfig in builds) {
            def _buildConfig = buildConfig
            def defaultParameters = _buildConfig.parameters ?: [:]
            def name = _buildConfig.name ?: _buildConfig.job
            def parameters = [
                branch: env.gitlabSourceBranch,
                commitId: env.gitlabMergeRequestLastCommit,
                triggerInfo: env.triggerInfo,
                testLabel: name
            ]
            defaultParameters.each { key, value ->
                parameters[key] = value
            }

            workflow["${name}"] = [
                job: _buildConfig.job,
                parameters: parameters,
                setGitlabStatus: true,
                async: true
            ]
        }

        workflow['build complete'] = [
            closure: { println 'build complete' },
            setGitLabStatus: false,
            async: false
        ]
    }
    def tests = config.tests ?: []
    for (testConfig in tests) {
        def _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def name = _testConfig.name ?: _testConfig.job
        def parameters = [
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            triggerInfo: env.triggerInfo,
            testLabel: name
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }

        workflow["${name}"] = [
            job: _testConfig.job,
            parameters: parameters,
            setGitlabStatus: true,
            async: true
        ]
    }

    runPipeline(workflow)
}
