@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

def build(config) {
    def builds = config.builds
    def buildTasks = [:]
    for (buildConfig in builds) {
        if (buildConfig.buildOnlyMerged == 'true' || buildConfig.job?.endsWith('.win')) { continue }
        Map _buildConfig = buildConfig
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            branch: env.gitlabSourceBranch ?: env.branch,
            commitId: constants.formatCommitID(env.commitId),
            triggerInfo: env.triggerInfo
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        buildTasks[_buildConfig.name] = {
            runPipeline.runJob([
                job: _buildConfig.job,
                parameters: parameters
            ])
        }
    }
    parallel buildTasks
}

def runCoverage(coverageOssPath) {
    docker.image(env.dockerImage).inside('-i -u 0:0 --privileged=true -v /dev:/dev -v /sys:/sys') {
        gitLib.fetchCode('mt-management', env.branch, env.commitId)
        def workspace = env.WORKSPACE.replace('/', '\\/')
        oss.install('mtoss', 'mtoss123')
        dir('mt-management') {
            sh """
                mc cp -r ${coverageOssPath} .
                sed -i \\
                    -e 's|SF:/home/<USER>/workspace/test.mt-management_vdi|SF:${workspace}|g' \\
                    -e 's|SF:/home/<USER>/agent/workspace/build.mt-management/mtml_Debug_x86_64|SF:${workspace}|g' \\
                    vdi_report.info qy1_report.info sudi_report.info mpc_report.info
                ./build_ci.sh DEBUG x86_64 1.9.0 YES
                lcov -a sudi_report.info -a qy1_report.info -a mpc_report.info -a vdi_report.info -o report.info --branch-coverage
                lcov --remove report.info '*/include/*' '*/3rdparty/*' '*/test/*' '*/mt-sdk/*' '*/cml/*' '*/fol/*' '*/shared/*' '*/pfm/*' --ignore-errors unused -o report.info --branch-coverage
            """
            summary = sh(script: "genhtml --filter branch,function,line --branch-coverage --erase-functions 'mtmlErrorString|mtmlDeviceGetAdapterName|QyEfuse.*get*|SudiEfuse.*get*|mkisMapNames' -o htmlReport report.info", returnStdout: true)
            print(summary)
            commonLib.publishHTML('htmlReport', '*.html', 'All Coverage Report')
        }
    }
}

runner.start(env.runChoice) {
    def config = null
    env.branch = env.gitlabSourceBranch ?: env.branch
    env.commitId = env.gitlabMergeRequestLastCommit ?: env.commitId

    gitLib.fetchCode('mt-management', env.branch, env.commitId, [disableSubmodules: true])

    dir('mt-management') {
        config = commonLib.loadPipelineConfig(
            '.ciConfig.yaml',
            "mt-management/${env.gitlabTargetBranch}.yaml",
            [commitId: constants.formatCommitID(env.commitId)],
            'mt-management/default.yaml'
        )
    }

    def workflow = [:]
    workflow['build mtml'] = [ closure: { build(config) }, setGitlabStatus: true, statusName: 'jenkins/build' ]

    def tests = config.tests ?: []
    def coverageOssPath = config.coverageOssPath ?: "oss/backup/sdk_team/testdata/coverage_report/${constants.formatCommitID(env.commitId)}/"
    for (testConfig in tests) {
        if (!testConfig.job?.endsWith('.win')) {
            def _testConfig = testConfig
            def defaultParameters = _testConfig.parameters ?: [:]
            def name = _testConfig.name
            def parameters = [
                branch: env.branch,
                commitId: env.commitId,
                coverageOssPath: coverageOssPath,
                triggerInfo: env.triggerInfo,
                testLabel: name
            ]
            defaultParameters.each { key, value ->
                parameters[key] = value
            }

            workflow[name] = [
                job: _testConfig.job,
                parameters: parameters,
                setGitlabStatus: true,
                statusName: parameters['testLabel'],
                async: true
            ]
        }
    }

    if (gitLib.triggeredByMR() && env.runCov == 'true') {
        workflow['Analysis coverage'] = [ closure: { runCoverage(coverageOssPath) }, setGitlabStatus: true, statusName: 'jenkins/analysis_coverage' ]
    }

    runPipeline(workflow)
}
