@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git
import groovy.json.JsonOutput

commonLib = new common()
gitLib = new git()

// https://oss.mthreads.com/sw-build/wddm/develop/${wddmCommitId}
env.linuxDDKBaseUrl = 'https://oss.mthreads.com/sw-build/linux-ddk'
env.wddmBaseUrl = 'https://oss.mthreads.com/sw-build/wddm'
env.repo = 'mtcc'
env.buildLabel = 'jenkins/pkg_builds'

// initialize environment variables
def init() {
    def initParams = [:]
    print('Start build mr')
    initParams['isMr'] = 'true'
    initParams['ossRelPath'] = env.gitlabMergeRequestIid
    initParams['buildBranch'] = env.gitlabSourceBranch
    initParams['buildWddmBranch'] = env.gitlabTargetBranch == 'master' ? 'develop' : env.gitlabTargetBranch
    initParams['buildlinuxDDKBranch'] = env.gitlabTargetBranch
    initParams['mtccCommitId'] = env.gitlabMergeRequestLastCommit[0..8]
    initParams['prefix'] = "${initParams.mtccCommitId}_"
    initParams['wddmBaseUrl'] = env.wddmBaseUrl
    try {
        initParams['linuxDDKBaseUrl'] = env.linuxDDKBaseUrl
        if (env.linuxDDKCommitId) {
            initParams['linuxDDKCommitId'] = env.linuxDDKCommitId[0..8]
        } else {
            new common().runRelyNetwork(3, 10) {
                String ossPath = constants.genLatestOssPath('linux-ddk', initParams.buildlinuxDDKBranch)
                String linuxDDKPkgURL = sh(script: "curl --insecure ${constants.genOssPrefix(ossPath)}/${ossPath}/latest.txt", returnStdout: true).trim()
                initParams['linuxDDKCommitId'] = linuxDDKPkgURL.split('/')[-1][0..8]
            }
        }
    } catch (exc) {
        print("Error: ${exc.message}")
    }
    print("initParams: ${initParams}")

    return initParams
}

def testAfterBuild(testsConfig, initParams) {
    for (testConfig in testsConfig) {
        def _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def testname = _testConfig.name ?: _testConfig.job

        def parameters = [
            triggerInfo: env.triggerInfo,
            testLabel: testname
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value in Map || value in List ? JsonOutput.toJson(value) : value
        }
        initParams.each { key, value ->
            parameters[key] = value in Map || value in List ? JsonOutput.toJson(value) : value
        }
        runPipeline.runJob([
            wait: false,
            job: _testConfig.job,
            parameters: parameters,
        ])
    }
}

def testAftertest(testsConfig, initParams) {
    def testAftertestTasks = [:]
    for (testConfig in testsConfig) {
        def _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def testname = _testConfig.name ?: _testConfig.job

        def parameters = [
            triggerInfo: env.triggerInfo,
            testLabel: testname,
            mtccLitPackageUrl: constants.genPackageUrl(env.repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, defaultParameters.mtccLitPackageName ?: 'mtcc_lit.tar.gz'),
            mtccPackageUrl: constants.genPackageUrl(env.repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, defaultParameters.mtccPackageName ?: 'mtcc-x86_64-linux-gnu-ubuntu.tar.gz'),
            linuxDdkPackageUrl: "https://oss.mthreads.com/release-ci/computeQA/cuda_compatible/CI/stable/${defaultParameters.linuxDdkBranch}/musa_Ubuntu_amd64.deb",
            // musaRuntimePackageUrl: constants.genLatestPackageUrl('MUSA-Runtime', 'master', 'musaRuntime.tar.gz'),
            musaRuntimePackageUrl: constants.stableMusaRuntime,
        ]
        if (defaultParameters.packageDependency) {
            Map packagedependency = readJSON text: defaultParameters.packageDependency
            packagedependency.each { paramName, packageName ->
                parameters[paramName] = constants.genPackageUrl(env.repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, packageName)
            }
        }
        defaultParameters.each { key, value ->
            parameters[key] = value in Map || value in List ? JsonOutput.toJson(value) : value
        }
        initParams.each { key, value ->
            parameters[key] = value in Map || value in List ? JsonOutput.toJson(value) : value
        }
        testAftertestTasks[testname] = {
            runPipeline.runJob([
                job: _testConfig.job,
                parameters: parameters
            ])
        }
    }
    parallel testAftertestTasks
}

def build(builds, initParams) {
    def buildTasks = [:]
    for (buildConfig in builds) {
        if (buildConfig.job?.endsWith('.win')) { continue }
        Map _buildConfig = buildConfig
        def name = _buildConfig.name ?: _buildConfig.job
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            triggerInfo: env.triggerInfo,
            testLabel: name,
            mtccPackageUrl: constants.genPackageUrl(env.repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, defaultParameters.mtccPackageName ?: 'mtcc-x86_64-linux-gnu-ubuntu.tar.gz'),
            linuxDdkPackageUrl: "https://oss.mthreads.com/release-ci/computeQA/cuda_compatible/CI/stable/${defaultParameters.linuxDdkBranch}/musa_Ubuntu_amd64.deb",
            // musaRuntimePackageUrl: constants.genLatestPackageUrl('MUSA-Runtime', 'master', 'musaRuntime.tar.gz'),
            musaRuntimePackageUrl: constants.stableMusaRuntime,
        ]
        if (_buildConfig.job == 'build.mtcc') {
            parameters['branch'] = env.gitlabSourceBranch
            parameters['commitId'] = env.gitlabMergeRequestLastCommit
        }
        defaultParameters.each { key, value ->
            parameters[key] = parameters[key] = value in Map || value in List ? JsonOutput.toJson(value) : value
        }
        initParams.each { key, value ->
            parameters[key] = value in Map || value in List ? JsonOutput.toJson(value) : value
        }
        buildTasks[name] = {
            runPipeline.runJob([
                job: _buildConfig.job,
                parameters: parameters
            ])
            if (_buildConfig.tests) {
                testAfterBuild(_buildConfig.tests, initParams)
            }
        }
    }
    parallel buildTasks
}

def test(tests, initParams) {
    def testTasks = [:]
    for (testConfig in tests) {
        if (!testConfig.job?.endsWith('.win')) {
            def _testConfig = testConfig
            def defaultParameters = _testConfig.parameters ?: [:]
            def name = _testConfig.name ?: _testConfig.job

            def parameters = [
                triggerInfo: env.triggerInfo,
                testLabel: name,
                mtccLitPackageUrl: constants.genPackageUrl(env.repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, defaultParameters.mtccLitPackageName ?: 'mtcc_lit.tar.gz'),
                mtccPackageUrl: constants.genPackageUrl(env.repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, defaultParameters.mtccPackageName ?: 'mtcc-x86_64-linux-gnu-ubuntu.tar.gz'),
                linuxDdkPackageUrl: "https://oss.mthreads.com/release-ci/computeQA/cuda_compatible/CI/stable/${defaultParameters.linuxDdkBranch}/musa_Ubuntu_amd64.deb",
                // musaRuntimePackageUrl: constants.genLatestPackageUrl('MUSA-Runtime', 'master', 'musaRuntime.tar.gz'),
                musaRuntimePackageUrl: constants.stableMusaRuntime,
            ]
            if (defaultParameters.packageDependency) {
                Map packagedependency = readJSON text: defaultParameters.packageDependency
                packagedependency.each { paramName, packageName ->
                    parameters[paramName] = constants.genPackageUrl(env.repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, packageName)
                }
            }
            defaultParameters.each { key, value ->
                parameters[key] = value in Map || value in List ? JsonOutput.toJson(value) : value
            }
            initParams.each { key, value ->
                parameters[key] = value in Map || value in List ? JsonOutput.toJson(value) : value
            }
            testTasks[name] = {
                runPipeline.runTask(name, [
                    job: _testConfig.job,
                    parameters: parameters,
                    setGitlabStatus: true,
                    statusName: parameters['testLabel'],
                ])
                if (_testConfig.tests) {
                    runPipeline.runTask("${name}SubTest", [
                        closure: { testAftertest(_testConfig.tests, initParams) },
                        setGitlabStatus: true,
                        statusName: parameters['testLabel']])
                }
            }
        }
    }
    parallel testTasks
}

// use POD instead
runner.start(env.runChoice) {
    def config = null
    def initParams = env.gitlabTargetBranch.toLowerCase().contains('kuae') ? [:] : init()
    // get config from repo mtcc
    // def config = gitLib.getFileContentByApi('mtcc', env.branch, 'buildConfig.yaml', 'yaml')
    gitLib.fetchCode('mtcc', env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])

    dir('mtcc') {
        config = commonLib.loadPipelineConfig('.ciConfig.yaml', "mtcc/${env.gitlabTargetBranch}.yaml", [
            gitlabMergeRequestIid: env.gitlabMergeRequestIid
        ], 'mtcc/default.yaml')
    }

    def workflow = [:]
    print("config: ${config}")
    workflow['build'] = [ closure: { build(config.builds, initParams) }, setGitlabStatus: true, statusName: env.buildLabel]
    workflow['test'] = [ closure: { test(config.tests, initParams) }]
    runPipeline(workflow)
}
