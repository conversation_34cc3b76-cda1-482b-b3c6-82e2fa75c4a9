@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git
import groovy.transform.Field

commonLib = new common()
gitLib = new git()

env.repo = 'DirectStream'
@Field def ddkCommitIdMap = [:]
@Field def ddkUmdBranchMap = ['master':'develop']

def build(config) {
    def builds = config.builds
    def buildTasks = [:]
    for (buildConfig in builds) {
        if (buildConfig.buildOnlyMerged == 'true' || buildConfig.job?.endsWith('.win')) { continue }
        Map _buildConfig = buildConfig
        def name = _buildConfig.name ?: _buildConfig.job
        def defaultParameters = _buildConfig.parameters ?: [:]
        def ddkBranch = defaultParameters['ddkBranch'] ?: ''
        def submoduleMaps = ddkBranch ? constants.getLinuxDdkSubmoduleCommitInfo(defaultParameters['ddkBranch'], ddkCommitIdMap[defaultParameters['ddkBranch']]) : [:]
        def umdPackageUrls = ddkBranch ? "${constants.genPackageUrl('gr-umd', ddkUmdBranchMap[ddkBranch] ?: ddkBranch, submoduleMaps['gr-umd'], 'x86_64-mtgpu_linux-xorg-release-hw.tar.gz', true)},\
                                        ${constants.genPackageUrl('gr-umd', ddkUmdBranchMap[ddkBranch] ?: ddkBranch, submoduleMaps['gr-umd'], 'arm64-mtgpu_linux-xorg-release-hw.tar.gz', true)},\
                                        ${constants.genPackageUrl('gr-umd', ddkUmdBranchMap[ddkBranch] ?: ddkBranch, submoduleMaps['gr-umd'], 'loongarch64-mtgpu_linux-xorg-release-hw.tar.gz', true)},\
                                        ${constants.genPackageUrl('gr-umd', ddkUmdBranchMap[ddkBranch] ?: ddkBranch, submoduleMaps['gr-umd'], 'sw_64-mtgpu_linux-xorg-release-hw.tar.gz', true)}" : "\
                                        ${constants.genLatestPackageUrl('gr-umd', ddkUmdBranchMap[ddkBranch] ?: ddkBranch, 'x86_64-mtgpu_linux-xorg-release-hw.tar.gz')},\
                                        ${constants.genLatestPackageUrl('gr-umd', ddkUmdBranchMap[ddkBranch] ?: ddkBranch, 'arm64-mtgpu_linux-xorg-release-hw.tar.gz')},\
                                        ${constants.genLatestPackageUrl('gr-umd', ddkUmdBranchMap[ddkBranch] ?: ddkBranch, 'loongarch64-mtgpu_linux-xorg-release-hw.tar.gz')},\
                                        ${constants.genLatestPackageUrl('gr-umd', ddkUmdBranchMap[ddkBranch] ?: ddkBranch, 'sw_64-mtgpu_linux-xorg-release-hw.tar.gz')}"

        def parameters = [
            repo: env.gitlabTargetRepoName,
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            triggerInfo: env.triggerInfo,
            testLabel: name,
            umdPackageUrls: umdPackageUrls
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        buildTasks["${_buildConfig.name ?: _buildConfig.job}"] = {
            runPipeline.runJob([
                job: "${_buildConfig.job}",
                parameters: parameters
            ])
        }
    }
    parallel buildTasks
}

// use POD instead
runner.start(env.runChoice) {
    def config = null
    gitLib.fetchCode(env.repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])
    dir(env.repo) {
        config = commonLib.loadPipelineConfig('.ciConfig.yaml', "${env.repo}/${env.gitlabTargetBranch}.yaml", [
            branch: env.gitlabSourceBranch,
            gitlabTargetBranch: env.gitlabTargetBranch,
            gitlabMergeRequestIid: env.gitlabMergeRequestIid,
            BUILD_ID: env.build_ID
        ], "${env.repo}/default.yaml")
    }
    ddkCommitIdMap = constants.getCommitIdfromciConfig(config)
    def workflow = [:]
    workflow['build'] = [ closure: { build(config) }, setGitlabStatus: true, statusName: 'jenkins/build' ]
    def tests = config.tests ?: []
    for (testConfig in tests) {
        if (!testConfig.job?.endsWith('.win')) {
            def _testConfig = testConfig
            def defaultParameters = _testConfig.parameters ?: [:]
            def ddkBranch = defaultParameters['ddkBranch'] ?: ''
            def name = _testConfig.name ?: _testConfig.job
            def parameters = [
                branch: env.gitlabSourceBranch,
                commitId: env.gitlabMergeRequestLastCommit,
                triggerInfo: env.triggerInfo,
                testLabel: name,
                linuxDdkPackageUrl: ddkBranch ? constants.genPackageUrl('linux-ddk', ddkBranch, ddkCommitIdMap[ddkBranch], 'ddk2.0.deb', true) : '',
                directStreamPackageUrl: constants.genPackageUrl(env.repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, defaultParameters.directStreamPackageName ?: 'DirectStream.tar.gz'),
                directStreamTestPackageUrl: constants.genPackageUrl(env.repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, defaultParameters.directStreamTestPackageName ?: 'test_DirectStream.tar.gz'),
            ]
            defaultParameters.each { key, value ->
                parameters[key] = value
            }

            workflow["${name}"] = [
                job: _testConfig.job,
                parameters: parameters,
                setGitlabStatus: true,
                async: true
            ]
        }
    }

    runPipeline(workflow)
}
