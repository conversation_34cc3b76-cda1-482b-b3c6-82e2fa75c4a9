@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()
env.buildLabel = 'jenkins/build'

def build() {
    runPipeline.runJob([
        job: 'build.mtvgm',
        parameters: [
            branch: env.branch,
            commitId: env.commitId,
            triggerInfo: env.triggerInfo
        ]
    ])
}

runner.start(env.runChoice) {
    runPipeline([
        'build': [closure: { build() }, setGitlabStatus: true, statusName: env.buildLabel]
    ])
}
