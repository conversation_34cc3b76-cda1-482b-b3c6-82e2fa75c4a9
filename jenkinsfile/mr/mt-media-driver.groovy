@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

env.repo = 'mt-media-driver'
ddkCommitIdMap = [:]
env.buildLabel = 'jenkins/mt_build'

def build(config) {
    def buildTasks = [:]
    def builds = config.builds
    for (buildConfig in builds) {
        def _buildConfig = buildConfig
        def defaultParameters = _buildConfig.parameters ?: [:]
        def name = _buildConfig.name ?: _buildConfig.job
        def parameters = [
            repo: env.repo,
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            triggerInfo: env.triggerInfo,
            ddkCommitId: ddkCommitIdMap[defaultParameters['ddkBranch']] ?: '',
            testLabel: name
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }

        if (parameters.umdPackages && parameters.umdBranch) {
            parameters.umdPackageUrls = parameters.umdPackages.collect { umdPackage ->
                constants.genLatestPackageUrl('gr-umd', parameters.umdBranch, umdPackage)
            }.join(',')
        }
        parameters = commonLib.removeNonStringValuesOfMap(parameters)

        buildTasks[name] = {
            runPipeline.runJob([
                job: "${_buildConfig.job}",
                parameters: parameters
            ])
        }
    }
    parallel buildTasks
}

runner.start(env.runChoice) {
    def config = null
    stage('checkout') {
        gitLib.fetchCode(env.repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])
        relyKmdBranch = commonLib.findMrDependency('gr-kmd', (env.gitlabMergeRequestTitle ?: '') + (env.gitlabMergeRequestDescription ?: ''))
        relyKmdUrl = ''
        if (relyKmdBranch) {
            relyKmdCommitId = constants.formatCommitID(gitLib.getGitlabRepocommitByApi('gr-kmd', env.relyKmdBranch))
            relyKmdUrl = constants.genPackageUrl('gr-kmd', relyKmdBranch, relyKmdCommitId, 'x86_64-mtgpu_linux-xorg-release-hw.deb')
        }
        dir(env.repo) {
            config = commonLib.loadPipelineConfig('.ciConfig.yaml', "mt-media-driver/${env.gitlabTargetBranch}.yaml", [
                gitlabMergeRequestIid: env.gitlabMergeRequestIid,
                branch: env.gitlabSourceBranch,
                commitId: constants.formatCommitID(env.gitlabMergeRequestLastCommit),
                gitlabTargetBranch: env.gitlabTargetBranch,
                kmdPackageUrl: relyKmdUrl
            ])
        }
    }

    ddkCommitIdMap = constants.getCommitIdfromciConfig(config)

    def workflow = [:]

    if (config.builds && config.builds.size() > 0) {
        workflow['build'] = [ closure: { build(config) }, setGitlabStatus: true, statusName: env.buildLabel ]
    }
    if (relyKmdUrl) {
        workflow['wait kmd package'] = [ closure: { constants.waitPackageUrl(relyKmdUrl) } ]
    }

    def tests = config.tests ?: []
    for (testConfig in tests) {
        def _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def name = _testConfig.name ?: _testConfig.job
        def ddkBranch = defaultParameters['ddkBranch'] ?: ''
        def parameters = [
            linuxDdkPackageUrl: ddkBranch ? constants.genPackageUrl('linux-ddk', ddkBranch, ddkCommitIdMap[ddkBranch], 'ddk2.0.deb', true) : '',
            "${constants.linuxDdkSubmodulePackageVarName[_testConfig.job]}": constants.genPackageUrl(env.repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, 'ddk2.0.tar.gz'),
            testLabel: name,
            triggerInfo: env.triggerInfo
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        if (_testConfig.job ==~ /(CI_.*|DDK_.*)_test_gitlab/) {
            parameters.ghprbActualCommit = env.gitlabMergeRequestLastCommit
        }

        workflow[name] = [
            job: _testConfig.job,
            parameters: parameters,
            setGitlabStatus: true,
            async: true
        ]
    }
    runPipeline(workflow)
}
