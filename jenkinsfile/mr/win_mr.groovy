@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

mrStatuses = []

def shouldRunBasedOnWhitelist(String repoDir, def whitelist, String targetBranch) {
    def shouldRun = true

    if (!whitelist) {
        return shouldRun
    }

    dir(repoDir) {
        def changedFiles = sh(script: "git diff origin/${targetBranch} --name-only --merge-base", returnStdout: true).trim()
        def changedFilesList = changedFiles.split('\n').findAll { it != '' }

        echo "Changed files: ${changedFilesList}"

        def whitelistArray = whitelist instanceof List ? whitelist : [whitelist.toString()]

        shouldRun = commonLib.filterWithList(whitelistArray, changedFilesList, 'whitelist')
        echo "Whitelist check result: ${shouldRun}"
    }

    return shouldRun
}

def toStringParams(params, config = null) {
    def result = [:]
    params.each { k, v ->
        if (v instanceof String) {
            result[k] = v
        } else {
            // Convert non-string values to JSON string for pipeline parameter passing
            def jsonString = groovy.json.JsonOutput.toJson(v)

            // Handle variable expansion in JSON string
            if (config && jsonString.contains('${')) {
                // Find all variables in format ${variableName}
                def variablePattern = /\$\{([^}]+)\}/
                def matcher = jsonString =~ variablePattern
                while (matcher.find()) {
                    def fullMatch = matcher.group(0)      // ${variableName}
                    def variableName = matcher.group(1)   // variableName

                    // Try to get value from config
                    def variableValue = config[variableName]
                    if (variableValue != null) {
                        jsonString = jsonString.replace(fullMatch, variableValue.toString())
                    }
                }
            }

            result[k] = jsonString
        }
    }
    return result
}

def findDepName(builds, dependsOn) {
    builds.find {
        (it.job == dependsOn || it.name == dependsOn) && it.job?.endsWith('.win')
    }?.name ?: dependsOn
}

def isDependencySuccess(depName, futures, started) {
    return !depName || (started[depName] && (!futures[depName] || (futures[depName]?.result?.toString() == 'SUCCESS')))
}

def getWddmDriverInfo(Map config) {
    // Get WDDM driver info from config
    def latestWddmDriverUrl = config.latestWddmDriver
    if (!latestWddmDriverUrl) {
        echo 'No latestWddmDriver in config, skip getWddmDriverInfo'
        return [:]
    }

    def wddmPkgBaseUrl = sh(script: "curl --insecure ${latestWddmDriverUrl}", returnStdout: true).trim()
    def wddmCommitId = wddmPkgBaseUrl.tokenize('/')[-1]
    echo "Found WDDM driver - URL: ${wddmPkgBaseUrl}, commit: ${wddmCommitId}"

    return [
        url: wddmPkgBaseUrl,
        commitId: wddmCommitId
    ]
}

def executeVdiBuild(String guestDriverUrl, String m3dTestUrl, String directstreamTestUrl) {
    if (env.gitlabActionType == 'PUSH') {
        if (env.gitlabSourceRepoName?.toLowerCase() == 'wddm') {
            def params = [
                triggerInfo: env.triggerInfo,
                guest_driver_url: guestDriverUrl,
                m3d_test_url: m3dTestUrl,
                directstream_test_url: directstreamTestUrl
            ]
            runPipeline.runJob([
                job: 'M-vdi-build',
                wait: false,
                parameters: params
            ])
        }
    } else {
        def params = [
            triggerInfo: env.triggerInfo,
            guest_driver_url: guestDriverUrl,
            m3d_test_url: m3dTestUrl,
            directstream_test_url: directstreamTestUrl
        ]
        runPipeline.runJob([
            job: 'win-vdi-build',
            wait: false,
            parameters: params
        ])
    }
}

def build(config) {
    // Build jobs according to config.builds
    def builds = config.builds
    def buildMap = [:]
    builds.each { buildConfig ->
        def name = buildConfig.name ?: buildConfig.job
        buildMap[name] = buildConfig
    }

    def jobFutures = [:]
    def buildTasks = [:]
    buildMap.each { name, buildConfig ->
        if (!buildConfig.job?.endsWith('.win')) { return }

        if (buildConfig.whitelist) {
            def shouldRunBuild = shouldRunBasedOnWhitelist(env.gitlabSourceRepoName, buildConfig.whitelist, env.gitlabTargetBranch)
            if (!shouldRunBuild) {
                echo "Skipping build '${name}' - whitelist filter did not pass"
                return
            }
        }

        if (buildConfig.trigger) {
            def triggers = buildConfig.trigger.split(',').collect { it.trim().toLowerCase() }
            if (!triggers.contains(env.gitlabActionType?.toLowerCase())) { return }
        }
        if (env.gitlabActionType == 'PUSH' && (buildConfig.name.contains('clang-tidy') || buildConfig.name.contains('coverage'))) { return }

        jobFutures[name] = null
        def params = [
            repo: env.gitlabSourceRepoName,
            branch: (env.gitlabActionType == 'PUSH' ? env.gitlabTargetBranch : env.gitlabSourceBranch),
            commitId: env.gitlabMergeRequestLastCommit,
            triggerInfo: env.triggerInfo,
            testLabel: name
        ]

        if (env.gitlabSourceRepoName != 'wddm') {
            try {
                def wddmInfo = getWddmDriverInfo(config)
                if (wddmInfo) {
                    params.wddmDriverUrl = wddmInfo.url
                    params.wddmCommitId = wddmInfo.commitId
                }
            } catch (e) {
                echo "Warning: Failed to get WDDM driver info: ${e.message}"
            }
        }

        params.putAll(toStringParams(buildConfig.parameters ?: [:], config))
        if (buildConfig.driverType) { params.driverType = buildConfig.driverType }
        if (buildConfig.outputDir) { params.outputDir  = buildConfig.outputDir }

        buildTasks[name] = {
            if (buildConfig.dependsOn) {
                def depName = findDepName(builds, buildConfig.dependsOn)
                if (depName && jobFutures[depName]) {
                    def depResult = jobFutures[depName]
                    if (depResult?.result && depResult.result.toString() != 'SUCCESS') {
                        error("[win-mr] Dependency job ${depName} failed, aborting ${name}")
                    }
                }
            }
            echo "[win-mr] running job: ${name}"
            jobFutures[name] = runPipeline.runJob([
                job: buildConfig.job,
                parameters: params
            ])
        }
    }

    def started = [:], futures = [:]

    def canRunJob = { name ->
        // Dependency check for job execution
        def buildConfig = buildMap[name]
        def depName = buildConfig?.dependsOn ? findDepName(builds, buildConfig.dependsOn) : null
        if (!depName) { return true }
        if (!buildTasks.containsKey(depName)) { return true } // If dependency not defined, skip
        return started[depName] && (!futures[depName] || (futures[depName]?.result?.toString() == 'SUCCESS'))
    }

    while (started.size() < buildTasks.size()) {
        def progress = false
        def parallelTasks = [:]
        buildTasks.each { name, task ->
            if (!started[name] && canRunJob(name)) {
                parallelTasks[name] = {
                    started[name] = true
                    futures[name] = task()
                }
                progress = true
            }
        }
        if (parallelTasks) {
            parallel parallelTasks
        }
        if (!progress) {
            buildTasks.each { name, _ ->
                if (!started[name]) {
                    def buildConfig = buildMap[name]
                    def depName = buildConfig?.dependsOn ? findDepName(builds, buildConfig.dependsOn) : null
                    def depDefined = depName ? buildTasks.containsKey(depName) : true
                    def extraMsg = (!depDefined && depName) ? ' [dependency not defined, automatically skipped]' : ''
                    echo "[win-mr] Deadlock: '${name}' waiting for '${depName}', started[depName]=${started[depName]}, futures[depName]=${futures[depName]}${extraMsg}"
                }
            }
            error('[win-mr] Deadlock detected in job dependency graph!')
        }
    }
}
def updateLatestTxt() {
    // Update latest.txt for PUSH action
    if (env.gitlabActionType == 'PUSH') {
        constants.updateLatestTxt(env.gitlabSourceRepoName, env.gitlabTargetBranch, env.gitlabMergeRequestLastCommit)
    }
}

def getMRStatuses() {
    def verbose = env.printMrStatuses == 'true'
    try {
        retry(3) {
            println('[getMRStatuses] Waiting for 10 seconds ...')
            sleep time: 10, unit: 'SECONDS'

            mrStatuses = new git().getGitlabCommitAllStatuses(env.gitlabSourceRepoName, env.gitlabMergeRequestLastCommit, env.gitlabSourceBranch)
            if (verbose) {
                println '[getMRStatuses] Retrieved statuses:'
                mrStatuses.each { status ->
                    println "  name: ${status.name}, status: ${status.status}"
                }
            }
        }
    } catch (e) {
        echo "Error retrieving GitLab commit statuses: ${e.message}"
    }
}

def checkCiStatus(mrStatuses, String statusName) {
    if (mrStatuses.isEmpty()) {
        echo 'No statuses found or unable to retrieve statuses from GitLab.'
        return true
    }

    if (!env.gitlabTriggerPhrase =~ /(?i)cirerun/) {
        return true
    }

    def successfulStatuses = mrStatuses.findAll { it.status == 'success' }

    if (statusName && successfulStatuses.any { it.name.contains("${statusName}") }) {
        echo "Match found for ${statusName}. Skipping test."
        return false
}

    return true
}

runner.start(env.runChoice) {
    def directstreamPackageName = 'directstreamTest.tar.gz'
    def m3dPackageName = 'm3dTest.tar.gz'
    def ffmpegPackageName = 'windows_ffmpeg.tar.gz'
    def sdkgoogletestageName = 'sdkGoogletest'
    def vdiHostPackageName = 'mtgpu-2.9.0.amd64.deb'

    def config = null
    def fetchBranch = (env.gitlabActionType == 'PUSH') ? env.gitlabTargetBranch : env.gitlabSourceBranch
    gitLib.fetchCode(
        env.gitlabSourceRepoName,
        fetchBranch,
        env.gitlabMergeRequestLastCommit,
        [
            disableSubmodules: true,
            lfs: (env.gitlabSourceRepoName == 'dxc')
        ]
    )

    dir(env.gitlabSourceRepoName) {
        // Load pipeline config from repo or fallback yaml
        config = commonLib.loadPipelineConfig('.ciConfig.yaml', "${env.gitlabSourceRepoName}/${env.gitlabTargetBranch}.yaml")
    }

    // Cache directstream base URL to avoid multiple curl calls
    def directstreamBase = null
    def getDirectstreamBase = {
        if (!directstreamBase) {
            def directstreamLatestUrl = config.directstream_latest ?: 'https://oss.mthreads.com/sw-build/DirectStream/develop/latest.txt'
            directstreamBase = sh(script: "curl --insecure ${directstreamLatestUrl}", returnStdout: true).trim()
        }
        return directstreamBase
    }

    // Handle mt-vgpu specific directstream package download and upload
    if (env.gitlabSourceRepoName?.toLowerCase() == 'mt-vgpu' && env.gitlabActionType == 'PUSH') {
        def directstreamSourceUrl = "${getDirectstreamBase()}_${directstreamPackageName}"
        echo "Downloading directstream package from: ${directstreamSourceUrl}"

        sh """
            wget --no-check-certificate -O ${directstreamPackageName} ${directstreamSourceUrl}
        """

        // Upload to MR artifact path using artifact.upload
        artifact.upload(env.gitlabSourceRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, directstreamPackageName)
        echo "Uploaded ${directstreamPackageName} to MR artifact path"

        // Clean up local file
        sh "rm -f ${directstreamPackageName}"
    }

    def workflow = [:]
    workflow['build'] = [
        closure: { build(config) },
        setGitlabStatus: (env.gitlabActionType != 'PUSH'),
        statusName: 'win/build'
    ]

    if (['wddm', 'mt-video-drv', 'mtkmd'].contains(env.gitlabSourceRepoName?.toLowerCase())
        && !(env.gitlabTargetBranch?.toLowerCase()?.contains('release'))) {
        getMRStatuses()
        def skipVdi = (env.gitlabActionType == 'NOTE' && env.gitlabTriggerPhrase =~ /(?i)cirerun/ && !checkCiStatus(mrStatuses, 'vdi_test'))
        workflow['vdi_build'] = [
            closure: {
                if (!skipVdi) {
                    def vdiguestPackageName = "${env.gitlabSourceRepoName}_vdi.tar.gz"
                    def guestDriverUrl = constants.genPackageUrl(env.gitlabSourceRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, vdiguestPackageName)
                    def m3dTestUrl = constants.genPackageUrl(env.gitlabSourceRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, m3dPackageName)
                    def directstreamTestUrl = "${getDirectstreamBase()}_${directstreamPackageName}"
                    echo '[build] executeVdiBuild'
                    executeVdiBuild(
                        guestDriverUrl,
                        m3dTestUrl,
                        directstreamTestUrl
                    )
                }
            }
        ]
        }

    if (env.gitlabActionType != 'PUSH' && config.tests) {
        def usedNames = [] as Set
        def wddmInfo = null
        if (env.gitlabSourceRepoName != 'wddm') {
            try {
                wddmInfo = getWddmDriverInfo(config)
            } catch (e) {
                echo "Warning: Failed to get WDDM driver info: ${e.message}"
            }
        }
        config.tests.each { testConfig ->
            if (testConfig.job?.endsWith('.win')) {
                if (testConfig.trigger) {
                    def triggers = testConfig.trigger instanceof String
                        ? testConfig.trigger.split(',').collect { it.trim().toLowerCase() }
                        : [testConfig.trigger.toString().toLowerCase()]
                    if (!triggers.contains(env.gitlabActionType?.toLowerCase())) { return }
                }

                def params = [triggerInfo: env.triggerInfo]
                params.putAll(toStringParams(testConfig.parameters ?: [:], config))

                if (testConfig.driverType) {
                    // Common parameters for test jobs
                    params.testName = params.testName ?: (testConfig.job?.tokenize('.')?.getAt(1) ? "${testConfig.job.tokenize('.')[1]}_test" : '')
                    params.driverType = testConfig.driverType
                    params.statusName = testConfig.name

                    // Package names
                    def driverPackageName = "${env.gitlabSourceRepoName}_${testConfig.driverType}.tar.gz"

                    // Set only once per repo
                    switch (env.gitlabSourceRepoName) {
                        case 'diagsys-ci':
                            break
                        case 'FFmpeg':
                            params.ffmpgeUrl = constants.genPackageUrl(env.gitlabSourceRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, ffmpegPackageName)
                            break
                        case 'mt-management':
                            params.sdkGoogle_test_url = constants.genPackageUrl(env.gitlabSourceRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, sdkgoogletestageName)
                            break
                        default:
                            params.driverUrl = constants.genPackageUrl(env.gitlabSourceRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, driverPackageName)
                    }

                    // directstream_test_url
                    if (env.gitlabSourceRepoName == 'DirectStream') {
                        params.directstream_test_url = constants.genPackageUrl(env.gitlabSourceRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, directstreamPackageName)
                    } else {
                        params.directstream_test_url = "${getDirectstreamBase()}_${directstreamPackageName}"
                    }

                    // m3d_test_url
                    if (['wddm', 'm3d'].contains(env.gitlabSourceRepoName?.toLowerCase())) {
                        params.m3d_test_url = constants.genPackageUrl(env.gitlabSourceRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, m3dPackageName)
                    } else if (['mt-vgpu'].contains(env.gitlabSourceRepoName?.toLowerCase())) {
                        params.hostDriverPkgUrl = constants.genPackageUrl(env.gitlabSourceRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, vdiHostPackageName)
                    } else if (wddmInfo) {
                        params.m3d_test_url = "${wddmInfo.url}_${m3dPackageName}"
                    }
                }

                // Add global test configs if present
                if (config.testConfigs) {
                    config.testConfigs.each { k, v ->
                        params[k] = (v instanceof Map || v instanceof List) ? groovy.json.JsonOutput.toJson(v) : v
                    }
                }

                // Check for duplicate testConfig.name
                if (usedNames.contains(testConfig.name)) {
                    error "[win-mr] Duplicate testConfig.name found: ${testConfig.name}"
                }
                usedNames << testConfig.name
                echo "[win-mr] Triggering test job: ${testConfig.name} for driverType: ${testConfig.driverType}"

                workflow["${testConfig.name}"] = [
                    job: testConfig.job,
                    parameters: params,
                    setGitlabStatus: true,
                    async: true
                ]
            }
        }
    }

    workflow['updateLatestTxt'] = [
        closure: {
            try {
                updateLatestTxt()
            } catch (e) {
                echo "updateLatestTxt failed: ${e.message}"
            }
        }
    ]

    runPipeline(workflow)
}
