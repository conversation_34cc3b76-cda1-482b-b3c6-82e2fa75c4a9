@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()
env.buildLabel = 'jenkins/build'

def build(config) {
    def builds = config.builds ?: []
    def buildTasks = [:]
    for (buildConfig in builds) {
        Map _buildConfig = buildConfig
        def name = _buildConfig.name ?: _buildConfig.job
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            repo: env.gitlabTargetRepoName,
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            triggerInfo: env.triggerInfo,
            testLabel: name
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        buildTasks[name] = {
            runPipeline.runJob([
                job: _buildConfig.job,
                parameters: parameters
            ])
        }
    }
    parallel buildTasks
}

runner.start(env.runChoice) {
    def config = null
    gitLib.fetchCode(env.gitlabTargetRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])

    dir(env.gitlabTargetRepoName) {
        config = commonLib.loadPipelineConfig('.ciConfig.yaml', "${env.gitlabTargetRepoName}/${env.gitlabTargetBranch}.yaml")
    }

    def workflow = [:]
    workflow['build'] = [ closure: { build(config) }, setGitlabStatus: true, statusName: env.buildLabel ]
    def tests = config.tests ?: []
    for (testConfig in tests) {
        def _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def name = _testConfig.name ?: _testConfig.job
        def parameters = [
            triggerInfo: env.triggerInfo,
            testLabel: name
        ]
        if (_testConfig.job ==~ /(CI_.*|DDK_.*)_test_gitlab/) {
            parameters['branch'] = 'develop'
            parameters['ghprbTargetBranch'] = 'develop'
            parameters['fwRepo'] = env.gitlabTargetRepoName
            parameters['ghprbActualCommit'] = env.gitlabMergeRequestLastCommit
        }
        parameters.fwBinUrl = constants.genPackageUrl(env.gitlabTargetRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, defaultParameters.fwPackageName, false, true)
        defaultParameters.each { key, value ->
            parameters[key] = value
        }

        workflow[name] = [
            job: _testConfig.job,
            parameters: parameters,
            setGitlabStatus: true,
            async: true
        ]
    }

    runPipeline(workflow)
}
