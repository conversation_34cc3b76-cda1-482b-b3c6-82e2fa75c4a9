@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

env.repo = 'gr-umd'
env.buildLabel = 'jenkins/build'

def build(config) {
    def builds = config.builds ?: []
    def buildTasks = [:]
    for (buildConfig in builds) {
        Map _buildConfig = buildConfig
        def name = _buildConfig.name ?: _buildConfig.job
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            repo: env.gitlabTargetRepoName,
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            uploadCase: 'true',
            triggerInfo: env.triggerInfo,
            testLabel: name
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        buildTasks["${_buildConfig.name ?: _buildConfig.job}"] = {
            runPipeline.runJob([
                job: "${_buildConfig.job}",
                parameters: parameters
            ])
        }
    }
    parallel buildTasks
}

def uploadCase() {
    dir('gr-umd') {
        if (fileExists('cts')) {
            oss.install()
            oss.cp('cts/*', "oss/sw-build/gr-umd/${env.branch}/test/cts/")
        }
    }
}

def fetchCode() {
    def currentCommit = gitLib.fetchCode('gr-umd', env.branch, env.commitId)
    env.commitId = env.commitId ?: currentCommit
}

runner.start(env.runChoice) {
    // get config from repo gr-umd
    // def config = gitLib.getFileContentByApi('gr-umd', env.branch, 'buildConfig.yaml', 'yaml')
    env.branch = env.gitlabSourceBranch ?: env.branch
    env.commitId = env.gitlabMergeRequestLastCommit ?: env.commitId

    def config = null
    stage('checkout') {
        gitLib.fetchCode('gr-umd', env.branch, env.commitId, [disableSubmodules: true])
        dir('gr-umd') {
            config = commonLib.loadPipelineConfig('.ciConfig.yaml', "gr-umd/${env.gitlabTargetbranch}.yaml", [
                pkgType: env.pkgType ?: 'release',
                branch: env.branch,
                gitlabMergeRequestIid: env.gitlabMergeRequestIid,
                BUILD_ID: env.build_ID
            ], 'gr-umd/default.yaml')
        }
        println config
    }
    stage('upload case') {
        uploadCase()
    }

    def workflow = [:]
    if (config.builds && config.builds.size() > 0) {
        workflow['build'] = [ closure: { build(config) }, setGitlabStatus: true, statusName: env.buildLabel ]
    }

    def tests = config.tests ?: []
    for (testConfig in tests) {
        def _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def name = _testConfig.name ?: _testConfig.job
        // TODO: @蒋波: branch commitId ???
        def parameters = [
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            ossTestResultsSavePath: "release-ci/${_testConfig.name.replaceAll(' ', '_')}/${env.gitlabMergeRequestIid ? 'PR' : 'verify'}",
            musaRuntimePackageUrl: constants.genLatestPackageUrl('MUSA-Runtime', 'master', 'musaRuntime.tar.gz'),
            triggerInfo: env.triggerInfo,
            testLabel: name
        ]

        // check if  defaultParameters.packageName exists
        if (defaultParameters.packageName) {
            parameters.linuxDdkPackageUrl = constants.genPackageUrl(env.repo, env.branch, env.commitId, defaultParameters.packageName ?: 'ddk2.0.deb')
        } else {
            parameters.umdPackageUrl = constants.genPackageUrl(env.repo, env.branch, env.commitId, defaultParameters.umdPackageName ?: 'x86_64-mtgpu_linux-xorg-release-hw-glvnd.tar.gz')
        }

        defaultParameters.each { key, value ->
            parameters[key] = value
        }

        workflow["${name}"] = [
            job: _testConfig.job,
            parameters: parameters,
            setGitlabStatus: true,
            async: _testConfig.async != 'false'
        ]
    }

    runPipeline(workflow)
}
