@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

env.repo = 'vulkan'
ddkCommitIdMap = [:]
env.buildLabel = 'build'

def build(config) {
    def buildTasks = [:]
    def builds = config.builds

    for (buildConfig in builds) {
        if (buildConfig.job?.endsWith('.win')) { continue }
        def _buildConfig = buildConfig
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            repo: env.repo,
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            triggerInfo: env.triggerInfo,
            ddkCommitId: ddkCommitIdMap[defaultParameters['ddkBranch']] ?: '',
            testLabel: _buildConfig.name
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        buildTasks["${_buildConfig.name ?: _buildConfig.job}"] = {
            runPipeline.runJob([
                job: "${_buildConfig.job}",
                parameters: parameters,
            ])
        }
    }

    parallel buildTasks
}

runner.start(env.runChoice) {
    def config = null
    gitLib.fetchCode(env.repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])
    dir(env.repo) {
        config = commonLib.loadPipelineConfig('.ciConfig.yaml', "Vulkan/${env.gitlabTargetBranch}.yaml", [
            branch: env.gitlabSourceBranch,
            gitlabMergeRequestIid: env.gitlabMergeRequestIid,
            BUILD_ID: env.build_ID,
        ])
    }
    ddkCommitIdMap = constants.getCommitIdfromciConfig(config)
    def workflow = [:]
    workflow['build'] = [ closure: { build(config) }, setGitlabStatus: true, statusName: env.buildLabel ]

    def tests = config.tests ?: []
    for (testConfig in tests) {
        if (!testConfig.job?.endsWith('.win')) {
            def _testConfig = testConfig
            def defaultParameters = _testConfig.parameters ?: [:]
            def ddkBranch = defaultParameters['ddkBranch'] ?: ''
            def vulkanPackageName = defaultParameters?.vulkanPackageName ?: ''
            def parameters = [
                vulkanPackageUrl: vulkanPackageName ? constants.genPackageUrl(env.repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, vulkanPackageName) : '',
                linuxDdkPackageUrl: ddkBranch ? constants.genPackageUrl('linux-ddk', ddkBranch, ddkCommitIdMap[ddkBranch], 'ddk2.0.deb', true) : '',
                testLabel: _testConfig.name,
                triggerInfo: env.triggerInfo,
            ]
            defaultParameters.each { key, value ->
                parameters[key] = value
            }

            workflow["${_testConfig.name ?: _testConfig.job}"] = [
                job: _testConfig.job,
                parameters: parameters,
                setGitlabStatus: true,
                async: true
            ]
        }
    }

    runPipeline(workflow)
}
