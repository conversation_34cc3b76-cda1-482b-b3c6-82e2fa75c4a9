@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

env.repo = 'musa'
ddkCommitIdMap = [:]
env.buildLabel = 'jenkins/build'

def build(config) {
    def buildTasks = [:]
    def builds = config.builds
    for (buildConfig in builds) {
        def _buildConfig = buildConfig
        def name = _buildConfig.name ?: _buildConfig.job
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            repo: env.repo,
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            triggerInfo: env.triggerInfo,
            testLabel: name,
            ddkCommitId: ddkCommitIdMap[defaultParameters['ddkBranch']] ?: '',
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        buildTasks[name] = {
            runPipeline.runJob([
                job: "${_buildConfig.job}",
                parameters: parameters
            ])
        }
    }
    parallel buildTasks
}

runner.start(env.runChoice) {
    def config = null
    stage('checkout') {
        gitLib.fetchCode(repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])
        dir(repo) {
            config = commonLib.loadPipelineConfig('.ciConfig.yaml', "MUSA-Runtime/${env.gitlabTargetBranch}.yaml", [
            gitlabMergeRequestIid: env.gitlabMergeRequestIid,
            BUILD_ID: env.build_ID,
            commitId: env.gitlabMergeRequestLastCommit])
        }
    }
    ddkCommitIdMap = constants.getCommitIdfromciConfig(config)
    def workflow = [:]
    if (config.builds && config.builds.size() > 0) {
        workflow['build'] = [ closure: { build(config) }, setGitlabStatus: true, statusName: env.buildLabel ]
    }
    def tests = config.tests ?: []
    for (testConfig in tests) {
        def _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def name = _testConfig.name ?: _testConfig.job
        def ddkBranch = defaultParameters['ddkBranch'] ?: ''
        def parameters = [
            linuxDdkPackageUrl: ddkBranch ? constants.genPackageUrl('linux-ddk', ddkBranch, ddkCommitIdMap[ddkBranch], 'ddk2.0.deb', true) : '',
            "${constants.linuxDdkSubmodulePackageVarName[_testConfig.job]}": constants.genPackageUrl(repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, defaultParameters.packageName ?: (defaultParameters.pkgName ?: 'ddk2.0.tar.gz')),
            musaRuntimePackageUrl: constants.genLatestPackageUrl('MUSA-Runtime', 'master', defaultParameters.musaPackageName ?: 'musaRuntime.tar.gz'),
            testLabel: name,
            triggerInfo: env.triggerInfo,
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        workflow["${name}"] = [
            job: _testConfig.job,
            parameters: parameters,
            setGitlabStatus: true,
            statusName: parameters['testLabel'],
            async: true
        ]
    }

    runPipeline(workflow)
}
