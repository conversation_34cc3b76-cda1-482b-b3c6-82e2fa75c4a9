@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()
env.buildLabel = 'jenkins/linux-build'

def build(config) {
    def builds = config.builds
    def buildTasks = [:]
    for (buildConfig in builds) {
        Map _buildConfig = buildConfig
        def name = _buildConfig.name ?: _buildConfig.job
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            triggerInfo: env.triggerInfo,
            testLabel: name
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        buildTasks[name] = {
            runPipeline.runJob([
                job: _buildConfig.job,
                parameters: parameters
            ])
        }
    }
    parallel buildTasks
}

// use POD instead
runner.start(env.runChoice) {
    def config = null
    // get config from repo mtgpu_snapshot
    // def config = gitLib.getFileContentByApi('mtgpu_snapshot', env.branch, 'buildConfig.yaml', 'yaml')
    gitLib.fetchCode('mtgpu_snapshot', env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])

    dir('mtgpu_snapshot') {
        config = commonLib.loadPipelineConfig('.ciConfig.yaml', "mtgpu_snapshot/${env.gitlabTargetBranch}.yaml", [
            gitlabMergeRequestIid: env.gitlabMergeRequestIid
        ], 'mtgpu_snapshot/default.yaml')
    }

    def workflow = [:]
    workflow['build'] = [ closure: { build(config) }, setGitlabStatus: true, statusName: env.buildLabel]
    def tests = config.tests ?: []
    for (testConfig in tests) {
        def _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def name = _testConfig.name ?: _testConfig.job
        def parameters = [
            triggerInfo: env.triggerInfo,
            testLabel: name
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }

        workflow[name] = [
            job: _testConfig.job,
            parameters: parameters,
            setGitlabStatus: true,
            async: true
        ]
    }

    runPipeline(workflow)
}
