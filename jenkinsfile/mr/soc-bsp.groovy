@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

runner.start(env.runChoice) {
    def config = null
    env.repo = env.gitlabTargetRepoName
    gitLib.fetchCode(env.repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])
    dir(env.repo) {
        config = commonLib.loadPipelineConfig('.ciConfig.yaml', "${env.repo}/${env.gitlabTargetBranch}.yaml")
        println config
    }

    def workflow = [:]
    def builds = config.builds ?: []
    for (buildConfig in builds) {
        def _buildConfig = buildConfig
        def defaultParameters = _buildConfig.parameters ?: [:]
        def name = _buildConfig.name ?: _buildConfig.job
        def parameters = [
            repo: env.repo,
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            triggerInfo: env.triggerInfo,
            testLabel: name
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }

        workflow["${name}"] = [
            job: _buildConfig.job,
            parameters: parameters,
            setGitlabStatus: true,
            async: true
        ]
    }

    if (builds.size() > 0) {
        workflow['build complete'] = [
            closure: { println 'build complete' },
            setGitLabStatus: false,
            async: false
        ]
    }

    def tests = config.tests ?: []
    def defaultTestParams = config.defaultTestParameters ?: []
    for (testConfig in tests) {
        def _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def name = _testConfig.name ?: _testConfig.job
        def parameters = [
            repo: env.repo,
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            triggerInfo: env.triggerInfo,
            testLabel: name
        ]
        defaultTestParams.each { key, value ->
            parameters[key] = value
        }

        defaultParameters.each { key, value ->
            parameters[key] = value
        }

        workflow["${name}"] = [
            job: _testConfig.job,
            parameters: parameters,
            setGitlabStatus: true,
            async: true
        ]
    }

    runPipeline(workflow)
}
