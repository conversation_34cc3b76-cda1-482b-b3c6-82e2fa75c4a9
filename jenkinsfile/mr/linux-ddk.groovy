@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()
env.buildLabel = 'jenkins/linux-ddk-build'

def build(config) {
    def builds = config.builds
    def buildTasks = [:]
    for (buildConfig in builds) {
        Map _buildConfig = buildConfig
        def name = _buildConfig.name ?: _buildConfig.job
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            repo: env.gitlabTargetRepoName,
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            uploadCase: 'true',
            triggerInfo: env.triggerInfo,
            testLabel: name
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        buildTasks["${_buildConfig.name ?: _buildConfig.job}"] = {
            runPipeline.runJob([
                job: "${_buildConfig.job}",
                parameters: parameters
            ])
        }
    }
    parallel buildTasks
}

runner.start(env.runChoice) {
    // get config from repo linux-ddk
    // def config = gitLib.getFileContentByApi('linux-ddk', env.branch, 'buildConfig.yaml', 'yaml')
    env.repo = env.gitlabTargetRepoName ?: env.repo
    env.branch = env.gitlabSourceBranch ?: env.branch
    env.commitId = env.gitlabMergeRequestLastCommit ?: env.commitId

    def config = null
    stage('checkout') {
        gitLib.fetchCode(env.repo, env.branch, env.commitId, [disableSubmodules: true, noTags: true, updateBuildDescription: true])
        dir(env.repo) {
            config = commonLib.loadPipelineConfig('.ciConfig.yaml', "${env.repo}/${env.gitlabTargetbranch}.yaml", [
                branch: env.branch,
                commitId: env.commitId,
                gitlabMergeRequestIid: env.gitlabMergeRequestIid
            ])
        }
        println config
    }

    def workflow = [:]
    workflow['build'] = [ closure: { build(config) }, setGitlabStatus: true, statusName: env.buildLabel ]

    def tests = config.tests ?: []
    for (testConfig in tests) {
        def _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def name = _testConfig.name ?: _testConfig.job
        def vulkanPackageName = defaultParameters?.vulkanPackageName ?: ''
        def parameters = [
            vulkanPackageUrl: vulkanPackageName ? constants.genPackageUrl(env.repo, env.branch, env.commitId, vulkanPackageName) : '',
            linuxDdkPackageUrl: constants.genPackageUrl(env.repo, env.branch, env.commitId, defaultParameters.packageName ?: 'ddk2.0.deb'),
            triggerInfo: env.triggerInfo,
            testLabel: name
        ]
        if (constants.musaChangeBranches.contains(env.gitlabTargetBranch)) {
            parameters.musaRuntimePackageUrl = constants.genLatestPackageUrl('MUSA-Runtime', env.gitlabTargetBranch, 'musaRuntime.tar.gz')
        }
        defaultParameters.each { key, value ->
            parameters[key] = value
        }

        workflow["${name}"] = [
            job: _testConfig.job,
            parameters: parameters,
            setGitlabStatus: true,
            statusName: parameters.testLabel,
            async: true
        ]
    }

    runPipeline(workflow)
}
