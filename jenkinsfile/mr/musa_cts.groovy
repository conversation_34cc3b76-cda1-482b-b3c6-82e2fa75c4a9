@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

env.repo = 'musa_cts'
ddkCommitIdMap = [:]

runner.start(env.runChoice) {
    def config = null
    stage('checkout') {
        gitLib.fetchCode(repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])
        dir(repo) {
            config = commonLib.loadPipelineConfig(
                '.ciConfig.yaml',
                "musa_cts/${env.gitlabTargetBranch}.yaml",
                [
                    gitlabMergeRequestIid: env.gitlabMergeRequestIid,
                    BUILD_ID: env.build_ID,
                    commitId: env.gitlabMergeRequestLastCommit,
                ]
            )
        }
    }
    def workflow = [:]
    def tests = config.tests ?: []
    for (testConfig in tests) {
        def _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def name = _testConfig.name ?: _testConfig.job
        def parameters = [
            testLabel: name,
            branch: env.gitlabSourceBranch,
            musaRuntimePackageUrl: constants.genLatestPackageUrl('MUSA-Runtime', 'master', 'musaRuntime.tar.gz'),
            linuxDdkPackageUrl: constants.genLatestPackageUrl('linux-ddk', defaultParameters.ddkBranch ?: 'master', defaultParameters.packageName ?: 'ddk2.0.deb'),
            musaCtsCommitId: env.gitlabMergeRequestLastCommit,
            triggerInfo: env.triggerInfo,
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        workflow["${name}"] = [
            job: _testConfig.job,
            parameters: parameters,
            setGitlabStatus: true,
            statusName: parameters['testLabel'],
            async: true
        ]
    }

    runPipeline(workflow)
}
