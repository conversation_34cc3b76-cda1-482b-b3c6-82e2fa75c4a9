@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

// runChoice nodeLabel repo branch commitId submodule files uploadPath triggerInfo

def fetchCode() {
    gitLib.fetchCode(env.repo, env.branch, null, [disableSubmodules: true])
}

def uploadCase() {
    dir(env.repo) {
        oss.install()
        if (env.submodule) {
            credentials.runWithCredential('SSH_GITLAB') {
                sh "git submodule update --init ${env.submodule}"
                dir(env.submodule) {
                    oss.cp(env.files, env.uploadPath)
                }
            }
        } else {
            oss.cp(env.files, env.uploadPath)
        }
    }
}

runner.start(env.runChoice) {
    runPipeline([
        'checkout': [closure: { fetchCode() }],
        'upload': [closure: { uploadCase() }]
    ])
}
