@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

/*
 * parameters
 * useLatestDeb (Boolean)
 * linuxDdkPackageUrl (String)
 * kmdPackageUrl (String)
 * umdPackageUrl (String)
 * logAddress (String)
 * submitResult (Boolean)
 * support multiple test
    testConfig (Multiline String):
    {
        "gl4630": {
            "workdir": "/root/gfx_daily/build-mt-opengl-cts-*******/external/openglcts/modules",
            "binary": "glcts",
            "ctsUrl": "https://oss.mthreads.com/release-ci/computeQA/m3d/m3d_ogl_cts_smoke_ci/vulkan-cts-4630.tar.gz",
            "case": "/root/gfx_daily/mt-gfx-test/ogl/daily-passlist-for-ddk20-build-4630.txt"
            "assertOnHWRTrigger": "false"
        },
        "gl_mt_main": {
            "workdir": "/root/gfx_daily/build-mt-main/external/openglcts/modules",
            "binary": "glcts",
            "ctsUrl": "https://oss.mthreads.com/release-ci/computeQA/m3d/m3d_ogl_cts_smoke_ci/vulkan-cts-mt_main.tar.gz",
            "case": "/root/gfx_daily/mt-gfx-test/ogl/daily-passlist-for-ddk20-build-mt-main.txt"
            "assertOnHWRTrigger": "false"
        },
        "gles": {
            "workdir": "/root/gfx_daily/cts_gles",
            "binary": "glcts",
            "ctsUrl": "https://oss.mthreads.com/product-release/cts/gles/opengl-es-cts-3.2.7.0-xorg-intel-ubuntu.tar.gz",
            "case": "/root/gfx_daily/mt-gfx-test/gles/daily_ddk20.txt"
            "assertOnHWRTrigger": "false"
        },
        "vk": {
            "workdir": "/root/gfx_daily/cts_vk",
            "binary": "deqp-vk",
            "ctsUrl": "https://oss.mthreads.com/product-release/cts/vk/vulkan-cts-1.3.1.1-xorg-intel-ubuntu.tar.gz",
            "case": "/root/gfx_daily/mt-gfx-test/vulkan/vk_cts_daily_ddk2.txt"
            "assertOnHWRTrigger": "false"
        }
    }
//  * workdir (String)
//  * binary (String)
//  * ctsUrl (String)
//  * case (String)
*/

def recoverEnv() {
    commonLib.recoverEnv()
    commonLib.checkCtsDeps()
}

def installDriver() {
    if (env.useLatestDeb == 'true') {
        ddk.installLinuxDdkAndSetup()
        return
    }

    if (env.linuxDdkPackageUrl) {
        ddk.installLinuxDdkAndSetup()
    } else {
        ddk.install()
    }
}

def exportDisplay(int retryCount = 3) {
    retry(retryCount) {
        sh 'modprobe mtgpu'
        commonLib.startXorg('/usr/lib/xorg/Xorg')
        timeout(time: 5, unit: 'SECONDS') {
            // sh 'export DISPLAY=:0.0 && glxinfo -B'
            sh 'export DISPLAY=:0.0'
        }
    }
}

def runCase(String workdir, String testBin, String casePath, String logfile, String ctsUrl) {
    try {
        dir('/root/gfx_daily') {
            if (!fileExists("${workdir}/${testBin}")) {
                String ctsPkgName = ctsUrl.split('/')[-1]
                sh """
                    wget -q ${ctsUrl} -O ${ctsPkgName}
                    tar -xvf ${ctsPkgName}
                """
            }
        }
        exportDisplay()
        if (conditions.shouldRunKmemleak()) {
            sh 'echo clear > /sys/kernel/debug/kmemleak'
        }
        sh '''
            nproc; uptime; free -h
        '''
        // nproc查看核心数，uptime查看负载，free -h查看内存使用情况
        Number fdCount = sh(script: 'ulimit -n', returnStdout: true).trim().toInteger()
        String fdCmd = fdCount < 4096 ? 'ulimit -n 4096; ulimit -n' : 'ulimit -n'
        dir(workdir) {
            String caseFilename = casePath.split('/')[-1]
            sh "cp ${casePath} ${caseFilename}"
            if (fileExists("${caseFilename}.qpa")) { sh "rm ${caseFilename}.qpa" }
            sh """
                # print pid for later debug
                ${fdCmd}
                export DISPLAY=:0.0 && cd ${workdir}/ && ./${testBin} --deqp-caselist-file=${caseFilename} --deqp-log-shader-sources=disable --deqp-log-images=disable --deqp-log-filename=${caseFilename}.qpa | tee ${logfile}
            """
        }
    } catch (e) {
        sh 'cat /usr/local/var/log/Xorg.0.log'
        sh 'cat /sys/kernel/debug/musa/gpu00/firmware_trace ||:'
        throw(e)
    } finally {
        if (fileExists(logfile)) { sh "cat ${logfile}" }
        def passRate = sh(script: "awk '/Test run totals:/ {found=1} found' ${logfile}", returnStdout: true).trim()
        def failCases = sh(script: "awk '/Fail \\(/ {print prev; print} {prev=\$0}' ${logfile}", returnStdout: true).trim()
        print(passRate)
        if (failCases) {
            print('FailCases :\n' + failCases)
            sh 'cat /sys/kernel/debug/musa/gpu00/firmware_trace ||:'
        }
    }
}

def runGraphicCts() {
    Map testConfig = readJSON text: env.testConfig
    // 检查testConfig是否包含vk，如果包含则先安装vulkan-tools并运行vulkaninfo
    boolean hasVkConfig = testConfig.keySet().any { key -> key.toLowerCase().contains('vk') }
    if (hasVkConfig) {
        sh 'sudo apt update ||:'
        sh 'sudo apt install -y vulkan-tools ||:'
        sh 'vulkaninfo ||:'
    }
    String logPath = "${env.WORKSPACE}/log"
    sh "mkdir -p ${logPath}"
    String combineLog = "${logPath}/TestResults.log"
    sh "echo > ${combineLog}"
    // AssertOnHWRTrigger when debug
    if (env.assertOnHWRTrigger.toBoolean()) {
        sh 'echo Y > /sys/kernel/debug/musa/apphint/0/AssertOnHWRTrigger'
    }

    // check cts && caselist && script
    dir('/root/gfx_daily') {
        sh 'rm -rf mt-gfx-test'
        gitLib.fetchCode('mt-gfx-test', 'master')
    }
    try {
        int testCount = 0
        testConfig.each { name, config ->
            stage(name) {
                testCount += 1
                String casePath = config.case
                String logfile = "${logPath}/${name}_TestResults.log"
                sh "cp ${casePath} ${logPath}/${name}_caselist.txt"
                catchError {
                    timeout(time: env.ctsTimeout, unit: 'MINUTES') {
                        runCase(config.workdir, config.binary, casePath, logfile, config.ctsUrl)
                        sh "mv ${config.workdir}/*.qpa ${logPath}/TestResults_${name}.qpa"
                    }
                }
                sh 'dmesg -T'
                commonLib.reboot(env.NODE_NAME)
                sh "cat ${logfile} >> ${combineLog}"
                dir('/root/gfx_daily/mt-gfx-test/VK-GL-CTS_caselist/linux/script') {
                    sh """
                        python3 parse_cts_log.py ${logfile}
                    """
                }
            }
        }
        sh """
            for i in \$(seq 1 10); do ! ps -ef |grep Xorg|grep -v grep && break;pkill Xorg;sleep 3;done
        """
    } catch (e) {
        throw(e)
    } finally {
        if (env.logAddress) {
            String logPackage = "${env.build_ID}_graphic_cts_logs.tar.gz"
            sh """
                rm -rf /lib/firmware/musa*
                cd /etc/ && rm -rf musa.ini
                dmesg -T |tee ${logPath}/dmesg.log
                tar czf ${logPackage} ${logPath}
                mv ${logPackage} ${env.WORKSPACE}/
            """
            artifact.uploadTestReport("${env.WORKSPACE}/${logPackage}", "${env.logAddress}/${JOB_NAME}/${BUILD_NUMBER}")
        }
    }
}

def submitResult() {
    if (env.submitResult == 'false') {
        return
    }
    String logPath = "${env.WORKSPACE}/log"
    dir('/root/gfx_daily/mt-gfx-test/VK-GL-CTS_caselist/linux/script') {
        sh """
            pip3 install pymysql --trusted-host nexus.infra.shg1.mthreads.com
            python3 analyze_test_result.py ${logPath}
        """
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'env recovery': [closure: { recoverEnv() }],
        'install driver': [closure: { installDriver() }, maxWaitTime: [time: 15, unit: 'MINUTES']],
        'graphic cts test': [closure: { runGraphicCts() }, setGitlabStatus: true, statusName: env.testLabel],
        'submit result': [closure: { submitResult() }]
    ]
    runPipeline(workflow, [
        post: {
            commonLib.reboot(env.NODE_NAME)
            artifact.uploadLog('/var/log/kern.log')
        }
    ])
}
