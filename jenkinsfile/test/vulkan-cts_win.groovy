@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

def installDriver() {
    ddk.installWinDriver(env.driverUrl, env.vulkanPackageUrl)
}

def runTest() {
    bat """
        wget -q ${env.caseList} -O cts_ci_win.txt
        D:\\vulkan-cts\\deqp-vk.exe --deqp-caselist-file=cts_ci_win.txt
    """
}

runner.start(env.runChoice) {
    def workflow = [
        'installDriver': [closure: { installDriver() }],
        'runTest': [ closure: { runTest() } ]
    ]

    runPipeline(workflow)
}
