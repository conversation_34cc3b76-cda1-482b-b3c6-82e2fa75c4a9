@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

// linuxDdkPackageUrl musaToolkitsPackageUrl

def updateDockerimage() {
    sh """
        docker rmi dcgmbuild ||:
        docker rmi dcgmbuild-x86_64 ||:
        docker pull ${env.containerImage}
        docker pull ${env.relyImage}
        docker tag ${env.containerImage} dcgmbuild-x86_64:latest
        docker tag ${env.relyImage} dcgmbuild:base-gcc11-b3ae23-x86_64
        rm -rf /workspaces ||: && mkdir -p /workspaces
    """
}

def installMtml() {
    oss.install()
    def latestManagement = sh(script: "curl --insecure https://oss.mthreads.com/release-ci/management/${env.mtmlBranch}/latest.txt", returnStdout: true).trim()
    def latestMtmlCommit = (latestManagement =~ /_(\w+)\.tar\.gz$/)[0][1]
    def latestMtmlDeb = sh(script: "mc find oss/release-ci/mtml/${env.mtmlBranch} --name ${latestMtmlCommit}_mtml_*_amd64.deb", returnStdout: true).trim()
    sh """
        mc cp ${latestMtmlDeb} .
        dpkg -i ${latestMtmlCommit}_mtml_*_amd64.deb
        apt-get update ||:
        apt-get install -y libcapture-tiny-perl libdatetime-perl ||:
        mc cp oss/backup/sdk_team/tools/lcov_2.0-2_all.deb .
        dpkg -i lcov_2.0-2_all.deb
    """
}

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.gitlabSourceRepoName, env.branch, env.commitId)
}

def build() {
    dir(env.gitlabSourceRepoName) {
        sh """
            export DCGM_BUILD_INSIDE_DOCKER=1
            ${env.buildCmd}
        """
    }
}

def test() {
    sh """
        export LD_LIBRARY_PATH=${env.WORKSPACE}/mt-dcgm/_out/Linux-amd64-release/lib:\$LD_LIBRARY_PATH
        export LD_LIBRARY_PATH=/usr/local/musa/lib:\$LD_LIBRARY_PATH
        cd  mt-dcgm/_out/build/Linux-amd64-release/testing && ./testdcgmunittests -a
        cd ../
        lcov -d . -c -o result.info --branch-coverage --ignore-error=mismatch --rc check_data_consistency=0 --ignore-error=source --ignore-errors negative
        lcov --remove result.info '*/mt-dcgm/sdk/*' '*/mt-dcgm/nvvs/plugin_src/common/*' '*/mt-dcgm/nvvs/plugin_src/musa_common/*' '*/mt-dcgm/nvvs/include/*' '*/mt-dcgm/modules/common/*' '*/mt-dcgm/common/*' '*/mt-dcgm/testing/*' '*/c++/*' '*/catch2/*' '*/fmt/*' '*/json/*' '*/plog/*' '*/tclap/*' '*/tests/*' '*/_out/*' '*/bw_checker/*' '*/mt_test_common/*' '*/yaml-cpp/*' '*/x86_64-linux-gnu/*' '*/mt-dcgm/dcgmi/testing/*' '*/mt-dcgm/dcgmi/NvcmTCLAP.h' '*/mt-dcgm/dcgmi/DcgmiOutput.h' '*/mt-dcgm/dcgmi/dcgmi_common.h' '*/mt-dcgm/dcgmlib/src/DcgmCacheManager.h' '*/mt-dcgm/dcgmlib/src/DcgmEntityTypes.hpp' '*/mt-dcgm/dcgmlib/src/DcgmHostEngineHandler.h' '*/mt-dcgm/dcgmlib/src/DcgmPfmManager.hpp' '*/mt-dcgm/dcgmlib/src/DcgmTopology.hpp' '*/mt-dcgm/modules/DcgmModule.h' '*/mt-dcgm/dcgmlib/entry_point.h' -o report.info --branch-coverage --ignore-error=unused --ignore-errors inconsistent
        genhtml --filter branch,function,line --branch-coverage -o htmlReport report.info --ignore-errors inconsistent --ignore-errors corrupt
    """
    commonLib.publishHTML('mt-dcgm/_out/build/Linux-amd64-release/htmlReport', '*.html', 'Coverage Report')
}

runner.start(env.runChoice, [pre: {
    runPipeline([
        'setupOnNode': [ closure: {  ddk.installLinuxDdkAndSetup() } ],
        'update docker image': [ closure: { updateDockerimage() } ]
    ], [disablePost: true])
}, main: {
    runPipeline([
        'checkout': [ closure: { fetchCode() } ],
        'build': [ closure: { build() }, setGitlabStatus: true, statusName: "${env.testLabel}" ]
    ], [disablePre: true, disablePost: true])
}, post: {
    runPipeline([
        'install mtml': [ closure: { installMtml() } ],
        'test': [ closure: { test() }, setGitlabStatus: true, statusName: "${env.testLabel}" ]
    ], [disablePre: true])
}])
