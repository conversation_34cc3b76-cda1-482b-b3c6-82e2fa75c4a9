@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

repoName = 'alphacore-hunittest'

def fetchCode() {
    gitLib.fetchCode(repoName, env.branch)
}

def setUpOnNode() {
    commonLib.reboot(env.NODE_NAME)
    // env recovery
    commonLib.recoverEnv()
    ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
}

def setupDepends() {
    def baseDir = "${env.musaToolkitsPackageUrl}".substring(0, "${env.musaToolkitsPackageUrl}".lastIndexOf('/'))
    musa.installMusaToolkits(env.musaToolkitsPackageUrl)
    musa.installmuAlg(env.muAlgPackageUrl)
    musa.installmuThrust(env.muThrustPackageUrl)
    dir("${env.WORKSPACE}") {
        def AlphaCoreHoudiniPackage = env.AlphaCoreHoudiniPackage ? env.AlphaCoreHoudiniPackage : "${baseDir}/others/AlphaCoreHoudini.zip"
        constants.downloadPackage(AlphaCoreHoudiniPackage)
        sh 'pwd && unzip AlphaCoreHoudini.zip'
        sh '''
            cd /opt/hfs19.5/AlphaCore
            echo '{"ip": "**************", "port": 60606}' > /opt/hfs19.5/AlphaCore/axServerConfig.json
        '''

        sh '''
            rm -rf $HOME/houdini19.5/dso ||:
            rm -rf $HOME/houdini19.5/otls ||:
            cd AlphaCoreHoudini_Linux_Musa/19.5.303
            cp -r dso otls $HOME/houdini19.5/
        '''
    }
}

def runTest() {
    dir("${repoName}") {
        sh '/opt/hfs19.5.303/bin/hserver -S http://************:1715'
        sh 'printf "HOUDINI_DSO_ERROR=1" >> $HOME/houdini19.5/houdini.env'
        sh """
            export LD_LIBRARY_PATH=/usr/local/musa/lib;export PATH=${PATH}:/usr/local/musa/bin;
            musaInfo
            python3 HoudiniUnitTest.py
        """
    }
}

def uploadTestResult() {
    dir("${repoName}") {
        junit testResults: 'xml/report.xml'
        archiveArtifacts 'xml/report.xml'
    }
}

runner.start(env.runChoice, [pre: {
    if (env.runChoice == 'node' && env.linuxDdkPackageUrl) {
        stage('setUpOnNode') {
            setUpOnNode()
        }
    }
}, main: {
    def workflow = [:]
    workflow['fetchCode'] = [ closure: { fetchCode() } ]
    workflow['setupDepends'] = [ closure: { setupDepends() }]
    workflow['runTest'] = [ closure: { runTest() }]
    runPipeline(workflow)
}, post: {
    runPipeline([
        'upload result': [closure: { uploadTestResult() }],
    ], [disablePre: true])
}])
