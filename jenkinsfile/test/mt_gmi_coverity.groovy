@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

env.repo = 'mthreads-gmi'

def build() {
    def dockerImage = 'sh-harbor.mthreads.com/sdk/management:v5'
    def buildLists = ['x86_64', 'i386', 'aarch64', 'arm32']
    def buildTasks = [:]

    buildLists.each { name ->
        def pkgName = name
        def idir = "/data/samba/sast/idir-${env.JOB_NAME}-${env.BUILD_NUMBER}/${pkgName}"

        buildTasks[pkgName] = {
            dir(pkgName) {
                docker.image(env.dockerImage ?: dockerImage).inside("-i -u 0:0 -v ${coverity.coverityPath}:/home/<USER>/data:/data") {
                    stage('fetch code') {
                        gitLib.fetchCode(env.repo, env.branch, env.commitId)
                    }
                    stage('build') {
                        dir(env.repo) {
                            sh "/home/<USER>/bin/cov-build --dir ${idir} ./build_ci.sh RELEASE ${pkgName} ${verionNu} NO"
                        }
                    }
                }
                credentials.runWithCredential('COV') {
                    stage('--analyze--') {
                        sh """
                            sudo ${coverity.coverityBinPath}/cov-manage-emit --dir ${idir} reset-host-name
                            sudo ${coverity.coverityBinPath}/cov-analyze --wait-for-license --dir ${idir} ${env.granularity} --strip-path ${env.WORKSPACE}
                            sudo ${coverity.coverityBinPath}/cov-format-errors --dir ${idir} --exclude-files ".*/(usr|mthreads-gmi/mtml)/.*|(.*\\.h)" --html-output html || true
                        """
                    }
                    try {
                        stage('--createReport--') {
                            sh """
                                sudo ${coverity.coverityBinPath}/cov-commit-defects --dir ${idir} --exclude-files ".*/(usr|mthreads-gmi/mtml)/.*|(.*\\.h)" --url http://sh-coverity.mthreads.com:8080/ --user ${username} --password ${password} --stream mthreads-gmi-develop-${pkgName} --preview-report-v2 preview-report.json
                            """
                        }

                        stage('--checkReport--') {
                            if (env.checkSwitch == 'on') {
                                commonLib.loadScript('coverity_check.py', 'coverity', false)
                                sh 'sudo python3 ./coverity_check.py preview-report.json html/index.html'
                                commonLib.loadScript('checkReport_access.sh', 'coverity')
                                sh './checkReport_access.sh preview-report.json'
                            }
                        }
                    } catch (exc) {
                        commonLib.publishHTML('html', 'index.html', "Coverity Results ${pkgName}")
                        throw exc
                    }
                }
            }
        }
    }

    try {
        parallel buildTasks
        coverity.cleanIdir()
    } catch (exc) {
        if (env.checkSwitch != 'on') {
            coverity.cleanIdir()
        }
        throw exc
    }
}

runner.start(env.runChoice, {
    runPipeline([
        'jenkins/coverity test': [ closure: { build() }, setGitLabStatus: true ]
    ])
})
