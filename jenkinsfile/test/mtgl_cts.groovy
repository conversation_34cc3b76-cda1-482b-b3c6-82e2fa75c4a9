@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

// runChoice nodeLabel kmdPackageUrl umdPackageUrl mtglBranch

def installDriver() {
    if (env.musaPackageUrl) {
        println(env.musaPackageUrl)
        ddk.installMusa(env.musaPackageUrl)
    } else {
        ddk.install()
    }
}

def runMtglCtsTest() {
    // install tools
    sh """
        apt install build-essential libx11-dev libxrandr-dev libxinerama-dev libxcursor-dev libxi-dev python3-jinja2 || true
        apt autoremove -y || true
        mkdir -p ${env.WORKSPACE}/B${env.build_ID}_logs
    """
    // fetch code
    gitLib.fetchCode('mtgl_cts', env.mtglBranch, null, [preBuildMerge: false])
    // deploy && build && test && check result
    dir('mtgl_cts') {
        if (env.testMark == 'gatetest') {
            dir('script') {
                credentials.runWithCredential('SSH_GITLAB') {
                    sh """
                        export DISPLAY=:0.0
                        ./install.sh deploy
                        ./install.sh compile
                        ./install.sh ${env.testMark}
                    """
                // integration
                }
            }
            withChecks('Integration Tests') {
                junit 'gatetestresult.xml'
            }
            if (currentBuild.result == 'UNSTABLE') {
                throw new Exception('currentBuild.result : Unstable')
            }
        } else if (env.testMark == 'test') {
            dir('script') {
                credentials.runWithCredential('SSH_GITLAB') {
                    sh """
                        export DISPLAY=:0.0
                        ./install.sh deploy
                        ./install.sh compile
                        ./install.sh ${env.testMark}
                        ./install.sh check
                    """
                }
            }
            // integration
            withChecks('Integration Tests') {
                junit 'mtgl_cts_compatibility33_result.xml'
                junit 'mtgl_cts_core33_result.xml'
                junit 'mtgl_cts_core40_result.xml'
                junit 'mtgl_cts_core41_result.xml'
                junit 'mtgl_cts_core42_result.xml'
                junit 'mtgl_cts_core43_result.xml'
                junit 'mtgl_cts_es1x_result.xml'
            }
            if (currentBuild.result == 'UNSTABLE') {
                throw new Exception('currentBuild.result : Unstable')
            }
        }
    }
}

runner.start(env.runChoice) {
    runPipeline([
        'install driver': [closure: { installDriver() }],
        'build to test': [closure: { runMtglCtsTest() }, setGitlabStatus: true, statusName: "${env.testLabel}"]
    ], [post: { commonLib.reboot(env.NODE_NAME) }])
}
