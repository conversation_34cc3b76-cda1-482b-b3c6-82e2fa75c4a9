@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

commonLib = new common()
gitLib = new git()

chips = ['hg']
models = ['amodel']
driver_modes = ['metaOnly', 'fec']
driver_mode_params_map = ['metaOnly': "${env.metaOnlyParams}", 'fec': "${env.fecParams}"]
envExport = env.exports ? 'export ' + env.exports.split().join(' && export ') : ''
echo " ******** envExport: ${envExport} ******** "

// repo_ddk = 'linux-ddk'
repo_musa_cts = 'musa_cts'
repo_mtcc_test = 'mtcc_test'

// utPath = ''
imagePathhost = '/data/qemu/linux/cts_vps'
imagePathVps = '/root/images'
workPathVps = '/root/test/pkg'
testResultsUrl = "https://oss.mthreads.com/${env.ossTestResultsSavePath}"
sshLoginCmd = 'sshpass -p 123456 ssh 127.0.0.1 -p 10022 -o StrictHostKeyChecking=no'

// 根据 gpuArch 的值映射到对应的 device
deviceMap = [
    'mp_31': 'ph1',
    'mp_32': 'ph1s',
    'mp_41': 'hg',
    'mp_42': 'hs',
]

// 获取 gpuArch 参数值
gpuArch = env.gpuArch
echo " ******** gpuArch: ${gpuArch} ******** "
// 根据映射关系确定 device 的值
device = deviceMap.get(gpuArch, 'mp_41')
echo " ******** device: ${device} ******** "

def downloadImg() {
    // if restart vps exists in following steps, we need to copy vps img to workspace and delete it afterwards
    // vps storage will lost after rebooting vps, when starting vps with option -snapshot
    sh """
        echo ' ******** downloadImg ******** ' ||:
        pwd ||:
        ls -l ||:
        ls -l ${env.WORKSPACE} ||:
        ls -l ${workPathVps} ||:
    """
    if (!fileExists("${imagePathhost}/${env.vpsImg}")) {
        sh """
            mkdir -p ${imagePathhost}; cd ${imagePathhost}
            sudo wget -q --no-check-certificate https://oss.mthreads.com/release-ci/computeQA/vps/vps-img/${env.vpsImg}
            sleep 5
        """
    }
}

def fetchCode() {
    // if (env.gitlabSourceRepoName == 'libdrm-mt') {
    //     gitLib.fetchCode('libdrm-mt', env.branch, env.commitId, [updateBuildDescription: true])
    // } else {
    //     def libdrmBranch = commonLib.findMrDependency('libdrm-mt', (env.gitlabMergeRequestTitle ?: '') + (env.gitlabMergeRequestDescription ?: ''))
    //     gitLib.fetchCode(repo_ddk, env.ddkBranch, null, [preBuildMerge: true, noTags: true, disableSubmodules: true])
    //     credentials.runWithCredential('SSH_GITLAB') {
    //         dir(repo_ddk) {
    //             sh 'git submodule update --init libdrm-mt'
    //             if (libdrmBranch) {
    //                 sh "cd libdrm-mt; git checkout ${libdrmBranch}"
    //             }
    //         }
    //     }
    // }
    // utPath = fileExists(repo_ddk) ? "${repo_ddk}/libdrm-mt/pytest" : 'libdrm-mt/pytest'

    // commitId_linux_ddk = gitLib.fetchCode(repo_ddk, env.branch_ddk, env.commitId)
    sh """
        echo ' ******** fetchCode ******** ' ||:
        pwd ||:
        ls -l ||:
        ls -l ${env.WORKSPACE} ||:
        ls -l ${workPathVps} ||:
    """
    if (env.test_musa_cts == 'true') {
        commitId_musa_cts = gitLib.fetchCode(repo_musa_cts, env.branch_musa_cts)
    }
    if (env.test_mtcc_test == 'true') {
        commitId_mtcc_test = gitLib.fetchCode(repo_mtcc_test, env.branch_mtcc_test)
    }
}

// 安装ddk和mtcc等软件包，编译musa_cts前置步骤
def setUpinDocker() {
    sh """
        echo ' ******** setUpinDocker ******** ' ||:
        pwd ||:
        ls -l ||:
        ls -l ${env.WORKSPACE} ||:
        ls -l ${workPathVps} ||:
        # apt update ||:
        # apt install dkms -y ||:
    """
    // 安装ddk
    try {
        if (env.linuxDdkPackageUrl) {
            sh"""
                wget -q --no-check-certificate ${env.linuxDdkPackageUrl}
                tar -xzf *ddk*.tar.gz
                cd ${env.WORKSPACE}/linux-ddk/build && ./install.sh
            """
        }
    }
    catch (exc) {
        throw new Exception('test failed!')
    }

    // 安装MUSA-Runtime
    if (env.musaRuntimePackageUrl) {
        musa.installMusaRuntime(env.musaRuntimePackageUrl)
    }

    // 安装mtcc
    def dependencies = ['mtcc': env.mtccPackageUrl]
    installDependency(dependencies)

    // 安装musify
    if (env.musifyPackageUrl) {
        musa.installMusify(env.musifyPackageUrl)
    }

    // 安装musaAsm
    constants.downloadAndUnzipPackage(env.musaAsmPackageUrl)
    sh '''
        cp musa_asm/build/bin/* /usr/local/musa/bin/
    '''

    // 安装miniforge
    constants.downloadAndUnzipPackage(env.anacondaPackageUrl, '/home/<USER>')
}

// 编译musa_cts api cases
def compileMusaAndCases() {
    sh """
        echo ' ******** compileMusaAndCases ******** ' ||:
        pwd ||:
        ls -l ||:
        ls -l ${env.WORKSPACE} ||:
        ls -l ${workPathVps} ||:

        cd ${env.WORKSPACE}/musa_cts
        # 清理文件，节省空间
        rm -rf .git* ||:
        rm -rf mtcc/mtcc_test/ ||:
        # 暂时移除编译不通过的case
        rm -rf API_TEST/dt_api_test/src/CE_Atomic/mem_api/CEAtomicAddBF162.cu
        rm -rf hip_test/src/kernel/hipLaunchParm.cu
        rm -rf opencl_cts_on_musa/generic_address_space/test_generic_variable_gentype.cpp

        ${envExport}
        . /home/<USER>/miniforge/etc/profile.d/conda.sh && conda activate mathx

        mcc --version
        gcc --version
        g++ --version
        # apt install g++-12 -y
        # gcc --version
        # g++ --version

        cmake -B build . -DENABLE_OCL_TEST=OFF
        cmake --build build -j48
    """
}

def runVpsTest() {
    sh """
        echo ' ******** runVpsTest ******** ' ||:
        pwd ||:
        ls -l ||:
        ls -l ${env.WORKSPACE} ||:
        ls -l ${workPathVps} ||:
    """

    try {
        docker.image(env.buildImage).inside("--privileged -i -u 0:0 --hostname vps-build -v ${env.WORKSPACE}:${workPathVps}") {
            // apt.updateAptSourcesList()
            sh """
                echo ' ******** docker.image(env.buildImage) ******** ' ||:
                pwd ||:
                ls -l ||:
                ls -l ${env.WORKSPACE} ||:
                ls -l ${workPathVps} ||:
            """

            oss.install('mtoss', 'mtoss123')
            setUpinDocker()
            compileMusaAndCases()
        }

        chips.each { chip ->
            models.each { model ->
                driver_modes.each { driver_mode ->
                    def kmd_insmod_params = driver_mode_params_map[driver_mode]
                    println "chip: ${chip}, model: ${model}, driver_mode: ${driver_mode}, kmd_insmod_param: ${kmd_insmod_params}"

                    docker.image(env.testImage).inside("--privileged -i -u 0:0 --hostname vps-test -v ${env.WORKSPACE}:${workPathVps} -v ${imagePathhost}:${imagePathVps}") {
                        // apt.updateAptSourcesList()
                        sh """
                            echo ' ******** docker.image(env.testImage) ******** ' ||:
                            pwd ||:
                            ls -l ||:
                            ls -l ${env.WORKSPACE} ||:
                            ls -l ${workPathVps} ||:
                        """
                        oss.install('mtoss', 'mtoss123')
                        // commonLib.runRelyNetwork(3, 10) {
                        //     sh 'apt install -y sshpass'
                        // }

                        // 拷贝amodel
                        if (env.AmodlePackageUrl) {
                            sh "mc cp -r ${env.AmodlePackageUrl} /root/workspace/soc_model/release_mode/amodel/hg/"
                        }

                        // 拷贝cmodel  TODO
                        if (env.CmodlePackageUrl) {
                            sh """
                                mc cp -r ${env.CmodlePackageUrl}arch_DOG_release_binary.tar.gz ./
                                tar -xzf arch_DOG_release_binary.tar.gz >/dev/null
                                cd ci_env/transif_binary/
                                cp param_configuration.conf  /root/workspace
                                cp -rf *.so* qemu-system-riscv64 /root/workspace/soc_model/release_mode/cmodel/ph1s/
                            """
                        }

                        // 启动vps
                        sh """
                            /root/run_qemu.pl -r ${env.vpsVersion} -g ${imagePathVps}/${env.vpsImg} -extra_args "-net user,hostfwd=tcp::10022-:22 -net nic -m 8G -smp 4,cores=2,threads=2,sockets=1 -virtfs local,path=${workPathVps},mount_tag=host0,security_model=passthrough,id=host0 --enable-kvm -snapshot" -chip_type ${chip} -mode ${model} -model_mode release -ipc_type direct > ${env.WORKSPACE}/${BUILD_NUMBER}_${chip}_${model}_${driver_mode}_vps.log &
                        """

                        timeout(10) {
                            sh """
                                while ! timeout 10 ${sshLoginCmd}; do
                                    sleep 5
                                done
                            """
                        }

                        // vps内安装ddk、mtcc，进行测试
                        sh """
                            ${sshLoginCmd} "echo ' ******** test in vps ******** ' ||:; pwd ||:; ls -l ||:; ls -l ${env.WORKSPACE} ||:; ls -l ${workPathVps} ||:"
                            ${sshLoginCmd} 'mkdir -p ${workPathVps} && mount -t 9p -o trans=virtio,version=9p2000.L host0 ${workPathVps} ||:'
                            ${sshLoginCmd} 'chmod +x ${workPathVps}/*'
                            ${sshLoginCmd} "echo ' ******** test in vps ******** ' ||:; pwd ||:; ls -l ||:; ls -l ${env.WORKSPACE} ||:; ls -l ${workPathVps} ||:"

                            ${sshLoginCmd} 'dpkg -P musa ||:'
                            # ${sshLoginCmd} 'dpkg -i ${workPathVps}/*_vps_ddk2.0.deb ||:'
                            # ${sshLoginCmd} 'modprobe -rv mtgpu; modprobe -v mtgpu ${kmd_insmod_params}'
                            # ${sshLoginCmd} 'tar -xzf ${workPathVps}/linux-ddk-2.0.tar.gz'

                            # 安装ddk
                            ${sshLoginCmd} 'cd ${workPathVps}/linux-ddk/build && mkdir -p /lib/firmware/mthreads/ && ./install.sh -a 0 -g 1 -m 1 -l 1'
                            ${sshLoginCmd} 'cd ${workPathVps}/linux-ddk/build && rmmod mtgpu; modprobe drm; modprobe drm_kms_helper; modprobe snd; modprobe snd-pcm; insmod mtgpu.ko ${kmd_insmod_params}'

                            # 安装MUSA-Runtime
                            ${sshLoginCmd} 'cd ${workPathVps}/MUSA-Runtime && ./install.sh'

                            # 安装mtcc
                            ${sshLoginCmd} 'cd ${workPathVps}/ && ./install.sh'

                            ${sshLoginCmd} '${envExport}; musaInfo; mcc --version; which clang-tidy ||:; clang-tidy --version ||:'

                            ${sshLoginCmd} '${envExport}; gcc --version; g++ --version; rm -rf /var/lib/dpkg/lock /var/lib/dpkg/lock-frontend'

                            ${sshLoginCmd} "cd ${workPathVps}/musa_cts/pytest/ && ${envExport}; pytest -k 'not (exp_fp64 or round_fp64)' -v . --alluredir=${workPathVps}/results_${chip}_${model}_${driver_mode}_musa_cts --clean-alluredir"
                            # ${sshLoginCmd} "cd ${workPathVps}/musa_cts/pytest/ && ${envExport}; pytest -v . --alluredir=${workPathVps}/results_${chip}_${model}_${driver_mode}_musa_cts  --clean-alluredir"

                            ${sshLoginCmd} 'dmesg -T' ||:
                        """
                    }
                }
            }
        }
    } finally {
        // allure report
        chips.each { chip ->
            models.each { model ->
                driver_modes.each { driver_mode ->
                    def resultsDirMusaCts = "${env.WORKSPACE}/results_${chip}_${model}_${driver_mode}_musa_cts"
                    def reportNameMusaCts = "${chip}_${model}_${driver_mode}_musa_cts_allure_report"
                    if (fileExists(resultsDirMusaCts)) {
                        commonLib.allure(resultsDirMusaCts, reportNameMusaCts)
                    } else {
                        echo "Warning: ${resultsDirMusaCts} not found"
                    }
                }
            }
        }
        // upload log
        oss.install('mtoss', 'mtoss123')
        sh """
            echo ' ******** upload log ******** ' ||:
            pwd && ls -l ||:
            ls -l ${env.WORKSPACE} ||:
            ls -l ${workPathVps} ||:
            cd ${env.WORKSPACE}
            mc cp *_vps.log oss/${env.ossTestResultsSavePath}/
        """
        currentBuild.description += "Test Results PKG: ${testResultsUrl} <br>"
    }
}

runner.start(env.runChoice) {
    runPipeline([
        'downloadImg': [closure: { downloadImg() }],
        'fetchCode': [closure: { fetchCode() }],
        'runVpsTest': [closure: { runVpsTest() }, setGitlabStatus: true, statusName: env.testLabel]
    ])
}
