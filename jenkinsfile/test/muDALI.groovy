@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * branch mtcc_test branch - default 'master'
 * linuxDdkPackageUrl (String) - default ''
 * mtccPackageUrl (String) - default ''
 * mtccLitPackageUrl (String) - default ''
 * musifyPackageUrl (String) - default 'https://oss.mthhreads.com/release-ci/computeQA/tools/musify.tar'
 * muAlgPackageUrl (String) - default 'https://oss.mthreads.com/release-ci/computeQA/mathX/newest/muAlg.tar'
 * muThrustPackageUrl (String) - default 'https://oss.mthreads.com/release-ci/computeQA/mathX/newest/muThrust.tar'
 * anacondaPackageUrl(String) - 'release-ci/computeQA/tools/musify.tar;oss/release-ci/computeQA/ai-rely-pkg/miniforge/miniforge_mathx.tar.gz'
 * compileArgs(String) - ''
 * gCover(boolean) - 'false'
 * exports (Multiline String) default 'usual export', split by ';'
 * testType (String) - default 'smoke'
 * testArgs (String) -default '--device=quyuan2'
 * reportOssPath (String) - default 'oss/release-ci/computeQA/tmp/'
 * runChoice (Choice) - node [node | pod]
 * nodeLabel (Choice) - ''
 * containerImage (String) - sh-harbor.mthreads.com/qa/musa_debug:v2
*/

env.repo = 'DALI'
env.testRepo = 'muDALI_cts'
List testEnvs = env.testExports ? env.testExports.split(';') : []
testenvExport = testEnvs ? 'export ' + testEnvs.join(' && export ') : ''
List compileEnvs = env.compileExports ? env.compileExports.split(';') : []
compileenvExport = compileEnvs ? 'export ' + compileEnvs.join(' && export ') : ''

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    env.ctsCommitId = gitLib.fetchCode(env.testRepo, env.ctsBranch, env.ctsCommitId)
}

def setUpOnNode() {
    // install linuxDdk full pkgs and insmod mtgpu
    if (env.runChoice == 'node') {
        ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
    }
}

def setUpinDocker() {
    sh '''
        apt update ||:
        apt install dkms -y ||:
    '''
    ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    constants.downloadAndUnzipPackage(env.anacondaPackageUrl, '/home/<USER>')
    if (env.musaRuntimePackageUrl) {
        musa.installMusaRuntime(env.musaRuntimePackageUrl)
    }
    def dependencies = ['mtcc': env.mtccPackageUrl]
    installDependency(dependencies)
    musa.installmuAlg(env.muAlgPackageUrl)
    musa.installmuThrust((env.muThrustPackageUrl))
    constants.downloadAndUnzipPackage(env.muRANDPackageUrl)
    sh 'cd muRAND && chmod -R 777 . && ./install.sh ||:'
    constants.downloadAndUnzipPackage(env.muPPPackageUrl)
    sh 'cd muPP && chmod -R 777 . && ./install.sh ||:'
    constants.downloadAndUnzipPackage(env.muBLASPackageUrl)
    sh 'cd muBLAS && chmod +x install.sh . && ./install.sh ||:'
    constants.downloadAndUnzipPackage(env.muFFTPackageUrl)
    sh 'dpkg -i muFFT/build/package/mufft*.deb ||:'
    //installgtest
    sh '''
        wget --no-check-certificate https://github.com/google/googletest/archive/refs/tags/release-1.11.0.tar.gz
        tar zxf release-1.11.0.tar.gz
        cd googletest-release-1.11.0 && mkdir build
        cd build && cmake ..
        make && make install
    '''
}

def compileMuDali() {
    timeout(env.TIMEOUT) {
        dir(env.repo) {
            sh """
                ${compileenvExport}
                cmake -DCMAKE_BUILD_TYPE=Release -DBUILD_DALI_NODEPS=OFF -DBUILD_NVTX=OFF -DCMAKE_CXX_COMPILER=/usr/local/musa/bin/clang++ -DWITH_DYNAMIC_MUSA_TOOLKIT=ON -DMUPP_ROOT=/usr/local/musa -DMUSA_ROOT=/usr/local/musa -DBUILD_NVDEC=OFF -DBUILD_NVJPEG=OFF -DBUILD_NVJPEG2K=OFF -DBUILD_NVOF=OFF -DBUILD_NVML=OFF -DBUILD_CUFILE=OFF -DBUILD_NVCOMP=OFF -B build
                cmake --build build -- -j ${env.compileParallel}
            """
        }
    }
}
def runCtsTest() {
    timeout(env.TIMEOUT) {
        dir(env.testRepo) {
            sh """
                ${compileenvExport}
                ${testenvExport}
                export DALI_EXTRA_PATH=${env.WORKSPACE}/DALI_extra
                mkdir -p build && cd build
                cmake .. ${env.buildModule} && make -j ${env.compileParallel}
            """
        }
        // test
        dir(env.testRepo) {
            sh """
                ${compileenvExport}
                ${testenvExport}
                export DALI_EXTRA_PATH=${env.WORKSPACE}/DALI_extra
                ${constants.genCondaActivate('mathx')}
                python run_test.py ${testArgs}
            """
        }
    }
}

def checkResult() {
    dir(env.testRepo) {
        //python run_test.py generate test-report dir include allure report
        commonLib.allure('test-report')
    }
}

def uploadTestResult() {
    dir(env.testRepo) {
        sh 'tar -czvf mudali_cts_allure_result.tar.gz test-report'
        artifact.uploadTestReport('muAlg_cts_allure_result.tar.gz', env.reportOssPath)
    }
}

runner.start(env.runChoice, [main: {
    runPipeline([
        'checkout': [closure: { fetchCode() }],
        'setup in docker': [closure: { setUpinDocker() }],
        'muDALI build': [closure: { compileMuDali() }],
        'muDALI test': [closure: { runCtsTest() }],
    ], [disablePre: true, disablePost: true])
}, post: {
    runPipeline([
        'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
        'upload result': [closure: { uploadTestResult() }]
    ], [disablePre: true])
}, pre: {
    runPipeline([
        'setup pre': [closure: { setUpOnNode() }],
    ], [disablePost: true])
}])
