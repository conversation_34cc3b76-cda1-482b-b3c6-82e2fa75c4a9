@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common
import org.swqa.tools.SshTool

gitLib = new git()
commonLib = new common()

/*
 * exports (Multiline String) default 'usual export', split by ';'
 * linuxDdkPackageUrl (String) - default ''
 * musaToolkitPackageUrl (String) - default ''
 * mcclPackageUrl (String) - default ''
 * reportOssPath (String) - default 'oss path'
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
*/

env.repo = 'mccl_cts'
List envs = env.exports ? env.exports.split(';') : []
envExport = envs ? 'export ' + envs.join(' && export ') : ''
remoteWorkDir = "${env.remoteWorkDir}/${BUILD_NUMBER}/"
ipConfig = readJSON text: env.ipConfig
sshService = new SshTool(this, [user: ipConfig.service.user, ip: ipConfig.service.ip, port: ipConfig.service.port.toInteger(), sshOpts: env.sshOpts])
sshClients = []
ipConfig.client.each { clientInfo ->
    def sshClient = new SshTool(this, [user: clientInfo.user, ip: clientInfo.ip, port: clientInfo.port.toInteger(), sshOpts: env.sshOpts])
    sshClients << sshClient
}

def fetchCode() {
    env.mcclCtsCommit = gitLib.fetchCode(env.repo, env.mcclCtsBranch, env.mcclCtsCommit)
    sh 'tar -czf mccl_cts.tar.gz mccl_cts'
}

def transPackage() {
    sshService.exec("sudo mkdir -p ${remoteWorkDir}")
    mcclPackageName = env.mcclPackageUrl ? env.mcclPackageUrl.split('/').last() : 'mccl.QY2.tar.gz'
    musaToolkitPackageName = env.musaToolkitPackageUrl ? env.musaToolkitPackageUrl.split('/').last() : 'musa_toolkits_install_full.tar.gz'
    linuxDdkPackageName = env.linuxDdkPackageUrl ? env.linuxDdkPackageUrl.split('/').last() : 'musa_Ubuntu_amd64.deb'
    constants.downloadPackage(env.mcclPackageUrl)
    constants.downloadPackage(env.musaToolkitPackageUrl)
    constants.downloadPackage(env.linuxDdkPackageUrl)
    if (env.musaRuntimePackageUrl) {
        musaRuntimePackageName = env.musaRuntimePackageUrl.split('/').last()
        constants.downloadPackage(env.musaRuntimePackageUrl)
        sshService.scpTo(musaRuntimePackageName, remoteWorkDir)
    }
    if (env.mcclPluginpackageUrl) {
        mcclPluginackageName = env.mcclPluginpackageUrl.split('/').last()
        constants.downloadPackage(env.mcclPluginpackageUrl)
        sshService.scpTo(mcclPluginackageName, remoteWorkDir)
    }
    //scp to remote work dir
    sshService.scpTo(mcclPackageName, remoteWorkDir)
    sshService.scpTo(musaToolkitPackageName, remoteWorkDir)
    sshService.scpTo(linuxDdkPackageName, remoteWorkDir)
    sshService.scpTo('mccl_cts.tar.gz', remoteWorkDir)
}

def setUpOnService() {
    sshService.exec("cd ${remoteWorkDir}../../../; ./uninstall_linux_ddk.sh")
    sshService.exec("cd ${remoteWorkDir}../../../; cp install_linux_ddk.sh ${remoteWorkDir}; cd ${remoteWorkDir}; ./install_linux_ddk.sh; sleep 180")
    sshService.exec("cd ${remoteWorkDir}; mkdir -p ${remoteWorkDir}musa_install; tar xzf ${musaToolkitPackageName} -C ${remoteWorkDir}musa_install")
    sshService.exec("cd ${remoteWorkDir}; tar xzf ${mcclPackageName}")
    sshService.exec("cd ${remoteWorkDir}; tar xzf mccl_cts.tar.gz")
    sshService.exec("cd ${remoteWorkDir}musa_install/musa_toolkits_install; rm -rf /usr/local/musa*; ./install.sh")
    sshService.exec("cd ${remoteWorkDir}; cd mccl; ./install.sh")
    if (env.musaRuntimePackageUrl) {
        sshService.exec("cd ${remoteWorkDir}; tar xzf ${musaRuntimePackageName}; cd MUSA-Runtime; ./install.sh")
    }
    if (env.mcclPluginpackageUrl) {
        sshService.exec("cd ${remoteWorkDir}; tar xf ${mcclPluginackageName}; cd *top*enhance; ./install.sh")
    }
}

def setUpOnClients() {
    sshClients.each { client ->
        client.exec("mount ${ipConfig.service.ip}:/data/swqa/swqa_shared_dir /data/swqa/swqa_shared_dir")
        client.exec("cd ${remoteWorkDir}../../../; ./uninstall_linux_ddk.sh")
        client.exec("cd ${remoteWorkDir};./install_linux_ddk.sh; sleep 180")
        client.exec("cd ${remoteWorkDir}musa_install/musa_toolkits_install; rm -rf /usr/local/musa*; ./install.sh")
        client.exec("cd ${remoteWorkDir}; cd mccl; ./install.sh")
        if (env.musaRuntimePackageUrl) {
            client.exec("cd ${remoteWorkDir}; tar xzf ${musaRuntimePackageName}; cd MUSA-Runtime; ./install.sh")
        }
        if (env.mcclPluginpackageUrl) {
            client.exec("cd ${remoteWorkDir}; tar xf ${mcclPluginackageName}; cd *topo*enhance; ./install.sh")
        }
    }
}

def envCheck() {
    sshService.exec("export LD_LIBRARY_PATH=/usr/local/musa/lib/:/usr/lib/x86_64-linux-gnu/:\$LD_LIBRARY_PATH; /usr/local/musa/bin/musaInfo")
    sshClients.each { client ->
        client.exec("export LD_LIBRARY_PATH=/usr/local/musa/lib/:/usr/lib/x86_64-linux-gnu/:\$LD_LIBRARY_PATH; /usr/local/musa/bin/musaInfo")
    }
// sshService.exec("cd ${remoteWorkDir}mccl_cts/scripts/demo/; export LD_LIBRARY_PATH=/usr/local/musa/lib/:/usr/lib/x86_64-linux-gnu/:\$LD_LIBRARY_PATH; /usr/local/musa/bin/mcc axpy.cu -o axpy -mtgpu -lmusart --cuda-gpu-arch=mp_31")
// sshService.exec('sleep 2')
// sshClients.each { client ->
//     client.exec("export LD_LIBRARY_PATH=/usr/local/musa/lib/:/usr/lib/x86_64-linux-gnu/:\$LD_LIBRARY_PATH; ${remoteWorkDir}mccl_cts/scripts/demo/axpy")
// }
}

def mcclCtsRemoteBuild() {
    sshService.exec("cd ${remoteWorkDir}mccl_cts/scripts; ./porting2musa.sh")
    sshService.exec("cd ${remoteWorkDir}mccl_cts; mkdir -p build; cd build; cmake .. ${env.buildArgs}; make -j16")
    sshService.exec("cd ${remoteWorkDir}mccl_cts/scripts; ./mpich3_refine_config_smoke_daily_s5000.sh ${env.nodeNum} ${env.testType} ${ipConfig.client[0].ip}")
}

def mcclCtsRemoteTestApi() {
    sshService.exec("cd ${remoteWorkDir}mccl_cts/pytest; export LD_LIBRARY_PATH=/usr/local/musa/lib:\$LD_LIBRARY_PATH;${env.testEnvVar};${env.apiTestEnv};nohup pytest -v test_mccl_api.py --alluredir=allure_result_mccl_cts > output_api.txt 2>&1 & wait \$!")
}

def mcclCtsRemoteTestSingle() {
    sshService.exec("cd ${remoteWorkDir}mccl_cts/pytest; export LD_LIBRARY_PATH=/usr/local/musa/lib:\$LD_LIBRARY_PATH;${env.testEnvVar};nohup pytest -v ${env.singleNodeTestMark} --ignore=test_mccl_api.py --alluredir=allure_result_mccl_cts > output_single.txt 2>&1 & wait \$! ")
}

def mcclCtsRemoteTestmultiNode() {
    sshService.exec("cd ${remoteWorkDir}mccl_cts/pytest; export LD_LIBRARY_PATH=/usr/local/musa/lib:\$LD_LIBRARY_PATH; ${env.testEnvVar}; nohup pytest -v ${env.multiNodeTestMark} --alluredir=allure_result_mccl_cts> output.txt 2>&1 & wait \$!")
}

def uploadTestResult() {
    sshService.exec("cd ${remoteWorkDir}/mccl_cts/pytest; tar -czvf allure_result_mccl_cts_${BUILD_NUMBER}.tar.gz allure_result_mccl_cts; sudo chmod 777 -R *")
    sshService.scpFrom("${remoteWorkDir}/mccl_cts/pytest/allure_result_mccl_cts_${BUILD_NUMBER}.tar.gz", './')
    artifact.uploadTestReport("allure_result_mccl_cts_${BUILD_NUMBER}.tar.gz", env.reportOssPath)
    sh "tar -xzvf allure_result_mccl_cts_${BUILD_NUMBER}.tar.gz"
    commonLib.allure('allure_result_mccl_cts')
}

runner.start(env.runChoice, [main: {
    runPipeline([
        'checkout': [closure: { fetchCode() }],
        'transPackage': [closure: { transPackage() }],
        'setUpOnService': [closure: { setUpOnService() }],
        'setUpOnClients': [closure: { setUpOnClients() }],
        'envCheck': [closure: { envCheck() }],
        'mcclCtsRemoteBuild': [closure: { mcclCtsRemoteBuild() }],
        'mcclCtsRemoteTestmultiNode': [closure: { mcclCtsRemoteTestmultiNode() }],
        'mcclCtsRemoteTestSingle': [closure: { mcclCtsRemoteTestSingle() }],
        'mcclCtsRemoteTestApi': [closure: { mcclCtsRemoteTestApi() }],
        'uploadTestResult': [closure: { uploadTestResult() }],
    ])
}])
