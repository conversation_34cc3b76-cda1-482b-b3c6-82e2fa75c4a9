@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

env.repo = 'DirectStream'
List envs = env.exports ? env.exports.split(';') : []
envExport = envs ? 'export ' + envs.join(' && export ') : ''
testTimeout = env.testTimeout ?: env.TIMEOUT

def fetchCode() {
    gitLib.installGitLfs()
    withCredentials([sshUserPrivateKey(credentialsId: 'sh-code-ssh', keyFileVariable: 'PKEY')]) {
        withEnv(["GIT_SSH_COMMAND=ssh -i $PKEY -o StrictHostKeyChecking=no"]) {
            sh "git clone -b ${env.VaapiBranch} ************************:sw/vaapi-fits.git"
        }
    }
}

def setUpOnNode() {
    ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
    constants.downloadAndUnzipPackage(env.directStreamPackageUrl)
    sh '''
        mv directstream output
    '''
}

def vaapiTest() {
    dir('vaapi-fits') {
        timeout(testTimeout) {
            sh """
                ${envExport}
                chmod +x ./start_mtenc.sh && ./start_mtenc.sh --lib=${env.WORKSPACE}/output/x86/lib
            """
        }
    }
}

runner.start(env.runChoice, {
    def workflow = [
        'fetch code': [closure: { fetchCode() }],
        'setUp': [closure: { setUpOnNode() }],
        'vaapi test': [closure: { vaapiTest() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
    ]
    runPipeline(workflow)
})
