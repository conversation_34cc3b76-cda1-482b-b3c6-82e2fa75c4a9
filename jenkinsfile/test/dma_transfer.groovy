@Library('swqa-ci')

import org.swqa.tools.common

commonLib = new common()

// runChoice nodeLabel kmdPackageUrl umdPackageUrl

def installDriver() {
    ddk.install()
}

def runDmaTransferTest() {
    def testResult = sh(script: 'cd ddk_umd/x86_64-mtgpu_linux-xorg-release/usr/local/bin && ./dma_transfer_test', returnStdout: true).trim()
    print(testResult)
    if (testResult ==~ /.*FAIL: Buffers don't match.*/) {
        throw new Exception('test failed!')
    }
}

runner.start(env.runChoice) {
    runPipeline([
        'install driver': [closure: { installDriver() }],
        'dma transfer test': [closure: { runDmaTransferTest() }, setGitlabStatus: true, statusName: "${env.testLabel}"]
    ], [
        post: {
            commonLib.reboot(env.NODE_NAME)
            artifact.uploadLog('/var/log/kern.log')
        }
    ])
}
