@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

def fetchLatestDriverInfo() {
    def driverReady = false
    def latest_url_wddm = 'https://oss.mthreads.com/sw-build/wddm/develop/latest_new.txt'
    def maxRetries = 120
    def waitTime = 5

    timeout(time: maxRetries * waitTime, unit: 'MINUTES') {
        while (!driverReady) {
            wddm_pkg_url = sh(script: "curl --insecure ${latest_url_wddm}", returnStdout: true).trim()
            wddm_commitid = wddm_pkg_url.split('/')[-1]

            driverReady = true

            def wddm_pkg_name = "${wddm_commitid}_wddm_hw.tar.gz"
            download_driver_url = "${wddm_pkg_url}/${wddm_pkg_name}"

            def checkUrlCommand = "curl -k --silent --head --fail ${download_driver_url}"
            def urlExists = sh(script: checkUrlCommand, returnStatus: true) == 0

            print(urlExists)

            if (!urlExists) {
                driverReady = false
            }

            if (!driverReady) {
                println 'One or more URLs do not exist yet. Waiting...'
                sleep time: waitTime, unit: 'MINUTES'
            }
        }
    }

    println 'All URLs exist. Proceeding with downloading.'
    return [wddm_commitid: wddm_commitid, wddm_pkg_url: wddm_pkg_url]
}

def preparation() {
    winTest.update_driver(download_driver_url)
    gitLib.fetchCode(env.repo, env.branch, env.commitId)
}

def coveragetest() {
    dir(env.repo) {
        bat """
            ${env.testScript}
        """
        commonLib.publishHTML('build/coverage', 'index.html', 'Coverage Report')
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'fetchLatestDriverInfo': [closure: { fetchLatestDriverInfo() }],

        'preparation': [closure: { preparation() }],

        'coveragetest': [closure: { coveragetest() }]
    ]

    runPipeline(workflow, [disablePost:true])
}
