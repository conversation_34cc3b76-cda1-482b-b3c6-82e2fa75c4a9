@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

// runChoice nodeLabel branch commitId testMark testType compileArgs compileParallelNumber envExport enablePtsz enableAssembler umdPackageUrl mtccPackageUrl musaRuntimePackageUrl musaToolkitsPackageUrl musifyPackageUrl generateTestUrl

musaCtsRepo = 'musa_cts'
generateTestUrl = env.generateTestUrl
env.branch = env.musaCtsBranch ? env.musaCtsBranch : env.branch
env.commitId = env.musaCtsBranch ? '' : env.commitId

def fetchCode() {
    env.musaCtsCommit = gitLib.fetchCode(musaCtsRepo, env.branch, env.musaCtsCommit)
}

def setUpOnNode() {
    if (env.runChoice == 'node') {
        ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
        // this needs to be done on host, not
        if (conditions.shouldRunKmemleak()) {
            sh 'echo clear > /sys/kernel/debug/kmemleak'
        }
    }
}

def installPackage() {
    ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    if (env.kmdPackageUrl) {
        ddk.installLinuxDdk(env.kmdPackageUrl, true)
    }
    if (env.subModulePackageUrl) {
        ddk.installLinuxDdk(env.subModulePackageUrl)
    }
    if (env.musaRuntimePackageUrl) {
        musa.installMusaRuntime(env.musaRuntimePackageUrl)
    }
    installDependency(['mtcc': env.mtccPackageUrl])
    if (env.musifyPackageUrl) {
        musa.installMusify(env.musifyPackageUrl)
    }
    MUSA_PORTING_PATH = "export MUSA_PORTING_PATH=${env.WORKSPACE}/musify"

    if (env.mtTraceCaptureUrl) {
        constants.downloadPackage(env.mtTraceCaptureUrl)
    }
    dir(musaCtsRepo) {
        sh '''
            make install-muAlg install-muThrust
        '''
        if (env.gpuArch == 'mp_31') {
            sh '''
                make install-musa_asm install-mtc_tool
            '''
        }
        if (env.mtTraceCaptureUrl) {
            sh '''
                mv ../mt_trace_capturer ./
                chmod +x mt_trace_capturer
            '''
        }
        sh """
            ${env.envExport}
            ${MUSA_PORTING_PATH}
            cd ./scripts && ./porting_without_compile.sh
            cd ../elf && ./compile_elf.sh
            cd ../musa_samples && ./compile_elf.sh
            cd .. && mkdir build && cd build
            cmake .. ${env.compileArgs}
            make -j 12
            cd googletest && make install
            cd ../../ && make build-ptsz
        """
    }
}

def runCtsTest() {
    timeout(time: env.testTimer, unit: 'MINUTES') {
        dir(musaCtsRepo) {
            sh """
                ${env.envExport}
                ${MUSA_PORTING_PATH}
                cd pytest
                pytest . -n ${env.testParallel} ${env.testMark ? "-m ${env.testMark}" : ''} -v -k "not skip"  --alluredir=allure_result_musa_cts ||:
                tar -czf m3d_musa_cts_allure_result.tar.gz allure_result_musa_cts/
            """
            // TODO: 临时方案, 后续删除 @万鹏 @张羽
            if (env.envExport =~ 'USE_USERQ=') {
                def userqValues = (env.envExport =~ /USE_USERQ=([\d,]+)/)[0][1].split(',')
                userqValues.each { userqValue ->
                    sh """
                        ${env.envExport}
                        export MUSA_USERQ=${userqValue}
                        ${MUSA_PORTING_PATH}
                        cd pytest
                        pytest . ${env.testMark ? "-m ${env.testMark}" : ''} -v -k "not skip"  --alluredir=allure_result_musa_cts_userq_${userqValue} ||:
                        tar -czf m3d_musa_cts_allure_result_userq_${userqValue}.tar.gz allure_result_musa_cts_userq_${userqValue}/
                    """
                }
            }
            if (env.mtTraceCaptureUrl) {
                sh '''
                    mv mt_trace_output ./mt_traces
                    tar -czf mt_traces.tar.gz mt_traces
                '''
            }
        }
    }
}

def checkResult() {
    dir(musaCtsRepo) {
        commonLib.allure('pytest/allure_result_musa_cts')
        if (currentBuild.result == 'UNSTABLE') { error "Test failed, please check: ${allureReportUrl}" }
        if (env.envExport =~ 'USE_USERQ=') {
            def userqValues = (env.envExport =~ /USE_USERQ=([\d,]+)/)[0][1].split(',')
            userqValues.each { userqValue ->
                commonLib.allure("pytest/allure_result_musa_cts_userq_${userqValue}", "allureReportUserQ${userqValue}")
                if (currentBuild.result == 'UNSTABLE') { error "Test failed for USERQ=${userqValue}, please check: ${allureReportUrl}" }
            }
        }
    }
    // this needs to be done on host, not container
    // only kmd pr need this check
    // not every node has the ability to do kmemleak check
    if (conditions.shouldRunKmemleak()) {
        def logAddress = "swci-oss/sw-pr/${env.gitlabSourceRepoName}/${env.gitlabMergeRequestIid}/${env.BUILD_ID}/log"
        def output = sh(script: 'echo scan > /sys/kernel/debug/kmemleak; head -n 500 /sys/kernel/debug/kmemleak | tee kmemleak.log', returnStdout: true).trim()
        def dmesgOut = sh(script: 'dmesg -T | tee dmesgOut.log', returnStdout: true).trim()
        if (output) {
            artifact.uploadTestReport('kmemleak.log', logAddress, 'kmemleak log')
            error("kmemleak found!\nkmemleak output:\n${output}\n")
        }
        if (dmesgOut =~ /BUG: ASAN:/) {
            artifact.uploadTestReport('dmesgOut.log', logAddress, 'dmesgOut log')
            error("BUG: ASAN: found in dmesg!\ndmesg output:\n${dmesgOut}\n")
        }
    }
}

def mttraceTest() {
    // ossLib.cp("${env.WORKSPACE}/${musaCtsRepo}/pytest/m3d_musa_cts_allure_result.tar.gz", generateTestUrl)
    // currentBuild.description += "<br>generate_pkg:${generateTestUrl}/m3d_musa_cts_allure_result.tar.gz".replaceAll('//', '/')
    if (env.mtTraceCaptureUrl) {
        oss.install()
        oss.cp("${env.WORKSPACE}/${musaCtsRepo}/mt_traces.tar.gz", env.traceFileSavePath)
        def ossUrl = constants.ossPathToUrl(env.traceFileSavePath)
        runPipeline.runJob([
            job: 'test.mttrace_replay',
            parameters: [
                'traceDataUrl': "${ossUrl}mt_traces.tar.gz",
                'gpuType': env.gpuArch == 'mp_31' ? 'ph1' : 'qy2',
            ]
        ])
    }
}

runner.start(env.runChoice, [pre: {
    def workflow = [
        'setUpOnNode': [closure: { setUpOnNode() }],
    ]
    runPipeline(workflow, [disablePost: true])
}, main: {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'setup': [closure: { installPackage() }],
        "${env.testLabel}": [closure: { runCtsTest() }]
    ]
    runPipeline(workflow, [disablePost: true, disablePre: true])
}, post: {
    def workflow = [
        'check result': [closure: { checkResult() }, setGitlabStatus: !env.mtTraceCaptureUrl, statusName: "${env.testLabel}"]
    ]
    if (env.mtTraceCaptureUrl) {
        workflow['mttrace test'] = [closure: { mttraceTest() }, setGitlabStatus: true, statusName: "${env.testLabel}"]
    }
    runPipeline(workflow, [
        disablePre: true,
        post: {
            commonLib.reboot(env.NODE_NAME)
            artifact.uploadLog('/var/log/kern.log')
        }
    ])
}])
