@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * branch triton_cts branch - default 'master'
 * linuxDdkPackageUrl (String) - default ''
 * musaToolkitPackageUrl (String) - default ''
 * mcclPackageUrl (String) - default ''
 * mudnnPackageUrl (String) - default ''
 * torchPackageUrl (String) - default ''
 * torchMusaPackageUrl (String) - default ''
 * tritonMusaPackageUrl (String) - default ''
 * muthrustPackageUrl (String) - default ''
 * muAlgPackageUrl (String) - default ''
 * exports (Multiline String) default 'usual export', split by ';'
 * testType (String) - default 'smoke'
 * testMark (String) - default ''
 * reportOssPath (String) - default 'oss/release-ci/computeQA/tmp/'
 * runChoice (Choice) - node [node | pod]
 * nodeLabel (Choice) - ''
 * containerImage (String) - ''
*/

env.repo = 'triton_cts'
List envs = env.exports ? env.exports.split(';') : []
envExport = envs ? 'export ' + envs.join(' && export ') : ''

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    if (env.flaggemsBranch) {
        dir(env.repo) {
            sh 'rm flaggems -rf ||:'
            env.flaggemsCommitId = gitLib.fetchCode('flaggems', env.flaggemsBranch, env.flaggemsCommitId, ['gitlabGroup':'sw-compiler'])
        }
    }
}

def setUpOnNode() {
    // install linuxDdk full pkgs and insmod mtgpu
    if (env.runChoice == 'node') {
        ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
    }
}

def setUpinDocker() {
    sh '''
        apt update
        apt install libcunit1 -y ||:
    '''
    ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    if (env.runChoice == 'node' && env.containerImage == '') {
        try {
            commonLib.restartLinuxNode()
        } finally {
            sleep time: 3, unit: 'MINUTES'
        }
    }

    if (env.musaSdkPackageUrl) {
        sh """
            dpkg -P musa-sdk ||:
            wget ${env.musaSdkPackageUrl}
            dpkg -i ${env.musaSdkPackageUrl.split('/')[-1]}
        """
    }
    else {
        musa.installMusaToolkits(env.musaToolkitPackageUrl)
        constants.downloadAndUnzipPackage(env.mudnnPackageUrl)
        sh 'cd mudnn && ./install_mudnn.sh -i'
    }

    constants.downloadAndUnzipPackage(env.mcclPackageUrl)
    sh 'cd mccl && ./install.sh'

    if (env.muAlgPackageUrl) {
        musa.installmuAlg(env.muAlgPackageUrl)
    }
    else {
        gitLib.fetchCode('muAlg', 'develop')
        dir('muAlg') {
            sh './mt_build.sh -i'
        }
    }
    if (env.muThrustPackageUrl) {
        musa.installmuThrust(env.muThrustPackageUrl)
    }
    else {
        gitLib.fetchCode('muThrust', 'develop')
        dir('muThrust') {
            sh './mt_build.sh -i'
        }
    }

    constants.downloadPackage(env.torchPackageUrl)
    constants.downloadPackage(env.torchMusaPackageUrl)

    constants.downloadAndUnzipPackage(env.tritonMusaPackageUrl)

    if (env.runChoice == 'node' && env.containerImage == '') { //running on M1000
        sh 'pip install ./torch*.whl'
        sh 'cd bdist_wheel && pip install ./triton*.whl'
    }
    else {
        sh '''
            . /root/miniconda3/etc/profile.d/conda.sh
            conda activate py310
            pip install ./torch*.whl
        '''
        sh '''
            . /root/miniconda3/etc/profile.d/conda.sh
            conda activate py310
            cd bdist_wheel && pip install ./triton*.whl
        '''
        if (env.flaggemsBranch) {
            dir("${env.repo}/flaggems") {
                sh '''
                    . /root/miniconda3/etc/profile.d/conda.sh
                    conda activate py310
                    pip install -e . --no-deps
                '''
            }
        }
        constants.downloadAndUnzipPackage('https://oss.mthreads.com/release-ci/computeQA/ai-rely-pkg/triton_musa/triton_rely_so.tar.gz')
        sh 'cp -a triton_rely/* /usr/lib/x86_64-linux-gnu/'
    }
}

def runCtsTest() {
    timeout(env.TIMEOUT) {
        // test
        dir(env.repo) {
            if (env.runChoice == 'node' && env.containerImage == '') { //running on M1000
                sh """
                    ${envExport}
                    python run_test.py -m ${testMark} ||:
                """
            }
            else {
                sh """
                    ${envExport}
                    . /root/miniconda3/etc/profile.d/conda.sh
                    conda activate py310
                    pip install -r requirements.txt
                    python run_test.py -m ${testMark} ||:
                """
            }
        }
    }
}

def checkResult() {
    dir(env.repo) {
        //python run_test.py generate test-report dir include allure report
        commonLib.allure('test-report')
    }
}

def uploadTestResult() {
    dir(env.repo) {
        sh 'tar -czvf triton_cts_allure_result.tar.gz test-report'
        artifact.uploadTestReport('triton_cts_allure_result.tar.gz', env.reportOssPath)
    }
}

runner.start(env.runChoice, [main: {
    runPipeline([
        'checkout': [closure: { fetchCode() }],
        'setup in docker': [closure: { setUpinDocker() }],
        'triton test': [closure: { runCtsTest() }],
    ], [disablePre: true, disablePost: true])
}, post: {
    runPipeline([
        'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
        'upload result': [closure: { uploadTestResult() }]
    ], [disablePre: true])
}, pre: {
    runPipeline([
        'setup pre': [closure: { setUpOnNode() }],
    ], [disablePost: true])
}])
