@Library('swqa-ci')

import org.swqa.tools.common

commonLib = new common()

// nodeLabel runChoice ddkUrl mtccPackageUrl musaRuntimePackageUrl

def installPackage() {
    if (env.ddkUrl) {
        constants.downloadPackage(env.ddkUrl)
        sh 'dpkg -i ddk*.deb && sleep 5 && modprobe -rv mtgpu && modprobe -v mtgpu && lsmod | grep mtgpu ||:'
    }
    if (env.musaRuntimePackageUrl) {
        constants.downloadAndUnzipPackage(env.musaRuntimePackageUrl)
        sh '''
            cd MUSA-Runtime
            ./install.sh
        '''
    }
    if (env.mtccPackageUrl) {
        constants.downloadAndUnzipPackage(env.mtccPackageUrl)
        sh '''
            mkdir -p /usr/local/musa
            ./install.sh
        '''
    }
}

def runTest() {
    dir('pdump_test') {
        def device = '50'
        sh 'lspci ||:'
        def device1 = sh(script: 'lspci | grep 1ed5 | head -1 | awk -F \'led5:\' \'{print $NF}\' ||:', returnStdout: true).trim()
        if (device1) {
            device = device1
        }
        def device2 = sh(script: 'lspci | grep MTT | head -1 | awk -F \'MTT S\' \'{print $NF}\' ||:', returnStdout: true).trim()
        if (device2) {
            device = device2
        }
        switch (device) {
            case ~/01(.*)/:
            case ['10', '30', '50', '2000']:
                gpuArch = 'mp_10'
                break
            case ~/02(.*)/:
            case ['70', '80', '3000']:
                gpuArch = 'mp_21'
                break
            case ~/03(.*)/:
            case ['90', '4000']:
            case '03':
                gpuArch = 'mp_22'
                break
            default:
                gpuArch = 'mp_10'
                break
        }
        sh """
            export PATH=/usr/local/musa/bin:\$PATH;export LD_LIBRARY_PATH=/usr/local/musa/lib:/usr/lib/x86_64-linux-gnu/musa/:/usr/lib/x86_64-linux-gnu/:\$LD_LIBRARY_PATH
            wget -q --no-check-certificate https://oss.mthreads.com/release-ci/computeQA/tools/musa_pdump/axpy.cu
            wget -q --no-check-certificate https://oss.mthreads.com/release-ci/computeQA/tools/musa_pdump/HToDBuffer0.bin
            wget -q --no-check-certificate https://oss.mthreads.com/release-ci/computeQA/tools/musa_pdump/DToHBuffer1.bin
            /usr/local/musa/bin/mcc axpy.cu -o axpy -mtgpu --cuda-gpu-arch=${gpuArch} -lmusart -lmusa
            for i in \$(seq 1 10); do timeout 10 pdump|(./axpy && pkill pdump)&&break;echo \$?>tmp;done
            exit \$(cat tmp)
        """
        sh 'dpkg -P musa mtgpu&&lsmod|grep mtgpu'
        sh 'find /usr/lib/modules/ -name mtgpu.ko | xargs rm -f && depmod'
    }
    commonLib.reboot(env.NODE_NAME)
    dir('pdump_verify') {
        sh """
            wget -q --no-check-certificate https://oss.mthreads.com/release-ci/dog/HW_DOG/DiagSys_v3.1.1.zip
            unzip DiagSys_v3.1.1.zip
            cd DiagSys_v3.1.1
            python3 main.py --test_dir=${env.WORKSPACE}/pdump_test 1>verify.log 2>&1
            cat verify.log
            cat log/pdump_test_output/*/pdump_test/test.log
            cat verify.log
            grep -i fail verify.log||exit 0
            exit 1
        """
    }
}

runner.start(env.runChoice) {
    runPipeline([
        'env recovery': [closure: { commonLib.recoverEnv() }],
        'install package': [closure: { installPackage() }],
        'musa runtime pdump test': [closure: { runTest() }, setGitlabStatus: true, statusName: "${env.testLabel}"]
    ])
}
