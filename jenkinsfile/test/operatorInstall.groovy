@Library('swqa-ci')

/*
 * parameters
 * linuxDdkPackageUrl (String) - null
 * mtmlPackageUrl (String) - null
 * context (String)
*/

import org.swqa.tools.common

commonLib = new common()

def loadandDumpClusterConfig() {
    commonLib.loadScript('update_cluster_config.py', 'linux')
    sh "python update_cluster_config.py -c ${env.context} -l ${env.linuxDdkPackageUrl} -m ${env.mtmlPackageUrl}"
}

def showInfoPre() {
    println 'INFO: update ddk start!'
}

def showInfoPost() {
    println 'INFO: update ddk success!'
}

runner.start(env.runChoice, [
    main: {
        runPipeline([
        'applyClusterconfig': [closure: { loadandDumpClusterConfig() }],
        ], [disablePre: true, disablePost: true])
    },
    post: {
        runPipeline([
            'showInfoPost': [closure: {
                catchError(stageResult: 'FAILURE') {
                    showInfoPost()
                }
            }],
        ], [disablePre: true])
    },
    pre: {
        runPipeline([
            'showInfoPre': [closure: {
                catchError(stageResult: 'FAILURE') {
                    showInfoPre()
                }
            }],
        ], [disablePost: true])
    }
])
