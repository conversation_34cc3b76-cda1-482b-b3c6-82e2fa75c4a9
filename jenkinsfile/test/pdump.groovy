@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

// runChoice nodeLabel kmdPackageUrl umdPackageUrl timeout

def installDriver() {
    commonLib.recoverEnv()
    sh 'sed -i "1i /usr/lib/`uname -m`-linux-gnu/musa " /etc/ld.so.conf.d/`uname -m`-linux-gnu.conf'
    ddk.installUmd()
    dir('ddk_umd') {
        sh '''
            ls -l
            cp ./x86_64-mtgpu_linux-xorg-release/usr/local/bin/pdump /usr/local/bin/ ||:
        '''
    }
    ddk.installKmd(env.kmdPackageUrl, 'display=dummy')
    commonLib.reboot(env.NODE_NAME)
    sh 'modprobe mtgpu; sleep 10'
    sh '''
        rm -rf /usr/lib/modules/`uname -r`/mtgpu.ko ||:
        cp -rf /usr/lib/modules/`uname -r`/updates/dkms/mtgpu.ko /usr/lib/modules/`uname -r`/
    '''
    sh '''
        ls /dev/mtgpu* ||:
        # clinfo || ldconfig -v
        # clinfo
    '''
}

def runPdumpTest() {
    try {
        timeout(env.timeout.toInteger()) {
            gitLib.fetchCode('pdump_test', 'swqa-ci', null, [preBuildMerge: false])
            sh """
                sleep 2
                glxinfo -B || true
                sed "/default/d" /etc/musa.ini -i && sed "/EnableUserMemInReadPixel/d" /etc/musa.ini -i
                sed "1i [default]" /etc/musa.ini -i
                sed "1a EnableUserMemInReadPixel=0" /etc/musa.ini -i
                cd ${env.WORKSPACE}/pdump_test/pytest/ &&ls -al
                python3 -m pytest -s -v --alluredir=allure_result test_ogl_pdump.py
            """
        }
    } catch (exc) {
        throw new Exception('test failed!')
    } finally {
        sh """
            dmesg -T
            cp -raf ${env.WORKSPACE}/pdump_test/pytest/allure_result ${env.WORKSPACE}/allure_result-${env.BUILD_NUMBER}
        """
        commonLib.allure("allure_result-${env.BUILD_NUMBER}", "allure_report-${env.BUILD_NUMBER}")
    }
}

runner.start(env.runChoice) {
    runPipeline([
        'install driver': [closure: { installDriver() }],
        'pdump test': [closure: { runPdumpTest() }, setGitlabStatus: true, statusName: env.testLabel]
    ], [
        post: {
            commonLib.reboot(env.NODE_NAME)
            artifact.uploadLog('/var/log/kern.log')
        }
    ])
}
