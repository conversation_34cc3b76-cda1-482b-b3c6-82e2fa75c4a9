@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

// runChoice cluster podNodeSelector podResources containerImage testLabel triggerInfo

def runUtTest() {
    try {
        gitLib.fetchCode(env.repo, env.branch, env.commitId, [submoduleShallow: false, noTags: true, updateBuildDescription: true])
        dir(env.repo) {
            def stdout = sh(script: env.cmd, returnStdout: true).trim()
            print(stdout)
            if (stdout.contains('Some tests failed.')) {
                error 'test failed!'
            }
        }
    } catch (exc) {
        throw new Exception('test failed!')
    }
}

runner.start(env.runChoice) {
    runPipeline([
        'shared_include ut': [closure: { runUtTest() }, setGitlabStatus: true, statusName: env.testLabel]
    ])
}
