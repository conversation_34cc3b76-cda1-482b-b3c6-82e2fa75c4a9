@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

repoName = 'alphacore-assets'

def fetchCode() {
    gitLib.installGitLfs()
    withCredentials([sshUserPrivateKey(credentialsId: 'sh-code-ssh', keyFileVariable: 'PKEY')]) {
        withEnv(["GIT_SSH_COMMAND=ssh -i $PKEY -o StrictHostKeyChecking=no"]) {
            dir("${env.WORKSPACE}") {
                sh "git clone -b ${env.branch} ************************:sw/${repoName}.git"
            }
        }
    }
}

def setUpOnNode() {
    commonLib.reboot(env.NODE_NAME)
    // env recovery
    commonLib.recoverEnv()
    ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
}

def setupDepends() {
    def baseDir = "${env.musaToolkitsPackageUrl}".substring(0, "${env.musaToolkitsPackageUrl}".lastIndexOf('/'))
    dir("${repoName}") {
        def alphacoreUrl = env.AlphaCoreUnitTest ? env.AlphaCoreUnitTest : "${baseDir}/others/AlphaCoreUnitTest"
        constants.downloadPackage(alphacoreUrl)
        sh 'chmod +x ./AlphaCoreUnitTest'
    }
    musa.installMusaToolkits(env.musaToolkitsPackageUrl)
    musa.installmuAlg(env.muAlgPackageUrl)
    musa.installmuThrust(env.muThrustPackageUrl)
}

def runTest() {
    dir("${repoName}") {
        sh """
            export LD_LIBRARY_PATH=/usr/local/musa/lib;export PATH=${PATH}:/usr/local/musa/bin;
            musaInfo
            ./AlphaCoreUnitTest -r junit -o report.xml || true
        """
    }
}

def uploadTestResult() {
    dir("${repoName}") {
        junit testResults: 'report.xml'
        archiveArtifacts 'report.xml'
    }
}

runner.start(env.runChoice, [pre: {
    if (env.runChoice == 'node' && env.linuxDdkPackageUrl) {
        stage('setUpOnNode') {
            setUpOnNode()
        }
    }
}, main: {
    def workflow = [:]
    workflow['fetchCode'] = [ closure: { fetchCode() } ]
    workflow['setupDepends'] = [ closure: { setupDepends() }]
    workflow['runTest'] = [ closure: { runTest() }]
    runPipeline(workflow)
}, post: {
    runPipeline([
        'upload result': [closure: { uploadTestResult() }],
    ], [disablePre: true])
}])
