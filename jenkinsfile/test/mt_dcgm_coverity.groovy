@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

env.repo = 'mt-dcgm'
excludePath = coverity.excludeFiles[env.repo]
idir = '/workspace/mt-dcgm/_out/build/Linux-amd64-debug/cov_emit_dir'

def build() {
    stage('fetch code') {
        gitLib.fetchCode(env.repo, env.branch, env.commitId)
    }
    stage('update dockerImage') {
        sh """
            docker rmi dcgmbuild ||:
            docker rmi dcgmbuild-x86_64 ||:
            docker pull ${env.buildImage}
            docker pull ${env.relyImage}
            docker tag ${env.buildImage} dcgmbuild-x86_64:latest
            docker tag ${env.relyImage} dcgmbuild:base-gcc11-b3ae23-x86_64
            rm -rf /workspace ||: && mkdir -p /workspace
        """
    }
    stage('build dcgm') {
        credentials.runWithCredential('GIT') {
            sh "cp -rf ${env.repo} /workspace; cd /workspace/${env.repo}; ${env.buildCmd}"
        }
    }
    stage('analyze') {
        coverity.analyze(coverity.coverityBinPath, idir, true, env.WORKSPACE, excludePath)
    }
    stage('checkReport') {
        try {
            coverity.checkReport(coverity.coverityBinPath, idir, excludePath, "mt-dcgm-${env.gitlabTargetBranch}-linux")
            coverity.cleanIdir()
        } catch (exc) {
            commonLib.publishHTML('html', 'index.html', "Coverity Results ${env.gitlabTargetBranch}")
            throw exc
        }
    }
}

runner.start(env.runChoice, {
    runPipeline([
        'jenkins/coverity test': [ closure: { build() }, setGitLabStatus: true ]
    ])
})
