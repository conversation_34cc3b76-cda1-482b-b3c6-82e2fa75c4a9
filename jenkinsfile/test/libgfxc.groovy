@Library('swqa-ci')
import org.swqa.tools.git
import org.swqa.tools.common
import groovy.json.JsonOutput

env.repo = 'mtcc'
env.baseUrl = constants.OSS.URL_PREFIX

def shaderTest(String packageName) {
    ciConfig = readJSON text:  env.testGroups
    def shaderTestCommitId = ciConfig.commitId
    def run_cmd = ''
    packageName = "${packageName}.tar.gz"
    def driverUrl = "${env.baseUrl}/${env.ossRealPath}/${packageName}"

    def shader_test_repo = 'mt_compiler_test'
    new git().fetchCode(shader_test_repo, 'master', shaderTestCommitId ?: '', [preBuildMerge: false, lfs: true, shallow: false, updateBuildDescription: true])

    if (env.isMr.toBoolean()) {
        run_cmd = 'run_gfxc_ci'
    } else {
        run_cmd = 'run_gfxc_all'
    }

    dir(shader_test_repo) {
        if (isUnix()) {
            sh """
                wget -q ${driverUrl} -O ${packageName} --no-check-certificate
                tar -xf ${packageName}
                export PATH=$PATH:${env.WORKSPACE}/${shader_test_repo}/musa_compiler64_shared/bin
                export LD_LIBRARY_PATH=${env.WORKSPACE}/${shader_test_repo}/musa_compiler64_shared/lib
                cd testsuites
                chmod +x ${run_cmd}.sh
                ./${run_cmd}.sh
            """
        }
        else {
            bat """
                wget -q ${driverUrl} -O ${packageName} --no-check-certificate
                tar -xf ${packageName}
                set path=%path%;${env.WORKSPACE}\\${shader_test_repo}\\musa_compiler64_shared\\bin
                cd testsuites
                ${run_cmd}.bat
            """
        }
    }
}

// Reusage of tests for wddm
def wddmTest(String packageName, String inf_file, String driverType) {
    env.wddmCommitId = new git().fetchCode('wddm', env.buildWddmBranch, env.wddmCommitId, [preBuildMerge: false, disableSubmodules: true, shallow: false, updateBuildDescription: true])
    env.wddmCommitId = env.wddmCommitId[0..8]
    dir('wddm/ci') {
        wddmConfig = new common().loadPipelineConfig('CIConfig.yaml', '')
    }

    testConfigs = readJSON text:  env.testGroups

    def testInfo = [testinfo: testConfigs]
    if (wddmConfig.vps) {
        testInfo.vpsinfo = wddmConfig.vps
    }
    def driverUrl = "${env.baseUrl}/${env.ossRealPath}/${packageName}.tar.gz"
    def testConfigJson = JsonOutput.toJson(testInfo)

    // Set gitlab status for all wddm tests, otherwise skip
    def result = []
    testConfigs.each { testName, tests ->
        tests.each { test ->
            def formattedTest = "${testName}_${test.name}"
            result.add(formattedTest)
        }
    }
    List testItems = []
    if (env.initGitlabStatus?.trim()) {
        testItems = env.initGitlabStatus.split(',')
        result = result + testItems
    }

    node('Status_jump') {
        new git().setGitlabStatus(result.collect { "win/${it }" }, 'canceled')
        new git().setGitlabStatus(result.collect { "win/${it }" })
    }

    def jobName = "win-mtcc-test-${driverType}"
    runPipeline.runJob([
        job: jobName,
        wait: !env.isMr.toBoolean(),
        parameters: [
            triggerInfo: env.triggerInfo,
            ciConfig: testConfigJson,
            driver_url: driverUrl,
            inf_file: inf_file,
            m3d_test_url: '',
            directstream_test_url: '',
            d3dTestCommitId: wddmConfig.d3dtest,
            whlkTestCommitId: wddmConfig.whlktest
        ]
    ])
}

def oglMtccTest(String packageName) {
    def driverUrl = "${env.baseUrl}/${env.ossRealPath}/${packageName}.tar.gz"
    runPipeline.runJob([
        job: 'CI_OGL_mtcc_test',
        wait: !env.isMr.toBoolean(),
        parameters: [
            download_driver_url: driverUrl,
            gitlabMergeRequestId : env.isMr.toBoolean() ? env.gitlabMergeRequestIid : '',
            gitlabMergeRequestLastCommit: env.mtccCommitId,
            updateCIStatusForProject: env.repo,
            gitlabSourceBranch: env.buildBranch,
            gitlabTargetBranch: env.isMr.toBoolean() ? env.gitlabTargetBranch : env.buildBranch
        ]
        ])
}

def test() {
    def packageName = "${env.prefix}${env.package}"

    def testPackageMap = [
        'wddm_hw': [testItem: 'wddm_tests', packageSuffix: 'hw'],
        'wddm_vps': [testItem: 'wddm_tests', packageSuffix: 'vps'],
        'wddm_nohw': [testItem: 'wddm_tests', packageSuffix: 'nohw'],
        'gfxc_shader_tests': [testItem: 'gfxc_shader_tests', packageSuffix: ''],
        'ogl_mtcc_tests': [testItem: 'ogl_mtcc_tests', packageSuffix: 'hw']
    ]
    def testItemInfo = testPackageMap.get(env.testType)
    if (!testItemInfo?.get('testItem')) {
        error("Unsupported test type: ${env.testType}")
    }
    switch (testItemInfo.get('testItem')) {
        case 'wddm_tests':
            def pSuffix = testItemInfo.get('packageSuffix')
            def ppackageName = "${packageName}_${pSuffix}"
            wddmTest(ppackageName, env.infFile, pSuffix)
            break
        case 'gfxc_shader_tests':
            shaderTest(packageName)
            break
        case 'ogl_mtcc_tests':
            def pSuffix = testItemInfo.get('packageSuffix')
            def ppackageName = "${packageName}_${pSuffix}"
            oglMtccTest(ppackageName)
            break
        default:
            error("Unsupported test type: ${testItemInfo.testItem}")
            break
    }
}

runner.start(env.runChoice) {
    env.ossRealPath = "${env.ossPath}/${env.ossRelPath}"
    def workflow = [
        'test': [closure: { test() }, setGitlabStatus: env.isMr.toBoolean(), statusName: env.testLabel]
    ]

    runPipeline(workflow)
}
