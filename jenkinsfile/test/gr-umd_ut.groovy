@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

// runChoice nodeLabel ddkBranch linuxDdkPackageUrl caseListUrl

def installDriver() {
    env.modprobeOptions = env.modprobeOptions ?: 'options mtgpu display=dummy mtgpu_drm_major=2'
    ddk.installLinuxDdkAndSetup()
}

def runGrumdUtTest() {
    try {
        def ddkBranch = env.ddkBranch ?: 'mt-ddk-2.0'
        gitLib.fetchCode('linux-ddk', ddkBranch, null, [preBuildMerge: false, noTags: true, disableSubmodules: true])
        utPath = fileExists('linux-ddk') ? 'linux-ddk/gr-umd/unittests/pytest' : 'gr-umd/unittests/pytest'
        if (utPath.contains('linux-ddk')) {
            credentials.runWithCredential('SSH_GITLAB') {
                dir('linux-ddk') {
                    sh 'git submodule update --init gr-umd'
                }
            }
        }

        commonLib.runRelyNetwork(3, 10) {
            sh 'pip3 install allure-pytest pandas pytest'
        }
        dir(utPath) {
            if (env.caseListUrl) {
                sh "wget -q --no-check-certificate ${env.caseListUrl} -O gr_umd_test_list.txt"
            }
            def result = sh(script: 'pytest test_gr_umd_ut.py::test_gr_umd_ut --alluredir=allure-results', returnStdout: true).trim()
            print(result)
            if (result.contains('AssertionError: run fail!')) {
                error 'test failed!'
            }
        }
    } catch (exc) {
        throw new Exception('test failed!')
    } finally {
        commonLib.allure("${utPath}/allure-results", 'ddk2.0-allure_report')
    }
}

runner.start(env.runChoice) {
    runPipeline([
        'install driver': [closure: { installDriver() }, maxWaitTime: [time: 15, unit: 'MINUTES']],
        'ddk gr-umd ut': [closure: { runGrumdUtTest() }, setGitlabStatus: true, statusName: env.testLabel]
    ], [
        post: {
            commonLib.reboot(env.NODE_NAME)
            artifact.uploadLog('/var/log/kern.log')
        }
    ])
}
