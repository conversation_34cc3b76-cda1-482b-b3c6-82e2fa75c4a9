@Library('swqa-ci')

import org.swqa.tools.common

commonLib = new common()

// runchoice nodeLabel kmdPackageUrl umdPackageUrl timeout arch

def installDDK() {
    ddk.install(true)
}

def runGlmark2Test() {
    try {
        timeout(env.timeout) {
            retry(3) {
                sh '''
                    apt install glmark2 -y || true
                    export DISPLAY=:0.0 && glmark2 > /root/glmark2.log
                    cat /root/glmark2.log
                '''
                def res = sh(script: "grep 'glmark2 Score' /root/glmark2.log", returnStdout: true).trim()
                currentBuild.description += "${res}<br>"
            }
        }
    } catch (exc) {
        sh 'timeout 5 dmesg -T'
        throw exc
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'install ddk': [closure: { installDDK() }],
        'glmark2 test': [closure: { runGlmark2Test() }, setGitlabStatus: true, statusName: "${env.testLabel}"]
    ]

    runPipeline(workflow, [
        post: {
            commonLib.reboot(env.NODE_NAME)
            artifact.uploadLog('/var/log/kern.log')
        }
    ])
}
