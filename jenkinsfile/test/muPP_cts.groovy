@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * branch mtcc_test branch - default 'master'
 * linuxDdkPackageUrl (String) - default ''
 * mtccPackageUrl (String) - default ''
 * mtccLitPackageUrl (String) - default ''
 * musifyPackageUrl (String) - default 'https://oss.mthhreads.com/release-ci/computeQA/tools/musify.tar'
 * muAlgPackageUrl (String) - default 'https://oss.mthreads.com/release-ci/computeQA/mathX/newest/muAlg.tar'
 * muThrustPackageUrl (String) - default 'https://oss.mthreads.com/release-ci/computeQA/mathX/newest/muThrust.tar'
 * anacondaPackageUrl(String) - 'release-ci/computeQA/tools/musify.tar;oss/release-ci/computeQA/ai-rely-pkg/miniforge/miniforge_mathx.tar.gz'
 * compileArgs(String) - ''
 * gCover(boolean) - 'false'
 * exports (Multiline String) default 'usual export', split by ';'
 * testType (String) - default 'smoke'
 * testArgs (String) -default '--device=quyuan2'
 * reportOssPath (String) - default 'oss/release-ci/computeQA/tmp/'
 * runChoice (Choice) - node [node | pod]
 * nodeLabel (Choice) - ''
 * containerImage (String) - sh-harbor.mthreads.com/qa/musa_debug:v2
*/

env.repo = 'muPP_cts'
env.repomuPPTest = 'muPPTest'
envExport = utils.generateEnvExport(env)
testType = env.testType ?: 'smoke'
coverage_report_name = ''

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    env.muppTestCommitId = gitLib.fetchCode(env.repomuPPTest, env.muppTestBranch, env.muppTestCommitId)
    gitLib.fetchCode('musa_toolkit', 'master', null, [disableSubmodules: true])
}

def setUpOnNode() {
    // install linuxDdk full pkgs and insmod mtgpu
    if (env.runChoice == 'node') {
        ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
    }
}

def setUpinDocker() {
    if (env.linuxDdkPackageUrl) {
        ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    }
    constants.downloadAndUnzipPackage(env.anacondaPackageUrl, '/home/<USER>')
    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(env.musaToolkitsPackageUrl)
        if (env.musaToolkitsSrcPackageUrl) {
            constants.downloadAndUnzipPackage(env.musaToolkitsSrcPackageUrl, '/home/<USER>/agent/workspace/build.musa_toolkit/')
        }
    }
    else {
        if (env.musaRuntimePackageUrl) {
            musa.installMusaRuntime(env.musaRuntimePackageUrl)
        }
        def dependencies = ['mtcc': env.mtccPackageUrl]
        installDependency(dependencies)
        dir('musa_toolkit') {
            sh 'cp -r cmake /usr/local/musa/'
        }
        constants.downloadAndUnzipPackage(env.muPPPackageUrl)
        sh 'cd muPP && chmod -R 777 . && ./install.sh ||:'
    }

    musa.installMusify(env.musifyPackageUrl)
}

def runCtsTest() {
    timeout(time: env.TIMEOUT.toInteger(), unit: 'HOURS') {
        // build
        dir(env.repomuPPTest) {
            sh '''
                mkdir build && cd build
                cmake -DGTEST_ROOT=/home/<USER>/miniforge/envs/mathx  -DIPP_ROOT=/home/<USER>/ipp/latest ..
                make -j16
            '''
        }
        dir(env.repo) {
            sh '''
                wget --no-check-certificate https://oss.mthreads.com/release-ci/computeQA/tools/googletest.tar.gz
                tar -xzf googletest.tar.gz
                cd googletest  && mkdir build && cd build && cmake .. && make -j16 && make install
            '''
            sh """
                ${constants.genCondaActivate('mathx')}
                ${envExport}
                export TEST_TYPE=${testType}
                git submodule update --init --recursive
                mkdir -p build && cd build
                cmake .. ${env.compileArgs}
                make -j ${env.compileParallel}
            """
        }
        // test
        dir(env.repo) {
            sh """
                ${constants.genCondaActivate('mathx')}
                ${envExport}
                export TEST_TYPE=${testType}
                export MUSA_PORTING_PATH=${env.WORKSPACE}/musify
                sync && echo 3 > /proc/sys/vm/drop_caches ||:
                python run_test.py ${env.testArgs} ||:
            """
        }
        // coverage
        if (env.gCover == 'true') {
            runCoverage()
        }
    }

    if (env.TIMEDEBUG?.trim()) {
        timeout(time: env.TIMEDEBUG.toInteger(), unit: 'HOURS') {
            input message: "CI环境将保留 ${env.TIMEDEBUG} 小时，请选择:\nProceed(继续流水线)\n Abort(终止流水线)"
        }
    }
}

def runCoverage() {
    dir(env.repo) {
        llvm_cov_plan = mathxCoverage.llvm_cov_plan.get(env.repo)
        println "llvm_cov_plan: ${llvm_cov_plan}"
        coverage_report_name = mathxCoverage.mathxGenerateCoverage(llvm_cov_plan.'llvm_cov_directory', llvm_cov_plan.'src_remove', llvm_cov_plan.'so_name', env.repo)
        println "coverage_report_name: ${coverage_report_name}" // muPP_cts_llvm_coverage_report
        if (coverage_report_name) {
            // 读取代码覆盖率数据，并写入influxdb
            catchError(stageResult: 'FAILURE') {
                commonLib.loadScript('mathx_coverage_writetoInfluxDB.py', 'coverage', false)
                sh """
                    ${constants.genCondaActivate('mathx')}
                    ls -l
                    python mathx_coverage_writetoInfluxDB.py --indexfile ${env.WORKSPACE}/${env.repo}/${coverage_report_name}/index.html --product ${env.repo}
                """
            }
            commonLib.publishHTML(coverage_report_name, coverage_report_name)
        }
        else {
            println 'generate coverage fail!'
            currentBuild.description += '<b>generate coverage fail!</b><br>'
        }
    }
}

def checkResult() {
    dir(env.repo) {
        //python run_test.py generate test-report dir include allure report
        commonLib.allure('test-report')
    }
}

def uploadTestResult() {
    dir(env.repo) {
        sh "tar -czvf ${env.repo}_allure_result.tar.gz test-report"
        artifact.uploadTestReport("${env.repo}_allure_result.tar.gz", env.reportOssPath)

        catchError(stageResult: 'FAILURE') {
            if (env.gCover == 'true') {
                sh"""
                    pwd
                    ls -l
                    tar -czvf ${coverage_report_name}.tar.gz ${coverage_report_name}
                    ls -l
                """
                artifact.uploadTestReport("${coverage_report_name}.tar.gz", env.reportOssPath)
            }
        }
    }
}

runner.start(env.runChoice, [
    main: {
        runPipeline([
            'checkout': [closure: { fetchCode() }],
            'setup in docker': [closure: { setUpinDocker() }],
            'muPP test': [closure: { runCtsTest() }],
        ], [disablePre: true, disablePost: true])
    },
    post: {
        runPipeline([
            'upload result': [closure: {
                catchError(stageResult: 'FAILURE') {
                        uploadTestResult()
                }
            }
            ],
            'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
        ], [disablePre: true])
    },
    pre: {
        runPipeline([
            'setup pre': [closure: { setUpOnNode() }],
        ], [disablePost: true])
    }
])
