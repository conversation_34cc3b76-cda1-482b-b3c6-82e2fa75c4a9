@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * gfxswtestBranch (String) - default ''
 * linuxDdkPackageUrl (String) - default ''
 * oglPackageUrl (String) - default ''
 * dependency (String) default vulkan-cts-es.tar.gz and vulkan-cts-gl.tar.gz(test case)
 * caseListPackageUrl (String) - default ''
 * exports (Multiline String) default 'usual export', split by ';'
 * reportOssPath (String) - default 'oss path'
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
*/

def setUp() {
    // restart machine
    commonLib.reboot(env.NODE_NAME)
    // env recovery
    commonLib.recoverEnv()
    // install linuxDdk full pkgs and insmod mtgpu
    ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
    // install only ogl ddk2.0 pkg
    // to support no ogl pkg
    if (env.oglPackageUrl) {
        ddk.installLinuxDdk(env.oglPackageUrl)
    }
    commonLib.startXorg('/usr/lib/xorg/Xorg')
    commonLib.checkGlxinfo()
    dir('ogltest') {
        for (_ in env.dependency.split(';')) {
            constants.downloadAndUnzipPackage(_)
        }
    }
    gitLib.fetchCode('gfxswtest', env.gfxswtestBranch)
    dir('gfxswtest') {
        sh """
            mkdir -p ${env.WORKSPACE}/result
            cp perf/m3d_ogl_cts/analyze_log.py ${env.WORKSPACE}/result/
        """
    }
    // this needs to be done on host, not container
    if (conditions.shouldRunKmemleak()) {
        sh 'echo clear > /sys/kernel/debug/kmemleak'
    }
}

def runOglTest() {
    Map testConfig = readJSON text: env.testConfig
    Map defaultCaseList = [
        'gl': 'https://oss.mthreads.com/sw-build/m3d/ogl/master/passlist.txt',
        'gl4.2': 'https://oss.mthreads.com/sw-build/m3d/ogl/master/passlist-4.2.txt',
        'gl4.3': 'https://oss.mthreads.com/sw-build/m3d/ogl/master/passlist-4.3.txt',
        'es': 'https://oss.mthreads.com/sw-build/m3d/ogl/master/passlist-es.txt',
        'es4.2': 'https://oss.mthreads.com/sw-build/m3d/ogl/master/passlist-es-4.2.txt',
    ]
    timeout(env.TIMEOUT) {
        testConfig.each { name, config ->
            dir("ogltest/${config.workdir}/external/openglcts/modules/") {
                String caseUrl = config.case ?: defaultCaseList[name]
                String logName = caseUrl.split('/')[-1].replace('.txt', '.log')
                String caseListPath = "${env.WORKSPACE}/ogltest/${config.workdir}/external/openglcts/modules/${caseUrl.split('/')[-1]}"
                constants.downloadPackage(caseUrl)
                sh """
                    sudo DISPLAY=:0.0 vblank_mode=0 ${exports} ./${config.binary} --deqp-caselist-file=${caseListPath} |tee ${logName}
                    mv ${logName} ${env.WORKSPACE}/result
                    mv TestResults.qpa ${env.WORKSPACE}/result/TestResults_${name}.qpa
                """
            }
        }
        dir('result') {
            sh 'python3 analyze_log.py'
            def caseResultDescription = sh(script: 'cat result.info', returnStdout: true).trim()
            currentBuild.description += "${caseResultDescription} <br>"
            def result = sh(script: 'head -n1 result.info', returnStdout: true).trim()
            catchError(buildResult: null, stageResult: null) {
                uploadTestResult()
            }
            if (result != 'SUCCESS') {
                erriofo = caseResultDescription.split('\n')[1] + ' please find test owner @jiayi Wu'
                error(erriofo)
            }
        }
    }
    // this needs to be done on host, not container
    // only kmd pr need this check
    // not every node has the ability to do kmemleak check
    if (conditions.shouldRunKmemleak()) {
        def output = sh(script: 'echo scan > /sys/kernel/debug/kmemleak; head -n 500 /sys/kernel/debug/kmemleak | tee kmemleak.log', returnStdout: true).trim()
        def dmesgOut = sh(script: 'dmesg -T | tee dmesgOut.log', returnStdout: true).trim()
        if (output) {
            artifact.uploadTestReport('kmemleak.log', env.reportOssPath, 'kmemleak log')
            error("kmemleak found!\nkmemleak output:\n${output}\n")
        }
        if (dmesgOut =~ /BUG: ASAN:/) {
            artifact.uploadTestReport('dmesgOut.log', env.reportOssPath, 'dmesgOut log')
            error("BUG: ASAN: found in dmesg!\ndmesg output:\n${dmesgOut}\n")
        }
    }
}

def uploadTestResult() {
    dir(env.WORKSPACE) {
        sh 'tar -czvf m3d_ogl_test_result.tar.gz result'
        artifact.uploadTestReport("${env.WORKSPACE}/m3d_ogl_test_result.tar.gz", env.reportOssPath)
    }
}

runner.start(env.runChoice) {
    runPipeline([
        'setup': [closure: { setUp() }],
        'run ogl test': [closure: { runOglTest() }, setGitlabStatus: true, statusName: env.testLabel],
    ], [
        post: {
            commonLib.reboot(env.NODE_NAME)
            artifact.uploadLog('/var/log/kern.log')
        }
    ])
}
