@Library('swqa-ci')

import org.swqa.tools.common

//author: liya
//email: <EMAIL>

def executeTest() {
    node("CI_GFX_Win10_${env.testPlatform}") {
        def nodeName = env.NODE_NAME
        try {
            new common().initEnv(nodeName, "192.168.${nodeName.split('_')[-1]}")
            sleep(time: 10, unit: 'SECONDS')
            deleteDir()
            if (env.driverUrl) {
                winTest.update_driver(env.driverUrl, env.infFile)
            } else {
                winTest.update_driver_latest(env.infFile)
            }

            def testName = (env.testLabel ?: '').tokenize('/').last()
            if (!testName) {
                error 'Test name is empty, please check env.testLabel'
            }

            echo "Running ${testName} on ${env.testPlatform}"
            stage(testName) {
                timeout(env.testTimelimit) {
                    try {
                        winTest."${testName}"(env.winTestDependencyUrl)
                    } catch (e) {
                        currentBuild.result = 'FAIL'
                        echo "Error: ${e}"
                        error "${testName} failed"
                    }
                }
            }
        } catch (e) {
            currentBuild.result = 'FAIL'
            echo "Error: ${e}"
            error "${testName} Test failed"
        } finally {
            new common().initEnv(nodeName, "192.168.${nodeName.split('_')[-1]}")
        }
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'executeTest': [closure: { executeTest() }, setGitlabStatus: true, statusName: "${env.testLabel}"]
    ]
    runPipeline(workflow, [disablePost:true])
}
