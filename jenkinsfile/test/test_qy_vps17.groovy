@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()

// runChoice nodeLabel dockerImage linuxDdkPackageUrl chipType vpsVersion model tagDate
logFile = "${env.build_ID}_vps.log"
testResultsUrl = "https://oss.mthreads.com/${env.ossTestResultsSavePath}"
sshLoginCmd = 'sshpass -p 123456 ssh 127.0.0.1 -p 10022 -o StrictHostKeyChecking=no'
mtccPkgUrl = 'https://oss.mthreads.com/release-ci/computeQA/vps/newest_ph1s_pkg/mtcc-tmp-x86_64-linux-gnu-ubuntu-20.04.tar.gz'
env.skip_me = 'true'

def downloadImg() {
    // if restart vps exists in following steps, we need to copy vps img to workspace and delete it afterwards
    // vps storage will lost after rebooting vps, when starting vps with option -snapshot
    def imgPath = '/data/qemu/linux/cts_vps'
    if (!fileExists("${imgPath}/${env.vpsImg}")) {
        sh """
            mkdir -p ${imgPath} && cd ${imgPath}
            sudo wget -q --no-check-certificate https://oss.mthreads.com/release-ci/computeQA/vps/vps-img/${env.vpsImg}
            sleep 5
        """
    }
}

def compileAxpy() {
    docker.image(env.compileDockerImage).inside("--privileged -i -u 0:0 -v ${env.WORKSPACE}:/root/test/pkg") {
        if (env.musaRuntimePackageUrl) {
            musa.installMusaRuntime(env.musaRuntimePackageUrl)
        }
        sh """
            wget -q --no-check-certificate ${env.linuxDdkPackageUrl} && tar xzf *ddk2.0_vps.tar.gz
            cd linux-ddk/build && ./install.sh

            cd /root/test/pkg
            wget -q --no-check-certificate ${mtccPkgUrl}
            tar xzf mtcc-tmp-x86_64-linux-gnu-ubuntu-20.04.tar.gz
            ./install.sh

            export PATH=/usr/local/musa/bin:\$PATH
            export LD_LIBRARY_PATH=/usr/local/musa/lib/:\$LD_LIBRARY_PATH

            mc cp oss/release-ci/computeQA/vps/demo/test_axpy/axpy.cu /root/test/pkg/
            mcc axpy.cu -o axpy_quyuan1 -lmusart -lmusa -mtgpu --cuda-gpu-arch=mp_21
            mcc axpy.cu -o axpy_quyuan2 -lmusart -lmusa -mtgpu --cuda-gpu-arch=mp_22
            mcc axpy.cu -o axpy_ph1 -lmusart -lmusa -mtgpu --cuda-gpu-arch=mp_31
            mcc axpy.cu -o axpy_ph1s -lmusart -lmusa -mtgpu --cuda-gpu-arch=mp_32
        """
    }
}

def runVpsTest() {
    compileAxpy()

    docker.image(env.dockerImage).inside("--privileged -i -u 0:0 --hostname qemu-dev -v ${env.WORKSPACE}:/root/test/pkg -v /data/qemu/linux/cts_vps:/root/images") {
        try {
            // setup vps
            oss.install('mtoss', 'mtoss123')
            new common().runRelyNetwork(3, 10) {
                sh 'apt install -y sshpass'
            }

            def chips = ['quyuan1', 'quyuan2', 'ph1', 'ph1s']
            def models = ['amodel', 'cmodel']

            chips.each { chip ->
                models.each { model ->
                    println "chip: ${chip}, model: ${model}"
                    sh """
                        /root/run_qemu.pl -r ${env.vpsVersion} -g /root/images/${env.vpsImg} -extra_args "-net user,hostfwd=tcp::10022-:22 -net nic -snapshot -m 32G -smp 4 -virtfs local,path=/root/test/pkg,mount_tag=host0,security_model=passthrough,id=host0 -display none --enable-kvm" -chip_type ${chip} -mode ${model} -ipc_type direct > ${env.workspace}/${chip}_${model}_${logFile} &
                    """

                    timeout(10) {
                        sh '''
                            while ! timeout 10 sshpass -p 123456 ssh 127.0.0.1 -p 10022 -o StrictHostKeyChecking=no; do
                                sleep 5
                            done
                        '''
                    }

                    // insmod kmd
                    sh """
                        ${sshLoginCmd} 'rm -rf B${env.build_ID}_logs && mkdir -p ddk_vps B${env.build_ID}_logs'
                        ${sshLoginCmd} 'mkdir -p /root/test/pkg && mount -t 9p -o trans=virtio,version=9p2000.L host0 /root/test/pkg || true'
                        ${sshLoginCmd} 'chmod +x /root/test/pkg/*'

                        ${sshLoginCmd} 'rm -rf /lib/firmware/mthreads/*' ||:
                        ${sshLoginCmd} 'mkdir -p /lib/firmware/mthreads/'
                        ${sshLoginCmd} 'cd /root/test/pkg/linux-ddk/build && ./install.sh'
                        ${sshLoginCmd} 'modprobe -v mtgpu'
                    """
                    if (env.musaRuntimePackageUrl) {
                        sh "${sshLoginCmd} 'cd /root/test/pkg/MUSA-Runtime && ./install.sh'"
                    }
                    // run test
                    sh """
                        ${sshLoginCmd} 'export PATH=/usr/local/musa/bin:\$PATH; export LD_LIBRARY_PATH=/usr/local/musa/lib/:\$LD_LIBRARY_PATH; cd /root/test/pkg && ./axpy_${chip}'
                    """
                }
            }
        } catch (exc) {
            throw new Exception('test failed!')
        } finally {
            // update log
            oss.install('mtoss', 'mtoss123')
            sh """
                ${sshLoginCmd} 'dmesg -T'
                cd ${env.WORKSPACE}
                mc cp *${logFile} oss/${env.ossTestResultsSavePath}/
            """
            currentBuild.description += "Test Results PKG: ${testResultsUrl} <br>"
        }
    }
}

runner.start(env.runChoice) {
    runPipeline([
        'download img': [closure: { downloadImg() }],
        'vps test': [closure: { runVpsTest() }, setGitlabStatus: true]
    ])
}
