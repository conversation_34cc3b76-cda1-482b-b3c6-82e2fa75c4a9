@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * branch triton_cts branch - default 'master'
 * linuxDdkPackageUrl (String) - default ''
 * musaToolkitPackageUrl (String) - default ''
 * mcclPackageUrl (String) - default ''
 * mudnnPackageUrl (String) - default ''
 * torchPackageUrl (String) - default ''
 * torchMusaPackageUrl (String) - default ''
 * tritonMusaPackageUrl (String) - default ''
 * muthrustPackageUrl (String) - default ''
 * muAlgPackageUrl (String) - default ''
 * exports (Multiline String) default 'usual export', split by ';'
 * testType (String) - default 'smoke'
 * testMark (String) - default ''
 * reportOssPath (String) - default 'oss/release-ci/computeQA/tmp/'
 * runChoice (Choice) - node [node | pod]
 * nodeLabel (Choice) - ''
 * containerImage (String) - ''
*/

env.repo = 'flaggems'
List envs = env.exports ? env.exports.split(';') : []
envExport = envs ? 'export ' + envs.join(' && export ') : ''

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, ['gitlabGroup':'sw-compiler'])
}

def setUpOnNode() {
    // install linuxDdk full pkgs and insmod mtgpu
    if (env.runChoice == 'node') {
        ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
    }
}

def setUpinDocker() {
    sh '''
        apt update
        apt install libcunit1 -y ||:
    '''
    ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    musa.installMusaToolkits(env.musaToolkitPackageUrl)
    constants.downloadAndUnzipPackage(env.mcclPackageUrl)
    sh 'cd mccl && ./install.sh'
    constants.downloadAndUnzipPackage(env.mudnnPackageUrl)
    sh 'cd mudnn && ./install_mudnn.sh -i'
    musa.installmuAlg(env.muAlgPackageUrl)
    musa.installmuThrust((env.muThrustPackageUrl))
    constants.downloadPackage(env.torchPackageUrl)
    constants.downloadPackage(env.torchMusaPackageUrl)
    sh '''
        . /root/miniconda3/etc/profile.d/conda.sh
        conda activate py310
        pip install ./torch*.whl
    '''
    constants.downloadPackage(env.tritonMusaPackageUrl)
    sh '''
        . /root/miniconda3/etc/profile.d/conda.sh
        conda activate py310
        cd bdist_wheel && pip install ./triton*.whl
    '''
    constants.downloadAndUnzipPackage('https://oss.mthreads.com/release-ci/computeQA/ai-rely-pkg/triton_musa/triton_rely_so.tar.gz')
    sh 'cp -a triton_rely/* /usr/lib/x86_64-linux-gnu/'
}

def runCtsTest() {
    timeout(env.TIMEOUT) {
        // test
        dir(env.repo) {
            sh """
                ${envExport}
                . /root/miniconda3/etc/profile.d/conda.sh
                conda activate py310
                pip install -e . -no-deps
                cd tests && python test_50_ops.py ||:
            """
        }
    }
}

def checkResult() {
    dir("${env.repo}/tests") {
        //python run_test.py generate test-report dir include allure report
        commonLib.allure('test-report')
    }
}

def uploadTestResult() {
    dir(env.repo) {
        sh 'tar -czvf triton_cts_allure_result.tar.gz test-report'
        artifact.uploadTestReport('triton_cts_allure_result.tar.gz', env.reportOssPath)
    }
}

runner.start(env.runChoice, [main: {
    runPipeline([
        'checkout': [closure: { fetchCode() }],
        'setup in docker': [closure: { setUpinDocker() }],
        'flaggems test': [closure: { runCtsTest() }],
    ], [disablePre: true, disablePost: true])
}, post: {
    runPipeline([
        'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
        'upload result': [closure: { uploadTestResult() }]
    ], [disablePre: true])
}, pre: {
    runPipeline([
        'setup pre': [closure: { setUpOnNode() }],
    ], [disablePost: true])
}])
