@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * repo (String) - mtJPEG2000
 * branch (String) - develop
 * commitId (String) - ''
 * linuxDdkPackageUrl (String) - ''
 * musaToolkitPackageUrl (String) - ''
 * mtJPEGPackageUrl (String) - '''
 * cmd (String) - ./build.sh -a 21,22; tar czf mtJPEG2000.tar.gz mtjpeg2k_sdk mtjpeg2k_test
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/qa/linux-ddk:v14
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=2;requests=memory=5Gi;limits=cpu=10;limits=memory=25Gi
*/

def envSetUp() {
    commonLib.reboot(env.NODE_NAME)
    commonLib.recoverEnv()
    ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
    musa.installMusaToolkits(env.musaToolkitPackageUrl)
    if (env.musaRuntimePackageUrl) {
        musa.installMusaRuntime(env.musaRuntimePackageUrl)
    }
}

def test() {
    constants.downloadAndUnzipPackage(env.mtJPEGPackageUrl)
    catchError {
        dir('mtjpeg2k_test') {
            sh """
                ${env.cmd}
            """
        }
    }
}

def checkResult() {
    dir('mtjpeg2k_test') {
        def result = sh(script: 'cat result.txt', returnStdout: true).trim()
        if (result != '0') {
            sh 'cat log.txt || true'
            error('test failed')
        }else {
            sh 'cat log.txt || true'
        }
    }
}

def uploadTestResult() {
    dir('mtjpeg2k_test') {
        sh 'tar -czvf mtJPEG2000log.tar.gz logs/'
        artifact.uploadTestReport('mtJPEG2000log.tar.gz', env.reportOssPath)
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'envSetUp': [closure: { envSetUp() }],
        'test': [closure: { test() }],
        'uploadTestResult': [closure: { uploadTestResult() }],
        'checkResult': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
    ]
    if (env.mtJpegPackageName) { workflow['upload'] = [closure: { upload() }] }
    runPipeline(workflow)
}
