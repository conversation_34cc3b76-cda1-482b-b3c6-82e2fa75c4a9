@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * branch mtcc_test branch - default 'master'
 * linuxDdkPackageUrl (String) - default ''
 * mtccPackageUrl (String) - default ''
 * mtccLitPackageUrl (String) - default ''
 * musifyPackageUrl (String) - default 'https://oss.mthhreads.com/release-ci/computeQA/tools/musify.tar'
 * muAlgPackageUrl (String) - default 'https://oss.mthreads.com/release-ci/computeQA/mathX/newest/muAlg.tar'
 * muThrustPackageUrl (String) - default 'https://oss.mthreads.com/release-ci/computeQA/mathX/newest/muThrust.tar'
 * anacondaPackageUrl(String) - 'release-ci/computeQA/tools/musify.tar;oss/release-ci/computeQA/ai-rely-pkg/miniforge/miniforge_mathx.tar.gz'
 * exports (Multiline String) default 'usual export', split by ';'
 * testType (String) - default 'smoke'
 * testArgs (String) -default '--device=quyuan2'
 * reportOssPath (String) - default 'oss/release-ci/computeQA/tmp/'
 * runChoice (Choice) - node [node | pod]
 * nodeLabel (Choice) - ''
 * containerImage (String) - sh-harbor.mthreads.com/qa/musa_debug:v2
*/

env.repo = 'mtcc_test'
List envs = env.exports ? env.exports.split(';') : []
envExport = envs ? 'export ' + envs.join(' && export ') : ''
env.branch = env.mtccTestBranch ? env.mtccTestBranch : env.branch
env.commitId = env.mtccTestBranch ? '' : env.commitId

def getTestDevice(testArgs) {
    def name = 'sudi'
    for (each in ['sudi', 'quyuan1', 'quyuan2', 'ph1']) {
        if (testArgs.contains(each)) {
            println("GPU CHIP: $each")
            name = each
        }
    }
    if (testArgs.contains('extra_flags')) {
        name = "${name}_cflags"
    }
    return name
}

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
}

def setUpOnNode() {
    if (env.runChoice == 'node') {
        // install linuxDdk full pkgs and insmod mtgpu
        ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
    }
}

def setUpinDocker() {
    sh '''
        apt update ||:
        apt install dkms -y ||:
    '''
    println("env.linuxDdkPackageUrl: $env.linuxDdkPackageUrl")
    println("env.musaToolkitsPackageUrl: $env.musaToolkitsPackageUrl")
    println("env.mtccPackageUrl: $env.mtccPackageUrl")
    if (env.linuxDdkPackageUrl) {
        ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    }
    constants.downloadAndUnzipPackage(env.anacondaPackageUrl, '/home/<USER>')
    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(env.musaToolkitsPackageUrl)
    }
    else {
        if (env.musaRuntimePackageUrl) {
            musa.installMusaRuntime(env.musaRuntimePackageUrl)
        }
        def dependencies = ['mtcc': env.mtccPackageUrl]
        installDependency(dependencies)
        musa.installMusify(env.musifyPackageUrl)
    }
    // reinstall mtcc
    if (env.mtccPackageUrl) {
        def dependencies = ['mtcc': env.mtccPackageUrl]
        installDependency(dependencies)
    }

    if (mtccLitPackageUrl) {
        constants.downloadAndUnzipPackage(env.mtccLitPackageUrl, 'mtcc_lit_test')
        sh "cp -r /usr/local/musa/* ${env.WORKSPACE}/mtcc_lit_test/build"
    }
    musa.installmuAlg(env.muAlgPackageUrl)
    musa.installmuThrust((env.muThrustPackageUrl))

    dir("${env.repo}/tools") {
        sh """
            ${constants.genCondaActivate('mathx')}
            export MUSA_PORTING_PATH=${env.WORKSPACE}/musify
            [ -e /usr/local/musa/tools/musify* ] && unset MUSA_PORTING_PATH
            bash porting_for_ci.sh  ${env.WORKSPACE}/mtcc_lit_test/mtcc
        """
    }
}

def runCtsTest() {
    timeout(time: env.TIMEOUT.toInteger(), unit: 'HOURS') {
        dir("${env.repo}/pytest") {
            // smoke testing
            if (env.testMtccLit == 'true') {
                sh """
                    ${constants.genCondaActivate('mathx')}
                    ${envExport}
                    python3 run_lit.py --mtcc_dir_path ${env.WORKSPACE}/mtcc_lit_test ${env.litTestArgs}
                """
            }
            // daily testing
            if (!env.litTestArgs?.contains('--hg true')) {
                sh """
                    ${constants.genCondaActivate('mathx')}
                    ${envExport}
                    python3 run_lit.py ${env.testArgs}
                """
                // daily testing on qy1 and qy2
                if (env.testAssemble == 'true') {
                    sh """
                        ${constants.genCondaActivate('mathx')}
                        ${envExport}
                        python3 run_lit.py ${env.testArgs} --type=assemble
                    """
                }
            }
            def deviceInfo = getTestDevice(env.testArgs)
            sh """
                ${constants.genCondaActivate('mathx')}
                ${envExport}
                cp mtcc_lit_report.py mtcc_test_${deviceInfo}.py
                pytest mtcc_test_${deviceInfo}.py --alluredir=mtcc_test_allure_result ||:
                ls mtcc_test_allure_result
            """
        }
    }

    if (env.TIMEDEBUG?.trim()) {
        timeout(time: env.TIMEDEBUG.toInteger(), unit: 'HOURS') {
            input message: "CI环境将保留 ${env.TIMEDEBUG} 小时，请选择:\nProceed(继续流水线)\n Abort(终止流水线)"
        }
    }
}

def checkResult() {
    dir("${env.repo}/pytest") {
        //python run_test.py generate test-report dir include allure report
        commonLib.allure('mtcc_test_allure_result')
    }
}

def uploadTestResult() {
    dir("${env.repo}/pytest") {
        sh 'tar -czvf mtcc_test_allure_result.tar.gz mtcc_test_allure_result'
        artifact.uploadTestReport('mtcc_test_allure_result.tar.gz', env.reportOssPath)
    }
}

runner.start(env.runChoice, [
    main: {
        runPipeline([
            'checkout': [closure: { fetchCode() }],
            'setup in docker': [closure: { setUpinDocker() }],
            'mtcc test': [closure: { runCtsTest() }],
        ], [disablePre: true, disablePost: true])
    },
    post: {
        runPipeline([
            'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
            'upload result': [closure: { uploadTestResult() }]
        ], [disablePre: true])
    },
    pre: {
        runPipeline([
            'setup pre': [closure: { setUpOnNode() }],
        ], [disablePost: true])
    }
])
