@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * branch mtcc_test branch - default 'master'
 * linuxDdkPackageUrl (String) - default ''
 * mtccPackageUrl (String) - default ''
 * mtccLitPackageUrl (String) - default ''
 * musifyPackageUrl (String) - default 'https://oss.mthhreads.com/release-ci/computeQA/tools/musify.tar'
 * muAlgPackageUrl (String) - default 'https://oss.mthreads.com/release-ci/computeQA/mathX/newest/muAlg.tar'
 * muThrustPackageUrl (String) - default 'https://oss.mthreads.com/release-ci/computeQA/mathX/newest/muThrust.tar'
 * anacondaPackageUrl(String) - 'release-ci/computeQA/tools/musify.tar;oss/release-ci/computeQA/ai-rely-pkg/miniforge/miniforge_mathx.tar.gz'
 * compileArgs(String) - ''
 * gCover(boolean) - 'false'
 * exports (Multiline String) default 'usual export', split by ';'
 * testType (String) - default 'smoke'
 * testArgs (String) -default '--device=quyuan2'
 * reportOssPath (String) - default 'oss/release-ci/computeQA/tmp/'
 * runChoice (Choice) - node [node | pod]
 * nodeLabel (Choice) - ''
 * containerImage (String) - sh-harbor.mthreads.com/qa/musa_debug:v2
*/

env.repo = 'muThrust_cts'
env.branch = env.muThrustCtsBranch ? env.muThrustCtsBranch : env.branch
env.commitId = env.muThrustCtsBranch ? '' : env.commitId
envExport = utils.generateEnvExport(env)
testType = env.testType ?: 'smoke'
coverage_report_name = ''

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    gitLib.fetchCode('musa_toolkit', 'master', null, [disableSubmodules: true])
}

def setUpOnNode() {
    // install linuxDdk full pkgs and insmod mtgpu
    if (env.runChoice == 'node') {
        ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
    }
}

def setUpinDocker() {
    sh '''
        apt update ||:
        apt install dkms -y ||:
    '''
    if (env.linuxDdkPackageUrl) {
        ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    }
    constants.downloadAndUnzipPackage(env.anacondaPackageUrl, '/home/<USER>')
    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(env.musaToolkitsPackageUrl)
        if (env.musaToolkitsSrcPackageUrl) {
            constants.downloadAndUnzipPackage(env.musaToolkitsSrcPackageUrl, '/home/<USER>/agent/workspace/build.musa_toolkit/')
        }
    }
    else {
        if (env.musaRuntimePackageUrl) {
            musa.installMusaRuntime(env.musaRuntimePackageUrl)
        }
        def dependencies = ['mtcc': env.mtccPackageUrl]
        installDependency(dependencies)
        dir('musa_toolkit') {
            sh 'cp -r cmake /usr/local/musa/'
        }
    }

    musa.installmuAlg(env.muAlgPackageUrl)
    musa.installmuThrust((env.muThrustPackageUrl))
    if (env.gCover == 'true') {
        dir(env.repo) {
            sh '''
                mkdir gcov
                cp -r /usr/local/musa/include/cub ./gcov/
                cp -r /usr/local/musa/include/thrust  ./gcov/
            '''
        }
    }
}

def runCtsTest() {
    timeout(time: env.TIMEOUT.toInteger(), unit: 'HOURS') {
        // build
        dir(env.repo) {
            def GCOV_TEST = env.gCover == 'true' ? 'export GCOV_TEST=ON' : ''
            sh """
                ${envExport}
                export TEST_TYPE=${testType}
                ${GCOV_TEST}
                mkdir -p build && cd build
                cmake .. ${env.compileArgs}
                make -j ${env.compileParallel}
            """
        }
        // test
        dir(env.repo) {
            sh """
                ${constants.genCondaActivate('mathx')}
                ${envExport}
                export TEST_TYPE=${testType}
                python run_test.py ${env.testArgs} ||:
            """
        }
    }

    if (env.TIMEDEBUG?.trim()) {
        timeout(time: env.TIMEDEBUG.toInteger(), unit: 'HOURS') {
            input message: "CI环境将保留 ${env.TIMEDEBUG} 小时，请选择:\nProceed(继续流水线)\n Abort(终止流水线)"
        }
    }
}

def checkResult() {
    dir(env.repo) {
        //python run_test.py generate test-report dir include allure report
        commonLib.allure('test-report')
    }
}

def uploadTestResult() {
    dir(env.repo) {
        sh "tar -czvf ${env.repo}_allure_result.tar.gz test-report"
        artifact.uploadTestReport("${env.repo}_allure_result.tar.gz", env.reportOssPath)

        catchError(stageResult: 'FAILURE') {
            if (env.gCover == 'true') {
                sh"""
                    pwd
                    ls -l
                    tar -czvf ${coverage_report_name}.tar.gz ${coverage_report_name}
                    ls -l
                """
                artifact.uploadTestReport("${coverage_report_name}.tar.gz", env.reportOssPath)

            // sh "tar -czvf ${env.repo}_test_llvm_coverage.tar.gz llvm_cov_data"
            // artifact.uploadTestReport("${env.repo}_test_llvm_coverage.tar.gz", env.reportOssPath)
            }
        }
    }
}

runner.start(env.runChoice, [
    main: {
        runPipeline([
            'checkout': [closure: { fetchCode() }],
            'setup in docker': [closure: { setUpinDocker() }],
            'muThrust test': [closure: { runCtsTest() }],
        ], [disablePre: true, disablePost: true])
    },
    post: {
        runPipeline([
            'upload result': [closure: {
                catchError(stageResult: 'FAILURE') {
                        uploadTestResult()
                }
            }
            ],
            'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
        ], [disablePre: true])
    },
    pre: {
        runPipeline([
            'setup pre': [closure: { setUpOnNode() }],
        ], [disablePost: true])
    }
])
