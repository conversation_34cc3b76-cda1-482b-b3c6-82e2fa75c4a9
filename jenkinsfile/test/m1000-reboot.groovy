@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

def fetchCode() {
    gitLib.fetchCode(env.testRepoName, env.testRepoBranch)
}

def installPackage() {
    dir(env.testRepoName) {
        artifact.download(env.repo, env.branch, env.commitId, "${env.packageName}")

        sh """
            ${env.upgradeScript}
        """
    }

    // install musa
    sh """
        wget -c --no-verbose "${env.testMusaDeb}" -O musa.deb || { echo "Failed to download ${env.testMusaDeb}"; exit 1; }
        rm -rf /var/lib/dkms/mtgpu/
        dpkg -i musa.deb
    """
}

def reboot() {
    sh '''
        sudo rm -f /var/log/syslog
    '''

    commonLib.reboot(env.NODE_NAME)
}

def runRebootTest() {
    dir(env.testRepoName) {
        sh """
            ${env.testScript}
        """
    }
}

def runBootTimeTest() {
    dir(env.testRepoName) {
        sh """
            ${env.bootTimeTestScript}
        """
    }
}

def runS3Test() {
    dir(env.testRepoName) {
        sh """
            ${env.s3TestScript}
        """
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'install package': [closure: { installPackage() }, maxWaitTime: [time: 30, unit: 'MINUTES']],
        'reboot': [closure: { reboot() }],
        'reboot check': [closure: { runRebootTest() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
        'boot-time check': [closure: { runBootTimeTest() }, setGitlabStatus: true, statusName: "${env.bootTimeTestLabel}"],
        's3 test': [closure: { runS3Test() }, setGitlabStatus: true, statusName: "${env.s3TestLabel}"]
    ]

    runPipeline(workflow)
}
