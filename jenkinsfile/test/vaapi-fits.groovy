@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * coverageCmd (String) - default ''
 * vaapiBranch vaapi-fits branch - default 'scj_dev'
 * vaapiCommitId vaapi-fits commit - default ''
 * libvaUtilsBranch - default 'ci-2.20'
 * linuxDdkPackageUrl (String) - default ''
 * kmdPackageUrl (String) - default ''
 * umdPackageUrl (String) - default ''
 * libdrmPackageUrl (String) - default ''
 * libdrmBranch (String) - default 'master'
 * libdrmCommitId (String) - default ''
 * mediaDriverPackageUrl (String) - default ''
 * JPEGPackageUrl (String) - default 'https://oss.mthreads.com/release-ci/media/depend/JPEG.zip'
 * videosDependency (String) - default "oss/release-ci/computeQA/dataset/vpu_tests/"
 * exports (Multiline String) default 'usual export', split by ';'
 * testType (String) - default 'smoke'
 * testMark (String) - default ''
 * reportOssPath (String) - default 'oss path'
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * TIMEOUT(String)
*/

env.vaapiRepo = 'vaapi-fits'
List envs = env.exports ? env.exports.split(';') : []
envExport = envs ? 'export ' + envs.join(' && export ') : ''

def libvaUtilsCompile() {
    libvaUtilsCommit = gitLib.fetchCode('libva-utils', env.libvaUtilsBranch)
    sh '''
        export PKG_CONFIG_PATH=/usr/local/mt_vaapi/lib/pkgconfig/:\$PKG_CONFIG_PATH
        export PATH=/usr/local/mt_vaapi/bin/:\$PATH
        export LD_LIBRARY_PATH=/usr/local/mt_vaapi/lib:\$LD_LIBRARY_PATH
        cd libva-utils;
        chmod 777 autogen.sh;
        ./autogen.sh --enable-tests=yes  --libdir=/usr/local/mt_vaapi/lib --includedir=/usr/local/mt_vaapi/include
        make -j12
    '''
    currentBuild.description += "libva-utils compile by commit:${libvaUtilsCommit}<br>"
}

def installDenpencyPkgs() {
    //等待libva ffmpeg 上大包后停止且不调用此方法
    dir('otherpkgs') {
        constants.downloadAndUnzipPackage('https://oss.mthreads.com/sw-build/mt-media-driver/zjf/m3d/65ebe0904/65ebe0904_media_driver.tar.gz')
        sh '''
            cd mt_video
            dpkg --force-overwrite -i libva_2.20_x86_64.deb
            dpkg --force-overwrite -i ffmpeg_4.4.2_x86_64.deb
        '''
    }
}

def fetchCode() {
    // restart machine
    commonLib.reboot(env.NODE_NAME)
    // env recovery
    commonLib.recoverEnv()
    gitLib.installGitLfs()
    withCredentials([sshUserPrivateKey(credentialsId: 'sh-code-ssh', keyFileVariable: 'PKEY')]) {
        withEnv(["GIT_SSH_COMMAND=ssh -i $PKEY -o StrictHostKeyChecking=no"]) {
            sh "git clone -b ${env.vaapiBranch} ************************:sw/vaapi-fits.git"
        }
    }
    if (fileExists('/dev/dri/renderD128') && !fileExists('/sys/bus/pci/drivers/mtgpu/*/drm/renderD128')) {
        sh '''
            sed -i 's/GRUB_CMDLINE_LINUX_DEFAULT=""/GRUB_CMDLINE_LINUX_DEFAULT="quiet splash i915.modeset=0"/' /etc/default/grub
            sudo update-grub
        '''
    }
}

def setUpOnNode() {
    if (env.linuxDdkPackageUrl) {
        ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
    } else {
        installDependency([
            'gr-kmd': [env.kmdPackageUrl, 'GeneralSVMHeapPageSize=0x1000'],
            'gr-umd': [env.umdPackageUrl, false]
        ])
        sh 'update-initramfs -u -k \$(uname -r)'
        commonLib.reboot(env.NODE_NAME)
        sh 'modprobe mtgpu'
        sh 'cat /sys/module/mtgpu/parameters/GeneralSVMHeapPageSize'

        constants.downloadAndUnzipPackage(env.mediaDriverPackageUrl)
        dir('mt_video') {
            sh '''
                cp firmware/* /lib/firmware/mthreads ||:
                ls -l /lib/firmware/mthreads ||:
            '''
        }
    }
}
def setUpinDocker() {
    sh 'apt update ||:'
    if (env.linuxDdkPackageUrl) {
        ddk.installLinuxDdk(env.linuxDdkPackageUrl)
        if (env.mediaDriverPackageUrl) {
            ddk.installLinuxDdk(env.mediaDriverPackageUrl)
        }
        if (env.kmdPackageUrl) {
            ddk.installLinuxDdk(env.kmdPackageUrl, true)
        }
    //installDenpencyPkgs()
    } else {
        installDependency([
            'gr-umd': [env.umdPackageUrl, false],
            'libdrm-mt': [env.libdrmBranch, env.libdrmCommitId],
            'mt-media-driver': env.mediaDriverPackageUrl,
        ])
    }
    oss.install()
    //oss.cp(env.videosDependency, './')
    //install vc-1&videos
    //prepare test data
    sh '''
        mv /home/<USER>/*  ./
        tar -zxvf chromium_test_data.tar.gz
        tar -zxvf chromium_test_case.tar.gz
        unzip Bosphorus_1920x1080_120fps_420_8bit_YUV.zip
        unzip BasketballDrive_1920x1080_50.zip
        rm -rf *.tar.gz
        rm -rf *.zip
    '''
    libvaUtilsCompile()
}

def runCoverage() {
    try {
        gitLib.setGitlabStatus('runCoverage', 'running')
        gitLib.fetchCode(env.gitlabTargetRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [submoduleShallow: false, updateBuildDescription: true])
        apt.installPackage('lcov=1.15-1')
        sh "${env.coverageCmd}"
        gitLib.setGitlabStatus('runCoverage', 'success')
    } catch (exc) {
        gitLib.setGitlabStatus('runCoverage', 'failed')
        log.info(exc.message)
        error 'runCoverage failed!'
    }
}

def runVpuTest() {
    if (env.coverageCmd) {
        runCoverage()
    }
    timeout(env.TIMEOUT) {
        dir(env.vaapiRepo) {
            //test vainfo
            sh """
                time dd if=/dev/zero bs=1024 count=1000000 of=`pwd`/iotest.zero.file  ||:
                time dd if=`pwd`/iotest.zero.file bs=1024 count=1000000 of=/dev/null ||:
                time rm `pwd`/iotest.zero.file ||:
                ${envExport}
                cat /sys/module/mtgpu/parameters/mtgpu_drm_major ||:
                dmesg -wT > dmesg_ci_test.txt &
                ls /dev/dri/renderD128 ||:
                vainfo
                if [ \$? -eq 0 ];then
                    echo \"vainfo check: pass\"
                else
                    echo \"vainfo check: fail------\"
                    exit 1
                fi
                vainfo 2>&1 | grep "va_openDriver() returns 0"
                gst-inspect-1.0 checksumsink2
                gst-inspect-1.0 vaapi
                gst-inspect-1.0 h264parse
            """
            //test vpu
            sh """
                ${envExport}
                mkdir results ||:
                python run_test.py ||:
            """
        }
    }
}

def checkResult() {
    dir("${env.WORKSPACE}/${env.vaapiRepo}") {
        sh """
            tar -czvf vpu_test_allure_result.tar.gz test-report
            tar -zcvf dmesg_ci_test_${BUILD_ID}.tar.gz dmesg_ci_test.txt
        """
        artifact.uploadTestReport("${env.WORKSPACE}/${env.vaapiRepo}/vpu_test_allure_result.tar.gz", env.reportOssPath)
        artifact.uploadTestReport("${env.WORKSPACE}/${env.vaapiRepo}/dmesg_ci_test_${BUILD_ID}.tar.gz", env.reportOssPath)
    }
    dir(env.vaapiRepo) {
        //python run_test.py generate test-report dir include allure report
        commonLib.allure('test-report')
    }
    sh '''
        sed -i 's/GRUB_CMDLINE_LINUX_DEFAULT="quiet splash i915.modeset=0"/GRUB_CMDLINE_LINUX_DEFAULT=""/' /etc/default/grub
        sudo update-grub
    '''
}

runner.start(env.runChoice, [main: {
    runPipeline([
        'setup in docker': [closure: { setUpinDocker() }],
        "${env.testLabel}": [closure: { runVpuTest() }],
    ], [disablePost: true, disablePre: true])
}, post: {
    runPipeline([
        'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
    ], [
        disablePre: true,
        post: {
            commonLib.reboot(env.NODE_NAME)
            artifact.uploadLog('/var/log/kern.log')
        }
    ])
}, pre: {
    runPipeline([
        'checkout': [closure: { fetchCode() }],
        'setup on node': [closure: { setUpOnNode() }]
    ], [disablePost: true])
}])
