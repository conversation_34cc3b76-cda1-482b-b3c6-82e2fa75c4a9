@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

// runChoice nodeLabel kmdPackageUrl umdPackageUrl

def installDriver() {
    if (env.musaPackageUrl) {
        println(env.musaPackageUrl)
        ddk.installMusa(env.musaPackageUrl)
    } else {
        ddk.install(true)
    }
}

def runOpenGLTest() {
    sh '''
        apt install mesa-utils -y || true
        update-pciids || true
        yes | sudo apt-get install pkg-config
        yes | apt install libmysqlclient-dev
        export MYSQLCLIENT_CFLAGS=$(mysql_config --cflags)
        export MYSQL_LDFLAGS="$(mysql_config --libs)"
        yes | pip3 install mysqlclient --index=http://mirrors.aliyun.com/pypi/simple --trusted-host=mirrors.aliyun.com
    '''
    dir('/root/') {
        gitLib.fetchCode('gcbs-gpu', 'master', null, [preBuildMerge: false])
        gitLib.fetchCode('gfxswtest', 'gcbs_ci', null, [preBuildMerge: false])
        sh 'cd gcbs-gpu/docs/utils/beforeRun && apt-get update && ./before-run.sh desktop'
    }
    commonLib.checkGlxinfo()
    sh '''
        rm -rf /root/gcbs-gpu/results/*
        cd /root/gcbs-gpu
        export DISPLAY=:0.0
        ./gcbs.sh -s 1 -t OpenGLSuite
    '''
}

def compareResult() {
    def nodeIP = commonLib.getNodeIP()
    def result = sh(script: "python3 /root/gfxswtest/ci_checkperf.py /root/gcbs-gpu/results/GCBS_single*/GCBS_single_*json ${nodeIP}", returnStdout: true).trim()
    print(result)
    if (result.contains('FAIL')) {
        def failMatches = (result =~ /FAIL: (.+?) ; original score: ([\d.]+) , new score: ([\d.]+)/)
        failMatches.each { match ->
            def failInfo = "FAIL: ${match[1]} ; original score: ${match[2]} , new score: ${match[3]}"
            currentBuild.description += "${failInfo}<br>"
        }

        throw new Exception('test failed!')
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'install driver': [closure: { installDriver() }],
        'openGL test': [closure: { runOpenGLTest() }],
        'compare result': [closure: { compareResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"]
    ]

    runPipeline(workflow, [
        post: {
            commonLib.reboot(env.NODE_NAME)
            artifact.uploadLog('/var/log/kern.log')
        }
    ])
}
