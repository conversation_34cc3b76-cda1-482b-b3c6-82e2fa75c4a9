@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

// runChoice nodeLabel branch commitId testMark testType compileArgs compileParallelNumber envExport enablePtsz enableAssembler umdPackageUrl mtccPackageUrl musaRuntimePackageUrl musaToolkitsPackageUrl musifyPackageUrl generateTestUrl

env.repo = 'musa_cts'
ASAN_LIB = env.testType == 'smoke' && env.isASAN != 'false' ? 'libasan.so.5' : ''
generateTestUrl = env.generateTestUrl ? env.generateTestUrl.split(';') : []

def fetchCode() {
    gitLib.fetchCode(env.repo, env.musaCtsBranch, env.musaCtsCommitId)
}

def installPackage() {
    sh 'dmesg -T | grep Version ||:'
    // sh 'clinfo | grep Driver ||:'

    if (!env.musaRuntimePackageUrl) {
        def subJob = runPipeline.runJob([
            job: 'build.MUSA-Runtime',
            parameters: [
                pkgName: 'MUSA_Runtime',
                umdPackageUrl: env.umdPackageUrl,
                triggerInfo: env.triggerInfo
            ]
        ])
        def description = subJob.getDescription()
        def regx = /.*MUSA_Runtime\.tar\.gz:\s*(.*MUSA_Runtime\.tar\.gz)/
        def match = (description =~ regx).collect { m -> m[1] }
        env.musaRuntimePackageUrl = match ? match[0] : ''
    }

    def dependencies = [
        'gr-umd': [env.umdPackageUrl],
        'MUSA-Runtime': env.musaRuntimePackageUrl,
        'mtcc': env.mtccPackageUrl
    ]

    installDependency(dependencies)

    if (env.musifyPackageUrl) {
        musa.installMusify(env.musifyPackageUrl)
    }
    MUSA_PORTING_PATH = "export MUSA_PORTING_PATH=${env.WORKSPACE}/musify"
    def installArg = 'mublas mufft mupp muRAND muAlg muThrust muSparse'
    if (sh(script: 'ls /user/local/musa/tools/musify* ||:', returnStdout: true)) {
        MUSA_PORTING_PATH += ' ;unset MUSA_PORTING_PATH'
        installArg = 'muRAND muAlg muThrust muSparse'
    }
    dir(env.repo) {
        sh """
            apt install pciutils -y
            apt install sudo -y
            python ./scripts/rely_repo_install.py -pkg '${installArg}'
        """
    }
}

def runCtsTest() {
    dir(env.repo) {
        sh """
            ${env.envExport}
            ${MUSA_PORTING_PATH}
            cd ./scripts && ./porting_without_compile.sh
            cd ../elf && ./compile_elf.sh
            cd .. && mkdir build && cd build
            cmake .. ${compileArgs}
            make -j ${compileParallelNumber}
            cd googletest && make install
        """
    }
    dir(env.repo) {
        sh """
            ${env.envExport}
            export TIMEOUT_FACTOR=8
            export ASAN_LIB=${ASAN_LIB}
            ${MUSA_PORTING_PATH}
            export PATH=\$PATH:${env.WORKSPACE}/allure-2.19.0/bin
            export DISPLAY=:0.0
            Xorg &
            cd ./pytest
            pip3 install pytest-xdist
            pytest -v -m '${env.testMark}' --alluredir=allure_result_musa_cts ||:
            tar -zcvf musa_cts_allure_result.tar.gz allure_result_musa_cts/ ||:
        """
    }
    if (env.enablePtsz == 'true') {
        dir(env.repo) {
            sh """
                ${env.envExport}
                export ASAN_LIB=${ASAN_LIB}
                ${MUSA_PORTING_PATH}
                export PATH=\$PATH:${env.WORKSPACE}/allure-2.19.0/bin
                mkdir build_ptsz && cd build_ptsz
                cmake .. -DMUSA_API_PER_THREAD_DEFAULT_STREAM=on ${compileArgs}
                make -j ${compileParallelNumber}
                cd ../pytest_ptsz
                pytest -v -m '${env.testMark}' --alluredir=allure_result_musa_cts_ptsz ||:
                tar -zcvf musa_cts_ptsz_allure_result.tar.gz allure_result_musa_cts_ptsz/ ||:
            """
        }
    }
    if (env.enableAssembler == 'true') {
        dir(env.repo) {
            sh """
                ${env.envExport}
                export ASAN_LIB=${ASAN_LIB}
                ${MUSA_PORTING_PATH}
                export PATH=\$PATH:${env.WORKSPACE}/allure-2.19.0/bin
                export RUN_TYPE=assembler
                cd ./pytest
                pip3 install pytest-xdist
                pytest -v -n 6 --alluredir=allure_result_musa_cts_assembler test_musa_mtcc.py  ||:
                pytest -v -n 6 --alluredir=allure_result_musa_cts_assembler test_musa_mtcc_fuzz.py  ||:
                tar -zcvf musa_cts_assembler_allure_result.tar.gz allure_result_musa_cts_assembler/ ||:
            """
        }
    }
    gcov = false
    if (sh(script: 'find . -name \'*.gcda\'', returnStdout: true)) {
        gcov = true
        sh """
            tar -czvf musa_cts_test_gcov_gcda.tar.gz `find . -name '*.gcda'`
            find . -name '*.gcda' | wc -l
        """
    }

    // code coverage
    gcno = false
    if (env.testType == 'daily') {
        if (sh(script: 'find ./MUSA-Runtime/build -name \'*.gcno\'', returnStdout: true)) {
            gcno = true
            sh """
                apt-get update ||:
                apt-get install -y lcov ||:
                cd ./MUSA-Runtime/build
                lcov --directory . --capture --output-file tmp.info --rc lcov_branch_coverage=1
                lcov --remove tmp.info '/usr/include/*' '/usr/lib/*' -o result_musart_test.info --rc lcov_branch_coverage=1
                mkdir -p info
                mv result_musart_test.info ./info
                cd ${env.WORKSPACE}
                mv MUSA-Runtime/ info/
                tar -czf result_musart_coverity.tgz info/
            """
        }
    }
}

def checkResult() {
    dir("${env.WORKSPACE}/${env.repo}") {
        def reportDir = 'pytest/allure_result_musa_cts'
        if (env.enablePtsz == 'true') {
            reportDir += ',pytest_ptsz/allure_result_musa_cts_ptsz'
        }
        if (env.enableAssembler == 'true') {
            reportDir += ',pytest/allure_result_musa_cts_assembler'
        }
        commonLib.allure(reportDir)
    }
}

def uploadTestResult() {
    oss.install()
    generateTestUrl.each {
        oss.cp("${env.WORKSPACE}/${env.repo}/pytest/musa_cts_allure_result.tar.gz", it)
        currentBuild.description += "<br>generate_pkg:${it}/musa_cts_allure_result.tar.gz".replaceAll('//', '/')
        if (env.enablePtsz == 'true') {
            oss.cp("${env.WORKSPACE}/${env.repo}/pytest_ptsz/musa_cts_ptsz_allure_result.tar.gz", it)
            currentBuild.description += "<br>generate_pkg:${it}/musa_cts_ptsz_allure_result.tar.gz".replaceAll('//', '/')
        }
        if (env.enableAssembler == 'true') {
            oss.cp("${env.WORKSPACE}/${env.repo}/pytest/musa_cts_assembler_allure_result.tar.gz", it)
            currentBuild.description += "<br>generate_pkg:${it}/musa_cts_assembler_allure_result.tar.gz".replaceAll('//', '/')
        }
        if (gcov) {
            oss.cp('musa_cts_test_gcov_gcda.tar.gz', it)
            currentBuild.description += "<br>generate_pkg:${it}/musa_cts_test_gcov_gcda.tar.gz".replaceAll('//', '/')
        }
        if (gcno) {
            oss.cp('result_musart_coverity.tgz', it)
            currentBuild.description += "<br>generate_pkg:${it}/result_musart_coverity.tgz".replaceAll('//', '/')
        }
    }
}

runner.start(env.runChoice, [main: {
    runPipeline([
        'checkout': [closure: { fetchCode() }],
        'install package': [closure: { installPackage() }, maxWaitTime: [time: 115, unit: 'MINUTES']],
        "${env.testLabel}": [closure: { runCtsTest() }, maxWaitTime: [time: 45, unit: 'MINUTES']]
    ], [disablePost: true])
}, post: {
    runPipeline([
        'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
        'upload result': [closure: { uploadTestResult() } ]
    ], [disablePre: true])
}, pre: {
    if (env.runChoice == 'node') {
        stage('install ddk') {
            // restart machine
            commonLib.reboot(env.NODE_NAME)
            // env recovery
            commonLib.recoverEnv()
            installDependency([
                'gr-kmd': [env.kmdPackageUrl],
                'gr-umd': [env.umdPackageUrl]
            ])
            sh 'update-initramfs -u -k \$(uname -r)'
            commonLib.reboot(env.NODE_NAME)
            sh 'modprobe mtgpu'
        }
    }
}])
