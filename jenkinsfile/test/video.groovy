@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

// runChoice nodeLabel targetRepo fwBinUrl hostArch osType isCodeCoverage reportDate mediaPkgUrl kmdPackageUrl umdPackageUrl

env.targetRepo = env.targetRepo ?: 'mt-media-driver'

def installDriver() {
    commonLib.installDriver()
}

def runTest() {
    docker.image(env.dockerImage).inside('-i -u 0:0 --privileged=true') {
        sh '''
            ./test.sh
        '''
    }
}

runner.start(env.runChoice) {
    runPipeline([
      'install driver': [closure: { installDriver() }],
      'run test': [closure: { runTest() }]
    ])
}
