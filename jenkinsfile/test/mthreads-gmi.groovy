@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

/*
 * parameters
 * repo (String) - mthreads-gmi
 * branch (String) - develop
 * commitId (String) - null
 * date (String) - null
 * containerImage (String) - sh-harbor.mthreads.com/sdk/management:v10
 * linuxDdkPackageUrl (String) - null
 * gmiPackageUrl (String) - null
 * runCov (String) - true
 * testLabel (String) - jenkins/gmi_test
 * runChoice (Choice) - node [node | pod]
 * nodeLabel (String) - S50 && Ubuntu22.04 && x86_64
*/

gitLib = new git()
commonLib = new common()
versionType = ''
env.mountParms = '-v /dev:/dev -v /sys:/sys'

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, [disableSubmodules: true, updateBuildDescription: true])
    versionType = utils.runCommandWithStdout("egrep -A1 'gmi_version' ${env.repo}/config.ini | grep 'version = ' | awk -F '=' '{print \$2}'")
    def date = env.date ?: new Date().format('yyyy.MM.dd')
    if (env.gitlabTargetBranch == 'develop') {
        versionType = date
    }
}

def runTest() {
    def gmiPackageUrl = env.gmiPackageUrl ?: constants.genPackageUrl(env.repo, env.branch, env.commitId, "${env.repo}_${versionType}.tar.gz")
    constants.downloadAndUnzipPackage(gmiPackageUrl)
    sh """
        export GCOV_PREFIX="${env.WORKSPACE}"
        export GCOV_PREFIX_STRIP=6
        ./test/bin/x86_64/mthreads_gmi_googletest --gtest_filter=-*Vgpu*:*Mpc*:TopoGpuPathParamTest.*:Decode*:Encoder*
    """
    if (env.runCov == 'true') {
        dir('build') {
            sh """
                lcov -d . -c -o report.info --branch-coverage --ignore-error=mismatch --ignore-errors source
                lcov --remove report.info '*/usr/*' '*/3rd/*' '*/test/*' '*/mtml/*' -o report.info --branch-coverage
                sed -i -e 's|SF:/home/<USER>/agent/workspace/build.mthreads-gmi|SF:${env.WORKSPACE}|g' report.info
                genhtml --filter branch,function,line --branch-coverage -o htmlReport report.info --ignore-errors source
            """
            commonLib.publishHTML('htmlReport', '*.html', 'HTML Report')
        }
    }
}

runner.start(env.runChoice, [
    pre: {
        runPipeline([
            'fetch code': [ closure: { fetchCode() } ],
            'install ddk': [ closure: { ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl) } ]
        ], [disablePost: true])
    },
    main: {
        runPipeline([
            'gmi test': [ closure: { runTest() }, setGitlabStatus: true, statusName: env.testLabel ]
        ], [disablePre: true])
    }
])
