@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

// runChoice nodeLabel branch commitId testMark testType compileArgs compileParallelNumber envExport enablePtsz enableAssembler umdPackageUrl mtccPackageUrl musaRuntimePackageUrl musaToolkitsPackageUrl musifyPackageUrl generateTestUrl

musaCtsRepo = 'musa_benchmarks_cts'
def fetchCode() {
    gitLib.fetchCode(musaCtsRepo, env.branch, env.commitId)
}

def setUpOnNode() {
    // restart machine
    commonLib.reboot(env.NODE_NAME)
    // env recovery
    commonLib.recoverEnv()
    ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
// modprobe mtgpu
}

def installPackage() {
    ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    def subModulePackageUrl = env.subModulePackageUrl ?: ''
    if (subModulePackageUrl) {
        ddk.installLinuxDdk(subModulePackageUrl)
    }
    if (env.musaRuntimePackageUrl) {
        musa.installMusaRuntime(env.musaRuntimePackageUrl)
    }
    installDependency(['mtcc': env.mtccPackageUrl])
}

def runTest() {
    def timeOut = env.TIMEOUT ?: 40
    timeout(time: timeOut, unit: 'MINUTES') {
        dir(musaCtsRepo) {
            sh """
                ${env.envExport}
                cd pytest
                pytest . -sv -m ${testMark} --alluredir=allure_result_musa_cts ||:
            """
        }
    }
}

def checkScore() {
    dir(musaCtsRepo) {
        sh 'apt install mysql-client -y'
        def linuxDdkBranch = env.gitlabTargetBranch ?: env.dailyDdkBranch
        def select = ['linux_node_name':['=', env.NODE_NAME], 'base':['=', 'true'], 'linuxddk_branch':['=', env.ddkBranch ?: linuxDdkBranch]]
        def dbreturn = db.selectData('swqa_ci', 'linux_musa_benchmark_data', select, 'score,job_id,oss_path')
        if (!dbreturn) {
            error "node ${env.NODE_NAME} doesn't have base information yet but data is updated to database"
        }

        def baseJobID = dbreturn ? dbreturn.split('\n')[-1].split()[1] : '0'
        def baseScoreOssPath = dbreturn ? dbreturn.split('\n')[-1].split()[2] : 'null'
        sh """
            cd ${env.WORKSPACE}/${musaCtsRepo}
            wget ${baseScoreOssPath} -O base.tar.gz
            rm -rf base && mkdir base && tar -zxvf base.tar.gz -C base
        """
        def baseScoreDir = "${env.WORKSPACE}/${musaCtsRepo}/base/log/musa_perf_score"
        def actualScoreDir = "${env.WORKSPACE}/${musaCtsRepo}/log/musa_perf_score"

        catchError(buildResult: null, stageResult: null) {
            def output = sh(
                script: "cd scripts && python3 compare_diff.py -base ${baseScoreDir} -actual ${actualScoreDir}",
                returnStdout: true
            ).trim()

            def results = readJSON text: output
            def totalScore = results.total_score_info.actual_score
            def baseScore = results.total_score_info.base_score
            def percentChange = results.total_score_info.percent_change
            def absDiff = results.total_score_info.abs_diff

            currentBuild.description += "baseScore: ${baseScore} mrScore: ${totalScore} percentValue: ${percentChange} absDiffValue: ${absDiff}<br>"
            currentBuild.description += "baseScoreUrl: ${baseScoreOssPath}<br>"

            if (results.has_failures) {
                for (failure in results.failures) {
                    echo "${failure.name}, base score ${failure.base_score}, actual score ${failure.actual_score}, percent change ${failure.percent_change}"
                }
                error "test failed! Please download ${constants.ossPathToUrl(env.reportOssPath)}/musa_perf_score.tar.gz and find @qingnian.zuo"
            } else {
                def data = [
                    job_id: env.BUILD_ID,
                    linux_node_name: env.NODE_NAME,
                    linuxddk_commitId: env.gitlabTargetRepoName == 'linux-ddk' ? env.gitlabMergeRequestLastCommit : '',
                    musa_runtime_commitId: env.gitlabTargetRepoName == 'MUSA-Runtime' ? env.gitlabMergeRequestLastCommit : '',
                    score: totalScore,
                    base: env.testType == 'daily',
                    linuxddk_branch: env.ddkBranch ?: linuxDdkBranch,
                    mr_id: env.gitlabMergeRequestIid ?: '',
                    percent_change_value: percentChange,
                    abs_diff_value: absDiff,
                    oss_path: "${constants.ossPathToUrl(env.reportOssPath)}/musa_perf_score.tar.gz",
                    baseline_time: env.testType == 'daily' ? new Date().format('yyyy-MM-dd HH:mm:ss') : ''
                ]
                db.insertData('swqa_ci', 'linux_musa_benchmark_data', data)
                if (baseJobID != '0' && env.testType == 'daily') {
                    db.updateData('swqa_ci', 'linux_musa_benchmark_data', ['base':'false'], ['job_id': ['=', baseJobID]])
                }
            }

            if (results.has_warnings) {
                def warningData = ''
                for (warning in results.warnings) {
                    warningData += "| ${warning.name} | ${warning.base_score} | ${warning.actual_score} | ${warning.percent_change} | \\n"
                }
                def webhook = 'https://oapi.dingtalk.com/robot/send?access_token=363bf2330357a9ad59dd860bcf22a477b391fc6a0d8815288bc5e368c6899806'
                def message = "# musa_Runtime benchMark test Warning \\n | CaseName | BaseValue | ActualValue | PercentDiff | \\n | ------- | ------- | ------- | ------- | \\n ${warningData} \\n ## Jenkins BuildURL \\n [${env.BUILD_URL}](${env.BUILD_URL}) \\n ## Perf DataURL \\n [${constants.ossPathToUrl(env.reportOssPath)}/musa_perf_score.tar.gz](${constants.ossPathToUrl(env.reportOssPath)}/musa_perf_score.tar.gz) \\n ## MR URL \\n [${env.mergeRequestLink ?: 'No Merge Info'}](${env.mergeRequestLink ?: 'No Merge Info'})"
                sh """
                    curl '${webhook}' -H 'Content-Type: application/json' -d '{"msgtype": "markdown", "markdown": {"title": "Musa-Runtime benchmark run Warning", "text": "${message}"}}' ||:
                """
            }

            echo 'Performance test passed.'
        }
    }
}

def uploadTestResult() {
    dir("${musaCtsRepo}/pytest") {
        commonLib.allure('allure_result_musa_cts')
    }

    dir(musaCtsRepo) {
        sh """
            cd musa_benchmarks/scripts
            python3 calculateScoreOfSuit.py --test ${env.WORKSPACE}/${musaCtsRepo}/log/musa_perf/ --score ${env.WORKSPACE}/${musaCtsRepo}/log/musa_perf_score/
        """
        sh 'tar -czvf musa_perf_score.tar.gz log/'
        artifact.uploadTestReport('musa_perf_score.tar.gz', env.reportOssPath)
    }
}

runner.start(env.runChoice, [pre: {
    def workflow = [
        'setUpOnNode': [closure: { setUpOnNode() }],
    ]
    runPipeline(workflow, [disablePost: true])
}, main: {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'setup': [closure: { installPackage() }],
        "${env.testLabel}": [closure: { runTest() }]
    ]
    runPipeline(workflow, [disablePre: true, disablePost: true])
}, post: {
    runPipeline([
        'upload result': [closure: { uploadTestResult() }],
        'check result': [closure: { checkScore() }, setGitlabStatus: true, statusName: "${env.testLabel}"]
    ], [disablePre: true])
}])
