@Library('swqa-ci')

import org.swqa.tools.common

commonLib = new common()

// runChoice nodeLabel linuxDdkPackageUrl kmdPackageUrl umdPackageUrl runGlmark2 ratedGlmark2Score runGlxgears ratedGlxgearsScore runUnixBench ratedUnixbenchScore

def installDriver() {
    if (env.linuxDdkPackageUrl) {
        ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl, true)
    } else {
        ddk.install(true)
    }
}

def runGFXBench(String type, String manhattanSwitch, String DISTRIB_ID) {
    timeout(time: 10, unit: 'MINUTES') {
        commonLib.startXorg()
        def gfxBenchFps = 0
        def logPath = "${env.WORKSPACE}/${type}_${manhattanSwitch}_${DISTRIB_ID}.log"
        def testApp = '/home/<USER>/gfxbench_gl-src-5.0.4+corporate/tfw-pkg/bin/testfw_app'
        def vblank = manhattanSwitch.endsWith('_off') ? '' : 'vblank_mode=0'
        def execParams = type == 'gles' ? '--gfx egl --gl_api=gles' : '--gfx glfw'
        try {
            sh """
                export DISPLAY=:0.0
                ${vblank} ${testApp} ${execParams} -w 1920 -h 1080 -t ${manhattanSwitch} | tee -a ${logPath}
            """
            gfxBenchFps = sh(script: "cat ${logPath} |grep '\"fps\":' ", returnStdout: true).trim()
            print(gfxBenchFps)
            gfxBenchFps = gfxBenchFps.length() < 16 ? gfxBenchFps[6..-1] : gfxBenchFps[6..15]
            sh 'sleep 5'
        } catch (exc) {
            print("${type}_${manhattanSwitch} Test Exception: ${exc}")
        }
        currentBuild.description += "gfxBench: ${type}_${manhattanSwitch}: ${gfxBenchFps}<br>"
        return gfxBenchFps
    }
}

def runPerfTest() {
    hasFailed = false
    def DISTRIB_ID = sh(script: "cat /etc/lsb-release | head -n 1 | awk -F '='  '{print \$2}'", returnStdout: true).split('HEAD')[-1].trim()
    // get glmark2 score
    if (env.runGlmark2.toBoolean()) {
        timeout(time: env.glmarkTimeout, unit: 'MINUTES') {
            commonLib.startXorg()
            def glmark2LogPath = "${env.WORKSPACE}/glmark2_perf_${DISTRIB_ID}.log"
            for (num in [1, 2, 3]) {
                def returnValueLog = "${env.WORKSPACE}/glmark2_perf_${DISTRIB_ID}_${num}.log"
                sh """
                    export DISPLAY=:0.0 && glmark2 2>&1 | tee -a ${glmark2LogPath}
                    echo \$? > ${returnValueLog}
                """
                def returnValue = sh(script: "cat ${returnValueLog}", returnStdout: true).trim()
                if (returnValue != '0') {
                    def score = sh(script: "cat ${glmark2LogPath} | grep 'glmark2 Score' | awk -F ': ' '{score = \$2} END {print score}'", returnStdout: true).trim()
                    println "${num} glmark2 score: ${score}"
                    currentBuild.description += "${num} glmark2 score: ${score}<br>"
                    hasFailed = true
                    error("glmark2 error: process exited with ${returnValue}")
                }
                sh '''
                    killall -9 glmark2 ||:
                    sleep 5
                '''
            }
            def glmark2Score = sh(script: "cat ${glmark2LogPath} | grep 'glmark2 Score' | awk -F ': ' '{sum += \$2;num += 1} END {print sum/num}'", returnStdout: true).trim()
            currentBuild.description += "glmark2: ${glmark2Score}-${env.ratedGlmark2Score}<br>"
            if (glmark2Score.toDouble() <= env.ratedGlmark2Score.toDouble() * 0.95) {
                hasFailed = true
                error("glmark2 failed: actual score(${glmark2Score}) < expected score(${ratedGlmark2Score} x 0.95)")
            }
            if (commonLib.getDmesgErrors()) {
                hasFailed = true
                error('Glmark2 failed.')
            }
        }
    }
    // get glxgears score
    if (env.runGlxgears.toBoolean()) {
        def glxgearsLogPath = "${env.WORKSPACE}/glxgears_perf_${DISTRIB_ID}.log"
        commonLib.startXorg()
        sh """
            export DISPLAY=:0.0 && vblank_mode=0 glxgears 2>&1 | tee -a ${glxgearsLogPath} &
            sleep 120
            killall -9 glxgears ||:
        """
        def glxgearsScore = sh(script: "cat  ${glxgearsLogPath}  | awk -F ' '  '{sum +=\$7;num +=1} END {print sum/(num-1)}'", returnStdout: true).trim()
        currentBuild.description += "glxgears: ${glxgearsScore}-${env.ratedGlxgearsScore}<br>"
        if (glxgearsScore.toDouble() <= env.ratedGlxgearsScore.toDouble() * 0.95) {
            hasFailed = true
            error("glxgears failed: actual score(${glxgearsScore}) < expected score(${ratedGlxgearsScore} x 0.95)")
        }
        if (commonLib.getDmesgErrors()) {
            hasFailed = true
            error('Glxgears failed.')
        }
    }
    // get UnixBench score
    if (env.runUnixBench.toBoolean()) {
        timeout(time: env.unixBenchTimeout, unit: 'MINUTES') {
            def unixBenchLogPath = "${env.WORKSPACE}/unixbench_perf_${DISTRIB_ID}.log"
            commonLib.startXorg()
            sh """
                export DISPLAY=:0.0
                cd /home/<USER>/UnixBench && ./Run graphics 2>&1 | tee -a ${unixBenchLogPath}
            """
            def unixBenchScore = sh(script: "cat ${unixBenchLogPath} | grep '2D Graphics Benchmarks Index Score' | awk -F 'Score' '{sum +=\$2;num +=1} END {print sum/num}'", returnStdout: true).trim()
            currentBuild.description += "UnixBench: ${unixBenchScore}-${env.ratedUnixbenchScore}<br>"
            if (unixBenchScore.toDouble() <= env.ratedUnixbenchScore.toDouble() * 0.95) {
                hasFailed = true
                error("UnixBench failed: actual score(${unixBenchScore}) < expected score(${ratedUnixbenchScore} x 0.95)")
            }
            if (commonLib.getDmesgErrors()) {
                hasFailed = true
                error('UnixBench failed.')
            }
        }
    }
    //get gfxBench score
    if (env.runGfxBench.toBoolean()) {
        glesMHT31off_fps = runGFXBench('gles', 'gl_manhattan31_off', DISTRIB_ID)
        glesMHT31_fps = runGFXBench('gles', 'gl_manhattan31', DISTRIB_ID)
        glesMHToff_fps = runGFXBench('gles', 'gl_manhattan_off', DISTRIB_ID)
        glesMHT_fps = runGFXBench('gles', 'gl_manhattan', DISTRIB_ID)
        glMHToff_fps = runGFXBench('gl', 'gl_manhattan_off', DISTRIB_ID)
        glMHT_fps = runGFXBench('gl', 'gl_manhattan', DISTRIB_ID)
        if (commonLib.getDmesgErrors()) {
            hasFailed = true
            error('GfxBench failed.')
        }
    }
}

runner.start(env.runChoice) {
    runPipeline([
        'install driver': [closure: { installDriver() }, maxWaitTime: [time: 15, unit: 'MINUTES']],
        'perf test': [closure: { runPerfTest() }, setGitlabStatus: true, statusName: env.testLabel]
    ], [
        post: {
            if (hasFailed) { sh 'dmesg -T' }
            commonLib.reboot(env.NODE_NAME)
            artifact.uploadLog('/var/log/kern.log')
        }
    ])
}
