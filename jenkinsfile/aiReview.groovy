@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

def fetchCode() {
    gitLib.fetchCode(env.gitlabSourceRepoName, env.gitlabSourceBranch)
}

def codeReview() {
    dir(env.gitlabSourceRepoName) {
        ai.codeReview()
    }
}

runner.start(env.runChoice) {
    gitLib.setGitlabStatus('jenkins/ai_analysis', 'canceled')
    gitLib.setGitlabStatus('jenkins/ai_analysis', 'running')

    runPipeline([
        'checkout': [ closure: { fetchCode() } ],
        'AI review': [ closure: { codeReview() }]
    ])
}
