@Library('swqa-ci')

import org.swqa.tools.git

//author: liya
//email: <EMAIL>

def wddm_commitId
def kmd_commitId
def branch

if (env.guest_driver_url) {
    def urlParts = env.guest_driver_url.split('/')
    wddm_commitId = urlParts[-2]
    branch = urlParts[-3]
} else {
    kmd_commitId = env.buildCommitId ?: env.master_commit_id ?: 'unknown'
    branch = env.buildBranch ?: env.ref?.split('refs/heads/')?.last() ?: 'unknown'

    def latest_url_vdi = "https://oss.mthreads.com/sw-build/VDI/${branch}/latest.txt"
    def vdi_pkg_url = ''
    node('Linux_jump') { vdi_pkg_url = sh(script: "curl --insecure ${latest_url_vdi}", returnStdout: true).trim() }

    def pkgParts = vdi_pkg_url.split('/')
    wddm_commitId = pkgParts.last().split('_')[0] ?: 'unknown'

    env.guest_driver_url = "${vdi_pkg_url}/${pkgParts.last()}_wddm_vdi_release.tar.gz"
    env.m3d_test_url = "${vdi_pkg_url}/${pkgParts.last()}_m3dTest.tar.gz"
    env.directstream_test_url = "${vdi_pkg_url}/${pkgParts.last()}_directstreamTest.tar.gz"
}

echo "wddm_commitId: ${wddm_commitId}"
echo "branch: ${branch}"
echo "guest_driver_url: ${env.guest_driver_url}"

def loadCiConfig() {
    def ciConfigContent = libraryResource('conf/vdi/winvdiconfig270000.yaml')
    def ciConfig = readYaml(text: ciConfigContent)
    echo "ciConfig: ${ciConfig}"
    echo 'ciConfig loaded successfully.'

    def vdikmdConfig = ciConfig.vdi_kmd
    pkgVersion = ciConfig.pkg_version

    return vdikmdConfig
}

def fetchHostCode(String branch) {
    if (env.guest_driver_url) {
        new git().fetchCode('gr-kmd', branch)
    }else {
        new git().fetchCode('gr-kmd', branch, kmd_commitId)
    }
    new git().updateSubmodule('gr-kmd')

    ciConfig = loadCiConfig()

    dir('gr-kmd') {
        sh 'git submodule'
    }
}

def fetchGuestDriver() {
    def guest_driver_pkg_name = env.guest_driver_url.split('/')[-1]

    dir('vdi_guest') {
        sh """
            wget -q "${env.guest_driver_url}" --no-check-certificate
            tar xvzf "${guest_driver_pkg_name}" || exit 1
        """
    }
}

def build(Map buildConfig, String branch, String wddm_commitId, String kmd_commitId, String ossBranchPath) {
    def builds = ciConfig.builds
    def commonCmd = builds.common_cmd

    def parallelSteps = [:]
    def ubuntuBuildDone = false

    builds.platforms.each { platform, buildList ->
        parallelSteps[platform] = {
            buildList.each { build ->
                def pkgName = build.pkgName.replace('${pkg_version}', pkgVersion).replaceAll('-000', '')
                def dockerImage = build.dockerImage
                def cmd = adjustCmdFormat(build.cmd.replace('${common_cmd}', commonCmd).replace('${pkg_version}', pkgVersion))

                echo "Platform: ${platform}"
                echo "Package Name: ${pkgName}"
                echo "Docker Image: ${dockerImage}"
                echo "Command: ${cmd}"

                dir("${platform}") {
                    lock("vdi-lock_${env.BUILD_NUMBER}") {
                        fetchHostCode(branch)
                    }

                    if (platform in ['rpm', 'deb']) {
                        // Wait for Ubuntu build to complete
                        timeout(30) {
                            while (!ubuntuBuildDone) {
                                echo 'Waiting for Ubuntu build to complete...'
                                sleep(10)
                            }
                        }
                        dkms_build(wddm_commitId, kmd_commitId, dockerImage, "${cmd}")
                        retry(2) { uploadDkms(pkgName, platform, wddm_commitId, kmd_commitId, ossBranchPath) }
                    } else {
                        docker.image(dockerImage).inside {
                            dir('gr-kmd') {
                                sh cmd
                            }
                        }
                        retry(2) { packagedriver(pkgName, branch, wddm_commitId, kmd_commitId, ossBranchPath) }
                        if (platform == 'Ubuntu') {
                            ubuntuBuildDone = true
                        }
                    }
                }
            }
        }
    }

    parallel parallelSteps
}

def dkms_build(String wddm_commitId, String kmd_commitId, String dockerImage, String cmd, String buildDir = 'Ubuntu') {
    def driverDir = "${env.WORKSPACE}/${buildDir}/${wddm_commitId}_${kmd_commitId}_kmd_${buildDir}_release"
    def firmwareDir = 'gr-kmd/ci_win_fw'

    sh """
        mkdir -p ${firmwareDir} && \
        cp -rf ${driverDir}/firmware/*.bin ${firmwareDir} && \
        cp -rf ${driverDir}/firmware/*.dict ${firmwareDir} && \
        cp -rf ${driverDir}/firmware/*.win ${firmwareDir}
    """

    docker.image(dockerImage).inside {
        dir('gr-kmd') {
            sh cmd
        }
    }
}

def packagedriver(String pkgName, String branch, String wddm_commitId, String kmd_commitId, String ossBranchPath) {
    def buildDir = "${wddm_commitId}_${kmd_commitId}_${pkgName}"
    def package_name = "${wddm_commitId}_${kmd_commitId}_${pkgName}.tar.gz"

    oss.setUp()

    sh """
        mkdir -p ${buildDir}/firmware && \
        cp -rf gr-kmd/mtvpu/firmware/*.bin ${buildDir}/firmware && \
        cp -rf gr-kmd/dkms/dkms.post_install.vz ${buildDir} && \
        cp -rf ${env.WORKSPACE}/vdi_guest/*.win ${buildDir}/firmware && \
        cp -rf ${env.WORKSPACE}/vdi_guest/*.dict ${buildDir}/firmware && \
        cp gr-kmd/binary_*/target_x86_64/kbuild/mtgpu.ko ${buildDir} && \
        tar -cvzf ${package_name} -C ${buildDir} . && \
        mc cp ${package_name} oss/${ossBranchPath}/
    """

    currentBuild.description += "Driver binary: https://oss.mthreads.com/${ossBranchPath}/${package_name}<br>"
}

def adjustCmdFormat(String cmd) {
    def envVarPattern = /(\w+=[^\s]+)/
    def envVars = []
    def commandParts = []
    def lastOption = ''

    cmd.tokenize().each { part ->
        if (part ==~ envVarPattern) {
            envVars << part
        } else if (part.startsWith('-j32')) {
            lastOption = part
        } else {
            commandParts << part
        }
    }

    def adjustedCmd = "${commandParts.join(' ')} ${envVars.join(' ')} ${lastOption}".trim()
    return adjustedCmd
}

def uploadDkms(String pkgName, String platform, String wddm_commitId, String kmd_commitId, String ossBranchPath) {
    try {
        def targetDirPattern = 'gr-kmd/binary_*/target_x86_64'
        def fileName = "${pkgName}.${platform}"
        def destinationFile = "${wddm_commitId}_${kmd_commitId}_${fileName}"
        def ossPath = "oss/${ossBranchPath}/${destinationFile}"

        oss.setUp()

        def targetDir = sh(script: "find gr-kmd -type d -path '${targetDirPattern}' | head -n 1", returnStdout: true).trim()
        if (targetDir) {
            dir(targetDir) {
                if (fileExists(fileName)) {
                    echo "Moving ${fileName} to ${destinationFile}"
                    sh "mv ${fileName} ${destinationFile}"

                    echo "Uploading ${destinationFile} to OSS at ${ossPath}"
                    sh "mc cp ${destinationFile} ${ossPath}"

                    def fileUrl = "https://oss.mthreads.com/${ossBranchPath}/${destinationFile}"
                    currentBuild.description += "Driver binary: ${fileUrl} <br>"
                    echo "File successfully uploaded: ${fileUrl}"
                } else {
                    error "File ${fileName} not found in ${targetDir}"
                }
            }
        } else {
            error "Target directory not found for pattern ${targetDirPattern}"
        }
    } catch (e) {
        echo "Error occurred during upload: ${e.message}"
        error "Upload failed: ${e}"
    }
}

def getRenamedFileName(String originalFileName, String branchName) {
    return originalFileName.replaceFirst(/^([a-zA-Z0-9]{9}(_[a-zA-Z0-9]{9}){0,2}_)/, branchName + '_')
}

def rename_guest_file(String ossBranchPath) {
    def ossBranchName = ossBranchPath.split('/')[-1]

    def fileMap = [
        'guest_driver': env.guest_driver_url,
        'm3d_test': env.m3d_test_url,
        'directstream_test': env.directstream_test_url
    ]

    dir('vdi_guest') {
        fileMap.each { key, url ->
            if (key != 'guest_driver') {
                sh "wget -q \"${url}\" --no-check-certificate"
            }
        }

        oss.setUp()

        fileMap.each { key, url ->
            def originalFileName = url.split('/')[-1]
            def renamedFileName = getRenamedFileName(originalFileName, ossBranchName)
            if (key == 'guest_driver') {
                new_guest_driver_url = "https://oss.mthreads.com/${ossBranchPath}/${renamedFileName}"
            }

            sh "mv ${originalFileName} ${renamedFileName} || true"
            sh "mc cp ${renamedFileName} oss/${ossBranchPath}/"
        }
    }
}

def sign(String guest_driver_url, String ossBranchPath) {
    build job: 'win-sign', wait:false, parameters: [
        string(name: 'driver_url', value: guest_driver_url),
        string(name: 'oss_save_path', value: ossBranchPath)
    ]
}

runner.start(env.runChoice) {
    deleteDir()
    fetchHostCode(branch)
    kmd_commitId = new git().getCurrentCommitID('gr-kmd')
    def ossBranchPath = constants.genOssPath('VDI', branch, wddm_commitId) + "_${kmd_commitId}"
    def workflow = [
        'fetchGuestDriver': [closure: { fetchGuestDriver() }],

        'build': [closure: { build(ciConfig, branch, wddm_commitId, kmd_commitId, ossBranchPath) }],

        'renameGuestFile': [closure: { rename_guest_file(ossBranchPath) }],

        'updateLatestTxt': [closure: { winBuild.updateLatestTxt(ossBranchPath) }],

        'sign' : [closure: { sign(new_guest_driver_url, ossBranchPath) }]
    ]

    runPipeline(workflow, [disablePost:true])
}
