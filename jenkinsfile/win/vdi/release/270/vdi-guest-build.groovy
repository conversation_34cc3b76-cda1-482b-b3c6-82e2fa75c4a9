@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

//author: liya
//email: <EMAIL>

tasksToRemove = []
disableSubmoduleUpdate = ['import/mtcc']

def time_tag_name = new Date().format('MM/dd/yyyy')
def version = "${time_tag_name}"

if (env.buildM3d != 'true') {
    tasksToRemove << 'm3d'
}

if (env.buildDirectstream != 'true') {
    tasksToRemove << 'directstream'
}

if (env.buildDxc != 'true') {
    tasksToRemove << 'dxc'
    disableSubmoduleUpdate << 'dxc'
}

if (env.buildOGL != 'true') {
    tasksToRemove << 'ogl'
    disableSubmoduleUpdate << 'ogl'
}

if (env.buildVulkan != 'true') {
    tasksToRemove << 'Vulkan'
    disableSubmoduleUpdate << 'Vulkan'
}

def initGitlabStatus() {
    env.debug = 'false'
    List names = env.initGitlabStatus.split(',').collect { "win/${it}" }
    /* groovylint-disable-next-line UnnecessarySetter */
    try { new git().setGitlabStatus(names) }catch (e) { print(e) }
}

def fetchCode() {
    def disable_submodule_update = disableSubmoduleUpdate.join(',')

    new git().fetchCode(env.repo, env.gitlabTargetBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true, updateBuildDescription: true])
    new git().updateSubmodule(env.repo, 1, '', disable_submodule_update)

    def directstreamBranch = env.gitlabTargetBranch

    if (env.buildDirectstream == 'true') {
        new git().fetchCode('DirectStream', directstreamBranch)
    }
    ciConfig = loadCiConfig()
}

def loadCiConfig() {
    def ciConfigContent = libraryResource('conf/vdi/winvdiconfig270000.yaml')
    def ciConfig = readYaml(text: ciConfigContent)
    echo 'ciConfig loaded successfully.'

    def vdiWddmConfig = ciConfig.vdi_wddm
    driverPath = vdiWddmConfig.product_paths.x64.common.release
    mtapi_url = ciConfig.mtapi_url
    mtml_url = ciConfig.mtml_url

    return vdiWddmConfig
}

def extendTestPkg() {
    //upload m3d test
    if (env.buildM3d == 'true') {
        winBuild.upload_m3d(env.repo, env.buildBranch, env.gitlabMergeRequestIid)
    }
    //upload directstream test
    if (env.buildDirectstream == 'true') {
        winBuild.upload_directstream(env.repo, env.buildBranch, env.gitlabMergeRequestIid)
    }
}

def executeVdiBuild(String guest_driver_url, String m3d_test_url, String directstream_test_url) {
    runPipeline.runJob([
        job: 'win-vdi-build',
        wait:false,
        parameters: [
            triggerInfo: env.triggerInfo,
            guest_driver_url: guest_driver_url,
            m3d_test_url: m3d_test_url,
            directstream_test_url: directstream_test_url
        ]
    ])
}

runner.start(env.runChoice) {
    def ossBranchPath = constants.genOssPath(env.gitlabSourceRepoName, env.gitlabTargetBranch, env.gitlabMergeRequestIid)
    def prefix = constants.genOssPrefix(ossBranchPath)
    def baseUrl = "${prefix}/${ossBranchPath}"
    def guest_driver_url = "${baseUrl}/${env.gitlabMergeRequestIid}_wddm_vdi_release.tar.gz"
    def m3d_test_url = "${baseUrl}/${env.gitlabMergeRequestIid}_m3dTest.tar.gz"
    def directstream_test_url = "${baseUrl}/${env.gitlabMergeRequestIid}_directstreamTest.tar.gz"
    //init
    new common().initEnv(env.NODE_NAME, "192.168.${env.NODE_NAME.split('_')[-1]}")

    deleteDir()

    def workflow = [
        'initGitlabStatus': [closure: { initGitlabStatus() }],

        'checkout': [closure: { fetchCode() }],

        'build': [closure: { winBuild.integratedBuildProcess(ciConfig, tasksToRemove, env.driver_env, env.driver_type, env.chip_type, env.build_type) }],

        'pkgProducts' : [closure: { winBuild.pkgProducts(ciConfig, env.driver_type, env.build_type, tasksToRemove) }],

        'fetchMtapi' : [closure: { winBuild.fetchMtapi(driverPath, mtapi_url) }],

        'fetchMtml' : [closure: { winBuild.fetchMtml(driverPath, mtml_url) }],

        'signature' : [closure: { winBuild.signature(ciConfig, env.driver_type, version) }],

        'upload' : [closure: { winBuild.upload(ciConfig, env.repo, env.buildBranch, env.gitlabMergeRequestIid, env.driver_type) }],

        'extendTestPkg' : [closure: { extendTestPkg() }],

        'executeVdiBuild' : [closure: { executeVdiBuild(guest_driver_url, m3d_test_url, directstream_test_url) }]
    ]

    runPipeline(workflow, [disablePost:true])
}
