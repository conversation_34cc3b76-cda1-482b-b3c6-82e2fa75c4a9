@Library('swqa-ci')

import groovy.transform.Field
import org.swqa.tools.git
import org.swqa.tools.common

//author: liya
//email: <EMAIL>

def time_tag_name = new Date().format('MM/dd/yyyy')
def version = "${time_tag_name}"

@Field List<String> tasksToRemove = []
@Field List<String> disableSubmoduleUpdate = ['import/mtcc']

if (env.buildM3d != 'true') {
    tasksToRemove << 'm3d'
}

if (env.buildDirectstream != 'true') {
    tasksToRemove << 'directstream'
}

if (env.buildDxc != 'true') {
    tasksToRemove << 'dxc'
    disableSubmoduleUpdate << 'dxc'
}

if (env.buildOGL != 'true') {
    tasksToRemove << 'ogl'
    disableSubmoduleUpdate << 'ogl'
}

if (env.buildVulkan != 'true') {
    tasksToRemove << 'Vulkan'
    disableSubmoduleUpdate << 'Vulkan'
}

def fetchCode() {
    def disable_submodule_update = disableSubmoduleUpdate.join(',')

    new git().fetchCode(env.repo, env.gitlabTargetBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true, updateBuildDescription: true])
    new git().updateSubmodule(env.repo, 1, '', disable_submodule_update)

    if (env.buildDirectstream == 'true') {
        new git().fetchCode('DirectStream', env.gitlabTargetBranch)
    }
    ciConfig = loadCiConfig()
}

def loadCiConfig() {
    def ciConfigPath = 'wddm\\ci\\CIConfig.yaml'
    def ciConfig = readYaml file: ciConfigPath
    echo "ciConfig: ${ciConfig}"
    return ciConfig
}

runner.start(env.runChoice) {
    //init
    new common().initEnv(env.NODE_NAME, "192.168.${env.NODE_NAME.split('_')[-1]}")

    def workflow = [
        'checkout': [closure: { fetchCode() }],

        'build': [closure: { winBuild.integratedBuildProcess(ciConfig, tasksToRemove, env.driver_env, env.driver_type, env.chip_type) }],

        'pkgProducts' : [closure: { winBuild.pkgProducts(ciConfig, env.driver_type, env.build_type, tasksToRemove) }],

        'signature' : [closure: { winBuild.signature(ciConfig, env.driver_type, version) }],

        'upload' : [closure: { winBuild.upload(ciConfig, env.repo, env.gitlabTargetBranch, env.gitlabMergeRequestIid, env.driver_type) }],

        'extendDriver' : [closure: { winBuild.extendDriver(ciConfig, env.driver_type, version) }],
    ]

    runPipeline(workflow, [disablePost:true])
}
