@Library('swqa-ci')

import groovy.transform.Field
import org.swqa.tools.git
import org.swqa.tools.common

//author: liya
//email: <EMAIL>

@Field List collectedResults = []

def isVpsNode(nodeName) {
    return ['hg', 'ph1', 'sudi', 'quyuan'].any { nodeName.contains(it) }
}

def initVps(String jsonFile, String nodeType, String nodeName) {
    node('Status_jump') {
        def config = readJSON text: jsonFile

        def vpsinfoParts = config.vpsinfo[nodeType]?.split(',') ?: []
        if (vpsinfoParts.size() < 3) {
            error "Insufficient vpsinfo parts: ${vpsinfoParts}"
        }

        def vpsmodel = vpsinfoParts[0]
        def vpsversion = vpsinfoParts[1]
        def vpstag = vpsinfoParts[2]

        if (nodeType.contains('hg')) {
            env.hgvpstag = vpstag
        }else if (nodeType.contains('ph1')) {
            env.ph1vpstag = vpstag
        }

        startVpsNodes(nodeName, vpsversion, nodeType.split('_')[-2], vpsmodel, vpstag)
    }
}

def startVpsNodes(String nodeName, String vps_version, String chip_type, String model_type, String tag) {
    def parameters = [
        nodeName: nodeName,
        vps_version: vps_version,
        chip_type: chip_type,
        model_type: model_type,
        tag: tag
    ]

    runPipeline.runJob([
        job: 'win_start_vps_nodes',
        parameters: parameters
    ])
}

def shouldRunTest(mrStatuses, nodeType, String testName = null) {
    if (mrStatuses.isEmpty()) {
        echo 'No statuses found or unable to retrieve statuses from GitLab.'
        return true
    }

    def failedStatuses = mrStatuses.findAll { it.status != 'success' && it.name.contains(nodeType) }
    def successfulStatuses = mrStatuses.findAll { it.status == 'success' && it.name.contains(nodeType) }

    if (successfulStatuses.isEmpty()) {
        echo "No successful statuses found containing ${nodeType}. Running test."
        return true
    }

    if (failedStatuses.isEmpty()) {
        echo "No failed statuses found containing ${nodeType}. Skipping test."
        return false
    }

    if (testName && successfulStatuses.any { it.name.endsWith("${nodeType}_${testName}") }) {
        echo "Match found for ${nodeType}_${testName}. Skipping test."
        return false
}

    return true
}

def executeTestsInBatches(String jsonFile, int n) {
    def filesToCopy = ['mtdxum32.dll', 'mtdxconv32.dll', 'mtgfxc32.dll', 'mtdxum64.dll', 'mtdxconv64.dll', 'mtgfxc64.dll', 'mtdxc64.dll', 'mtdh64.dll', 'mtdh32.dll']
    def config = readJSON text: jsonFile

    def allTests = config.testinfo.collectMany { node, tests ->
        tests.collect { test -> [node: node, test: test] }
    }

    def testBatches = allTests.groupBy { it.node }.collectEntries { nodeType, nodeTests ->
        [(nodeType): nodeTests.collate(n)]
}

    def mrStatuses = []
    try {
        retry(3) {
            sleep time: 30, unit: 'SECONDS'
            println('Waiting for 30 seconds ...')
            mrStatuses = new git().getGitlabCommitAllStatuses(env.gitlabSourceRepoName, env.gitlabMergeRequestLastCommit, env.gitlabSourceBranch)
            println '[wddm-test] Retrieved statuses:'
            mrStatuses.each { status ->
                println "  name: ${status.name}, status: ${status.status}"
            }
        }
    } catch (e) {
        echo "Error retrieving GitLab commit statuses: ${e.message}"
    }

    def parallelStages = [:]

    testBatches.each { nodeType, batches ->
        batches.eachWithIndex { batch, index ->
            def stageName = "${nodeType} Batch-${index + 1}"
            if (!shouldRunTest(mrStatuses, nodeType)) {
                echo "Skipping tests for ${nodeType} as tests already ran successfully."
                return
            }

            parallelStages[stageName] = {
                def nodeName = null
                node("CI_GFX_Win10_${nodeType}") {
                    try {
                        nodeName = env.NODE_NAME
                        if (isVpsNode(nodeType)) {
                            initVps(jsonFile, nodeType, nodeName)
                        } else {
                            new common().initEnv(nodeName, "192.168.${nodeName.split('_')[-1]}")
                        }
                        sleep(time: 10, unit: 'SECONDS')
                        deleteDir()
                        winTest.update_driver(env.driver_url, env.inf_file)
                        if (isVpsNode(nodeType)) {
                            dir('driver') {
                                filesToCopy.each { file ->
                                    bat "copy /Y ${file} C:\\Test\\"
                                }
                            }
                        }

                        batch.each { testInfo ->
                            def testName = testInfo.test.name
                            def caselist = testInfo.test.caselist
                            def timelimit = testInfo.test.timelimit

                            echo "Running ${testName} on ${nodeType}"

                            if (shouldRunTest(mrStatuses, nodeType, testName)) {
                                if (!testName.contains('mttrace')) {
                                    env.mailReceiver = ''
                                }
                                stage(testName) {
                                    try {
                                        node('Status_jump') {
                                            new git().setGitlabStatus("win/${nodeType}_${testName}", 'canceled')
                                            new git().setGitlabStatus("win/${nodeType}_${testName}", 'pending')
                                        }
                                        timeout(timelimit) {
                                            if (caselist) {
                                                winTest."${testName}"(caselist)
                                            } else {
                                                winTest."${testName}"()
                                            }
                                        }
                                        node('Status_jump') {
                                            new git().setGitlabStatus("win/${nodeType}_${testName}", 'success')
                                        }
                                    } catch (e) {
                                        currentBuild.result = 'FAIL'
                                        print(e)
                                        node('Status_jump') {
                                            new git().setGitlabStatus("win/${nodeType}_${testName}", 'failed')
                                        }
                                        error "${testName} failed"
                                    }
                                }
                            } else {
                                echo "Skipping ${testName} on ${nodeType}, already successful."
                            }
                        }
                    } catch (e) {
                        currentBuild.result = 'FAIL'
                        print(e)
                        error 'Test failed'
                    } finally {
                        if (isVpsNode(nodeType)) {
                            initVps(jsonFile, nodeType, nodeName)
                        } else {
                            new common().initEnv(nodeName, "192.168.${nodeName.split('_')[-1]}")
                        }
                    }
                }
            }
        }
    }

    parallel parallelStages
}

runner.start(env.runChoice) {
    int numTestBatches = (env.numTestBatches ?: '5').toInteger()
    def workflow = [
        'executeTestsInBatches': [closure: { executeTestsInBatches(env.ciConfig, numTestBatches) }],
    ]

    runPipeline(workflow, [disablePost:true])
}
