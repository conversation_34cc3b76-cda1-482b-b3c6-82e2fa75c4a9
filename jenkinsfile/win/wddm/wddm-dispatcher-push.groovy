@Library('swqa-ci')

import org.swqa.tools.git

//author: liya
//email: <EMAIL>

buildBranch = env.ref.split('refs/heads/')[-1]

def loadCiConfig() {
    def ciConfigPath = 'wddm\\ci\\CIConfig.yaml'
    def ciConfig = readYaml file: ciConfigPath
    echo "ciConfig: ${ciConfig}"
    return ciConfig
}

def executeBuild(Map ciConfig) {
    def buildTasks = [:]
    def targetKeys = env.targetKey?.tokenize(',')?.findAll { it.trim() } ?: []

    ciConfig.builds.each { key, value ->
        echo "Processing build configuration: ${key}"
        if (!targetKeys.isEmpty() && !targetKeys.contains(key)) {
            return
        }

        def buildFlags = [
            buildM3d: !key.contains('vps') && !key.contains('nohw') && !key.contains('emu'),
            buildDirectstream: !key.contains('vps') && !key.contains('nohw') && !key.contains('emu'),
            buildOgl: !key.contains('emu'),
            buildVulkan: !key.contains('hw_30') && !key.contains('vps') && !key.contains('nohw') && !key.contains('emu') && !key.contains('release'),
            buildDxc: true,
            buildPanel: !key.contains('nohw') && !key.contains('emu')
        ].collectEntries { k, v -> [k, v.toString()] } // Convert Boolean to String

        def driver_env = key.split('_')[0]
        def build_type = 'release'
        def chip_type = 'sudi'

        if (buildBranch.contains('release')) {
            build_type = 'release_branch'
        }

        def validKeys = ['hw_30', 'emu_ph1']
        def nodeLabel = (key in validKeys) ? env.extend_build_nodes : env.build_nodes

        buildTasks[key] = {
            def jobParams = [
                job: 'M-wddm-build',
                parameters: [
                    chip_type: chip_type,
                    driver_env: driver_env,
                    driver_type: key,
                    build_type: build_type,
                    buildCommitId: env.master_commit_id,
                    buildBranch: buildBranch,
                    nodeLabel: nodeLabel,
                ] + buildFlags
            ]

            runPipeline.runJob(jobParams)

            def baseUrl = "https://oss.mthreads.com/sw-build/wddm/${buildBranch}/${env.master_commit_id[0..8]}"
            def vdi_guest_driver_url = "${baseUrl}/${env.master_commit_id[0..8]}_wddm_vdi.tar.gz"
            def m3d_test_url = "${baseUrl}/${env.master_commit_id[0..8]}_m3dTest.tar.gz"
            def directstream_test_url = "${baseUrl}/${env.master_commit_id[0..8]}_directstreamTest.tar.gz"

            if (key == 'hw') {
                executeVdiBuild(vdi_guest_driver_url, m3d_test_url, directstream_test_url)
            }
        }
    }

    parallel buildTasks
}

def executeVdiBuild(String guest_driver_url, String m3d_test_url, String directstream_test_url) {
    runPipeline.runJob([
        job: 'M-vdi-build',
        wait:false,
        parameters: [
            guest_driver_url: guest_driver_url,
            m3d_test_url: m3d_test_url,
            directstream_test_url: directstream_test_url
        ]
    ])
}

def dispatcher() {
    def ossBranchPath = constants.genOssPath(env.repo, buildBranch, env.master_commit_id)

    new git().fetchCode(env.repo, buildBranch, env.master_commit_id, [preBuildMerge: false, disableSubmodules: true, updateBuildDescription: false])

    ciConfig = loadCiConfig()

    executeBuild(ciConfig)

    winBuild.updateLatestTxt(ossBranchPath, 'latest_new.txt')

    winBuild.getCommitHistory(env.repo, env.master_commit_id, ossBranchPath)
}

runner.start(env.runChoice) {
    def workflow = [
        'dispatcher': [closure: { dispatcher() }],
    ]

    runPipeline(workflow, [disablePost:true])
}
