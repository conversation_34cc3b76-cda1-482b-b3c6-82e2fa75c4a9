@Library('swqa-ci')

import groovy.json.JsonOutput
import org.swqa.tools.git
import org.swqa.tools.jenkins

//author: liya
//email: <EMAIL>

ogl_test_condition = false

mrStatuses = []

wddmOglTestBlacklistDirs = [
    'dxc', 'mtdxum', 'vulkan', 'wddm/directx', 'wddm/mtvideodrv'
]

def checkPreviousBuildsOfPR() {
    def jenkinsInstance = new jenkins()
    return jenkinsInstance.checkPrPreviousBuilds()
}

def initGitlabStatus(Map ciConfig) {
    try {
        retry(3) {
            sleep time: 30, unit: 'SECONDS'
            println('Waiting for 30 seconds ...')
            mrStatuses = new git().getGitlabCommitAllStatuses(env.gitlabSourceRepoName, env.gitlabMergeRequestLastCommit, env.gitlabSourceBranch)
        }
    } catch (e) {
        echo "Error retrieving GitLab commit statuses: ${e.message}"
    }

    env.debug = 'false'

    ogl_test_condition = checkout_wddm_change_dir(env.gitlabTargetBranch, wddmOglTestBlacklistDirs)
    echo "OGL Test Condition: ${ogl_test_condition}"

    def result = []

    ciConfig.tests.each { testGroup, testDetails ->
        if (testDetails != null) {
            testDetails.each { testName, testConfigs ->
                testConfigs.each { testConfig ->
                    def formattedTest = "${testName}_${testConfig.name}"
                    def isValidActionOrStatus = env.gitlabActionType != 'NOTE' || !(env.gitlabTriggerPhrase =~ /(?i)cirerun/) || checkCiStatus(mrStatuses, formattedTest)
                    def isOglTestConditionMet = ogl_test_condition || !testName.contains('ogl_')

                    if (isValidActionOrStatus && isOglTestConditionMet) {
                        result.add(formattedTest)
                    }
                }
            }
        }
    }

    if (!env.gitlabTargetBranch.contains('release')) {
        List testItems = []
        if (env.initGitlabStatus?.trim()) {
            if (!(env.gitlabActionType == 'NOTE' && env.gitlabTriggerPhrase =~ /(?i)cirerun/)) {
                testItems = env.initGitlabStatus.split(',')
            }
            result = result + testItems
        }
    }

    node('Status_jump') {
        new git().setGitlabStatus(result.collect { "win/${it }" }, 'canceled')
        new git().setGitlabStatus(result.collect { "win/${it }" })
    }
}

def checkCiStatus(mrStatuses, String statusName) {
    if (mrStatuses.isEmpty()) {
        echo 'No statuses found or unable to retrieve statuses from GitLab.'
        return true
    }

    if (!env.gitlabTriggerPhrase =~ /(?i)cirerun/) {
        return true
    }

    def successfulStatuses = mrStatuses.findAll { it.status == 'success' }

    if (statusName && successfulStatuses.any { it.name.contains("${statusName}") }) {
        echo "Match found for ${statusName}. Skipping test."
        return false
}

    return true
}

def checkout_wddm_change_dir(String branch='develop', test_limit_dir) {
    dir('wddm') {
        echo "${test_limit_dir}"
        def change_dir_list = sh(script: "git diff origin/${branch} --name-only --merge-base", returnStdout: true).trim()
        echo "${change_dir_list}"
        for (limit_dir in test_limit_dir) {
            /* groovylint-disable-next-line ConsecutiveStringConcatenation */
            regs = '(?m)^' + "${limit_dir}.*"
            if (change_dir_list =~ regs) {
                return true
            }
        }
    }

    return false
}

def loadCiConfig() {
    def ciConfigPath = 'wddm\\ci\\CIConfig.yaml'
    def ciConfig = readYaml file: ciConfigPath
    echo "ciConfig: ${ciConfig}"
    return ciConfig
}

def executeBuild(Map ciConfig) {
    def buildTasks = [:]
    def targetKeys = env.targetKey?.tokenize(',')?.findAll { it.trim() } ?: []

    ciConfig.builds.each { key, value ->
        echo "Processing build configuration: ${key}"
        def inf_file = value.infFiles[0][0]
        echo "inf file: ${inf_file}"

        if (env.gitlabTargetBranch.contains('release') && !env.gitlabTargetBranch.contains('release_hg_pre_isa')) {
            if (!key.contains('release')) {
                return
            }
        }else {
            if (!targetKeys.isEmpty() && !targetKeys.contains(key)) {
                return
            }
        }

        def buildFlags = [
            buildM3d: !key.contains('hw_30') && !key.contains('vps') && !key.contains('nohw') && !key.contains('emu'),
            buildDirectstream: !key.contains('hw_30') && !key.contains('vps') && !key.contains('nohw') && !key.contains('emu'),
            buildOgl: !key.contains('emu'),
            buildVulkan: !key.contains('hw_30') && !key.contains('vps') && !key.contains('nohw') && !key.contains('emu') && !key.contains('release'),
            buildDxc: true,
            buildPanel: !key.contains('nohw') && !key.contains('emu')
        ].collectEntries { k, v -> [k, v.toString()] } // Convert Boolean to String

        def driver_env = key.split('_')[0]
        def build_type = 'release'
        def chip_type = 'sudi'

        def buildMapping = [
            'hg': ['hg_release', 'hg'],
            'ph1': ['ph1_release', 'ph1']
        ]

        buildMapping.each { keyPart, values ->
            if (key.contains(keyPart)) {
                build_type = values[0]
                chip_type = values[1]
            }
        }

        buildTasks[key] = {
            if (!(env.gitlabActionType == 'NOTE' && env.gitlabTriggerPhrase =~ /(?i)cirerun/ && !checkCiStatus(mrStatuses, "build_${key}"))) {
                node('Status_jump') { new git().setGitlabStatus("win/build_${key }", 'pending') }
                runPipeline.runJob([
                    job: 'win-wddm-build',
                    parameters: [
                        triggerInfo: env.triggerInfo,
                        chip_type: chip_type,
                        driver_env: driver_env,
                        driver_type: key,
                        build_type: build_type,
                    ] + buildFlags
                ])
            }

            def ossBranchPath = constants.genOssPath(env.gitlabSourceRepoName, env.gitlabTargetBranch, env.gitlabMergeRequestIid)
            def prefix = constants.genOssPrefix(ossBranchPath)
            def baseUrl = "${prefix}/${ossBranchPath}"
            def driver_url = "${baseUrl}/${env.gitlabMergeRequestIid}_wddm_${key}.tar.gz"
            def driver_url_mttrace = "${baseUrl}/${env.gitlabMergeRequestIid}_wddm_${key}_mttrace.tar.gz"
            def driver_url_fec = "${baseUrl}/${env.gitlabMergeRequestIid}_wddm_${key}_fec.tar.gz"
            def driver_url_mtcc20 = "${baseUrl}/${env.gitlabMergeRequestIid}_wddm_${key}_mtcc20.tar.gz"
            def guest_driver_url = "${baseUrl}/${env.gitlabMergeRequestIid}_wddm_vdi.tar.gz"
            def m3d_test_url = "${baseUrl}/${env.gitlabMergeRequestIid}_m3dTest.tar.gz"
            def directstream_test_url = "${baseUrl}/${env.gitlabMergeRequestIid}_directstreamTest.tar.gz"

            switch (key) {
                case 'hw':
                    if (!(env.gitlabActionType == 'NOTE' && env.gitlabTriggerPhrase =~ /(?i)cirerun/ && !checkCiStatus(mrStatuses, 'vdi_test'))) {
                        executeVdiBuild(guest_driver_url, m3d_test_url, directstream_test_url)
                    }
                    break

                default:
                    break
            }

            echo "ciConfig.tests : ${ciConfig.tests}"
            echo "ciConfig.tests.key for ${key}: ${ciConfig.tests[key]}"

            try {
                def testConfig = ciConfig.tests[key]
                def vpsConfig = ciConfig['vps']

                if (testConfig) {
                    def scenarios = [
                        [cfg: testConfig,          url: driver_url,       inf: inf_file]
                    ]
                    if (key.contains('vps')) {
                        scenarios << [cfg: ciConfig.tests['vps_mttrace'], url: driver_url_mttrace, inf: inf_file]
                        scenarios << [cfg: ciConfig.tests['vps_fec'],     url: driver_url_fec,     inf: 'MT-VPS-HG-Amodel-FEC']
                    }else if (key.contains('hw') && !key.contains('nohw')) {
                        scenarios << [cfg: ciConfig.tests['hw_mtcc20'], url: driver_url_mtcc20, inf: inf_file]
                    }

                    def scenarioTasks = [:]
                    scenarios.eachWithIndex { sc, idx ->
                        def taskName = "${key}_scenario${idx + 1}"
                        scenarioTasks[taskName] = {
                            def ti = [testinfo: sc.cfg, vpsinfo: vpsConfig]
                            executeTest(
                                ti,
                                sc.url,
                                sc.inf,
                                m3d_test_url,
                                directstream_test_url,
                                ciConfig.d3dtest,
                                ciConfig.whlktest,
                                key
                            )
                        }
                    }
                    parallel scenarioTasks
                }
            } catch (e) {
                currentBuild.result = 'FAIL'
                error "Failed to executeTest: ${e.message}"
            }
        }
    }

    parallel buildTasks
}

def executeTest(Map testConfig, String driver_url, String inf_file, String m3d_test_url, String directstream_test_url, String d3dTestCommitId, String whlkTestCommitId, String driverType) {
    def testConfigJson = JsonOutput.toJson(testConfig)
    def jobName = "win-wddm-test-${driverType}"
    def waitCondition = true
    runPipeline.runJob([
        job: jobName,
        wait: waitCondition,
        parameters: [
            triggerInfo: env.triggerInfo,
            ciConfig: testConfigJson,
            driver_url: driver_url,
            inf_file: inf_file,
            m3d_test_url: m3d_test_url,
            directstream_test_url: directstream_test_url,
            d3dTestCommitId: d3dTestCommitId,
            whlkTestCommitId: whlkTestCommitId
        ]
    ])
}

def executeVdiBuild(String guest_driver_url, String m3d_test_url, String directstream_test_url) {
    runPipeline.runJob([
        job: 'win-vdi-build',
        wait:false,
        parameters: [
            triggerInfo: env.triggerInfo,
            guest_driver_url: guest_driver_url,
            m3d_test_url: m3d_test_url,
            directstream_test_url: directstream_test_url
        ]
    ])
}

def dispatcher() {
    new git().fetchCode(env.repo, env.gitlabTargetBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true, updateBuildDescription: true])

    ciConfig = loadCiConfig()

    initGitlabStatus(ciConfig)

    executeBuild(ciConfig)
}

runner.start(env.runChoice) {
    def workflow = [
        'checkPreviousBuildsOfPR': [closure: { checkPreviousBuildsOfPR() }],

        'dispatcher': [closure: { dispatcher() }],
    ]

    runPipeline(workflow, [disablePost:true])
}
