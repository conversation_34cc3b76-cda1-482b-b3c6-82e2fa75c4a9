@Library('swqa-ci')

import groovy.transform.Field
import org.swqa.tools.git
import org.swqa.tools.common

//author: liya
//email: <EMAIL>

def time_tag_name = new Date().format('MM/dd/yyyy')
def version = "${time_tag_name}"

@Field List<String> tasksToRemove = []
@Field List<String> disableSubmoduleUpdate = ['import/mtcc']

if (env.buildM3d != 'true') {
    tasksToRemove << 'm3d'
}

if (env.buildDirectstream != 'true') {
    tasksToRemove << 'directstream'
}

if (env.buildDxc != 'true') {
    tasksToRemove << 'dxc'
    disableSubmoduleUpdate << 'dxc'
}

if (env.buildOGL != 'true') {
    tasksToRemove << 'ogl'
    disableSubmoduleUpdate << 'ogl'
}

if (env.buildVulkan != 'true') {
    tasksToRemove << 'Vulkan'
    disableSubmoduleUpdate << 'Vulkan'
}

def fetchCode() {
    def disable_submodule_update = disableSubmoduleUpdate.join(',')

    new git().fetchCode(env.repo, env.buildBranch, env.buildCommitId, [preBuildMerge: false, disableSubmodules: true, updateBuildDescription: true])
    new git().updateSubmodule(env.repo, 1, '', disable_submodule_update)

    if (env.buildDirectstream == 'true') {
        new git().fetchCode('DirectStream', env.buildBranch)
    }
    ciConfig = loadCiConfig()
}

def loadCiConfig() {
    def ciConfigPath = 'wddm\\ci\\CIConfig.yaml'
    def ciConfig = readYaml file: ciConfigPath
    echo "ciConfig: ${ciConfig}"
    return ciConfig
}

def extendDriver(Map ciConfig, String driverType = 'hw', String version, String ossBranchPath) {
    def builds = ciConfig.builds
    def driverMapping = builds[driverType]
    int index = 1

    driverMapping.pkgNames[0].drop(1).each { type ->
        try {
            def driverPath = "${type}_driver\\bin\\fre_win10_amd64"

            if (type.contains('vdi')) {
                winBuild.fetchMtapi(driverPath)
            }

            if (type.contains('mttrace')) {
                dir('wddm\\mtdxum') {
                    bat '''
                        python cmake_mtdxum.py -a 64 -b 1 -t 0 -e 1 -d 1
                        python cmake_mtdxum.py -a 32 -b 1 -t 0 -e 1 -d 1
                    '''
                }

                dir('wddm\\dxc') {
                    bat '''
                        python cmake_dxc.py -a 64 -b 1 -t 0 -d 1
                        python cmake_dxc.py -a 32 -b 1 -t 0 -d 1
                    '''
                }

                def files = [
                    [name: 'mtdxum64', basePath: 'wddm\\mtdxum', build64: 'cmake_build', build32: 'cmake_build32'],
                    [name: 'mtdxum32', basePath: 'wddm\\mtdxum', build64: 'cmake_build', build32: 'cmake_build32'],
                    [name: 'mtdxc64', basePath: 'wddm\\dxc', build64: 'build', build32: 'build32'],
                    [name: 'mtdh64', basePath: 'wddm\\dxc', build64: 'build', build32: 'build32'],
                    [name: 'mtdxc32', basePath: 'wddm\\dxc', build64: 'build', build32: 'build32'],
                    [name: 'mtdh32', basePath: 'wddm\\dxc', build64: 'build', build32: 'build32']
                ]

                files.each { file ->
                    def buildPath = file.name.endsWith('64') ? file.build64 : file.build32
                    def sourcePath = "${file.basePath}\\${buildPath}\\Release\\${file.name}"

                    bat """
                        rm -rf ${driverPath}\\symbols\\${file.name}.pdb
                        rm -rf ${driverPath}\\${file.name}.dll
                        cp -rf ${sourcePath}.pdb ${driverPath}\\symbols\\
                        cp -rf ${sourcePath}.dll ${driverPath}\\
                    """
                }
            }

            if (type.contains('mtcc20')) {
                winBuild.build_ogl('mtcc20', 'x64')
                winBuild.build_ogl('mtcc20', 'x86')

                def files = [
                    [name: 'mticdg64', arch: 'x64'], [name: 'mticdfbg64', arch: 'x64'],
                    [name: 'mticdpxg64', arch: 'x64'], [name: 'mticdg32', arch: 'x86'],
                    [name: 'mticdfbg32', arch: 'x86'], [name: 'mticdpxg32', arch: 'x86']
                ]

                files.each { file ->
                    def buildPath = "build_${file.arch}"
                    def sourcePath = "wddm\\ogl\\${buildPath}\\Release\\${file.name}"

                    if (file.name.contains('mticdfbg') || file.name.contains('mticdpxg')) {
                        sourcePath = "wddm\\ogl\\imported\\pre-binary-kmd\\hw\\${file.name}"
                    }

                    bat """
                        rm -rf ${driverPath}\\symbols\\${file.name}.pdb
                        rm -rf ${driverPath}\\${file.name}.dll
                        cp -rf ${sourcePath}.pdb ${driverPath}\\symbols\\
                        cp -rf ${sourcePath}.dll ${driverPath}\\
                    """
                }
            }

            winBuild.signature(ciConfig, driverType, version, driverPath, index)

            winBuild.upload(ciConfig, env.repo, env.buildBranch, env.buildCommitId, driverType, driverPath, index)
        } catch (e) {
            echo "Error processing ${type} with index ${index}: ${e.message}"
        } finally {
            index++
        }
    }
    //upload m3d test
    if (env.buildM3d == 'true') {
        winBuild.upload_m3d(env.repo, env.buildBranch, env.buildCommitId)
    }
    //upload directstream test
    if (env.buildDirectstream == 'true') {
        winBuild.upload_directstream(env.repo, env.buildBranch, env.buildCommitId)
    }
}

def sign() {
    def time_hour = new Date().format('HH')
    if (env.driver_env == 'hw' && time_hour.toInteger() > 8) {
        def baseUrl = "https://oss.mthreads.com/sw-build/wddm/${env.buildBranch}/${env.buildCommitId[0..8]}"
        def driver_url = "${baseUrl}/${env.buildCommitId[0..8]}_wddm_hw.tar.gz"
        runPipeline.runJob([
            job: 'win-sign',
            wait:false,
            parameters: [
                driver_url: driver_url,
                buildPes: 'pes',
                signature_waiting_status: 'no'
            ]
        ])
    }
}

runner.start(env.runChoice) {
    //init
    new common().initEnv(env.NODE_NAME, "192.168.${env.NODE_NAME.split('_')[-1]}")

    def ossBranchPath = constants.genOssPath(env.repo, env.buildBranch, env.buildCommitId)

    def workflow = [
        'checkout': [closure: { fetchCode() }],

        'build': [closure: { winBuild.integratedBuildProcess(ciConfig, tasksToRemove, env.driver_env, env.driver_type, env.chip_type, env.build_type) }],

        'pkgProducts' : [closure: { winBuild.pkgProducts(ciConfig, env.driver_type, env.build_type, tasksToRemove) }],

        'symuploader' : [closure: { winBuild.symuploader(ciConfig, env.driver_type, env.build_type) }],

        'signature' : [closure: { winBuild.signature(ciConfig, env.driver_type, version) }],

        'upload' : [closure: { winBuild.upload(ciConfig, env.repo, env.buildBranch, env.buildCommitId, env.driver_type) }],

        'extendDriver' : [closure: { extendDriver(ciConfig, env.driver_type, version, ossBranchPath) }],

        'sign' : [closure: { sign() }],
    ]

    runPipeline(workflow, [disablePost:true])
}
