@Library('swqa-ci')

import org.swqa.tools.common

def getDriverConfig(String commitId) {
    [
        hw: [
            package_name: "${commitId}_wddm_hw.tar.gz",
            signed_name: "signed_${commitId}_wddm_hw.tar.gz"
        ],
        vdi: [
            package_name: "${commitId}_wddm_vdi.tar.gz",
            signed_name: "signed_${commitId}_wddm_vdi.tar.gz"
        ],
        vps: [
            package_name: "${commitId}_wddm_vps.tar.gz"
        ]
    ]
}

def initConfig() {
    def daily_tag = new Date().format('yyyyMMdd')
    Map tags_info = new common().fetchCommitFromTag(daily_tag)
    def commitId = tags_info.get(env.repo)?.get(env.branch)?.substring(0, 9)
    def branchPath = constants.genOssPath(env.repo, env.branch, commitId)

    return [
        daily_tag: daily_tag,
        paths: [
            branch: branchPath,
            prefix: constants.genOssPrefix(branchPath)
        ],
        *: getDriverConfig(commitId)
    ]
}

def sign(Map params) {
    print "[Sign] Starting driver signing for: ${params.url}"
    runPipeline.runJob([
        job: 'win-sign',
        wait: true,
        parameters: [
            driver_url: params.url,
            buildPes: 'pes',
            signature_waiting_status: 'yes',
            oss_save_path: params.save_path
        ]
    ])
}

def moveToDaily(Map params) {
    def ossAlias = constants.genOssAlias(params.source)

    print "[Daily] Moving ${params.package} to ${params.save_path}"

    oss.install()
    sh """
        mc cp ${ossAlias}/${params.source}/${params.package} . || exit 1
        mc cp ${params.package} ${ossAlias}/${params.save_path}/ || exit 1
    """
}

runner.start(env.runChoice) {
    def config = initConfig()

    def pathParts = config.paths.branch.split('/')

    def dailyPathParts = new String[pathParts.length]
    def vpsPathParts = new String[pathParts.length]

    for (int i = 0; i < pathParts.length; i++) {
        dailyPathParts[i] = pathParts[i].toString()
        vpsPathParts[i] = pathParts[i].toString()
    }

    dailyPathParts[-1] = config.daily_tag.toString()
    vpsPathParts[-1] = "hg_daily/${config.daily_tag}".toString()

    def savePath = dailyPathParts.join('/')
    def vpsSavePath = vpsPathParts.join('/')

    def workflow = [:]

    workflow['move vps'] = [
        closure: {
            moveToDaily(
                source: config.paths.branch,
                package: config.vps.package_name,
                tag: config.daily_tag,
                save_path: vpsSavePath
            )
        }
    ]

    workflow += ['vdi', 'hw'].collectEntries { type ->
        [
            "sign ${type}": [
                closure: {
                    sign(
                        url: "${config.paths.prefix}/${config.paths.branch}/${config[type].package_name}",
                        save_path: savePath
                    )
                }
            ]
        ]
    }

    runPipeline(workflow, [disablePost:true])
}
