@Library('swqa-ci')

import groovy.transform.Field
import org.swqa.tools.git
import org.swqa.tools.common

//author: liya
//email: <EMAIL>

def daily_tag_name = new Date().format('yyyyMMdd')
def time_tag_name = new Date().format('MM/dd/yyyy')
def version = "${time_tag_name}"

@Field List<String> tasksToRemove = []
@Field List<String> disableSubmoduleUpdate = ['import/mtcc']

if (env.buildM3d != 'true') {
    tasksToRemove << 'm3d'
}

if (env.buildDirectstream != 'true') {
    tasksToRemove << 'directstream'
}

if (env.buildDxc != 'true') {
    tasksToRemove << 'dxc'
    disableSubmoduleUpdate << 'dxc'
}

if (env.buildOGL != 'true') {
    tasksToRemove << 'ogl'
    disableSubmoduleUpdate << 'ogl'
}

if (env.buildVulkan != 'true') {
    tasksToRemove << 'Vulkan'
    disableSubmoduleUpdate << 'Vulkan'
}

def fetchCode(String daily_tag_name) {
    env.buildCommitId = env.buildCommitId ?: new common().fetchCommitFromTag(daily_tag_name)[env.repo][env.buildBranch]

    def disable_submodule_update = disableSubmoduleUpdate.join(',')

    new git().fetchCode(env.repo, env.buildBranch, env.buildCommitId, [preBuildMerge: false, disableSubmodules: true, updateBuildDescription: true])
    new git().updateSubmodule(env.repo, 1, '', disable_submodule_update)

    if (env.buildDirectstream == 'true') {
        new git().fetchCode('DirectStream', env.buildBranch)
    }

    ciConfig = loadCiConfig()
}

def loadCiConfig() {
    def ciConfigPath = 'wddm\\ci\\CIConfig.yaml'
    def ciConfig = readYaml file: ciConfigPath
    echo "ciConfig: ${ciConfig}"
    return ciConfig
}

def extendDriver(Map ciConfig, String driverType = 'hw', String version, String daily_tag_name) {
    def builds = ciConfig.builds
    def driverMapping = builds[driverType]
    int index = 1

    driverMapping.pkgNames[0].drop(1).each { type ->
        try {
            def driverPath = "${type}_driver\\bin\\fre_win10_amd64"

            if (type.contains('mtcc20')) {
                winBuild.build_ogl('mtcc20', 'x64', '-DRELEASE_BRANCH=ON -DENABLE_COMPILER_DUMP=OFF')
                winBuild.build_ogl('mtcc20', 'x86', '-DRELEASE_BRANCH=ON -DENABLE_COMPILER_DUMP=OFF')

                def files = [
                    [name: 'mticdg64', arch: 'x64'], [name: 'mticdfbg64', arch: 'x64'],
                    [name: 'mticdpxg64', arch: 'x64'], [name: 'mticdg32', arch: 'x86'],
                    [name: 'mticdfbg32', arch: 'x86'], [name: 'mticdpxg32', arch: 'x86']
                ]

                files.each { file ->
                    def buildPath = "build_${file.arch}"
                    def sourcePath = "wddm\\ogl\\${buildPath}\\Release\\${file.name}"

                    if (file.name.contains('mticdfbg') || file.name.contains('mticdpxg')) {
                        sourcePath = "wddm\\ogl\\imported\\pre-binary-kmd\\hw\\${file.name}"
                    }

                    bat """
                        rm -rf ${driverPath}\\symbols\\${file.name}.pdb
                        rm -rf ${driverPath}\\${file.name}.dll
                        cp -rf ${sourcePath}.pdb ${driverPath}\\symbols\\
                        cp -rf ${sourcePath}.dll ${driverPath}\\
                    """
                }
            }

            winBuild.signature(ciConfig, driverType, version, driverPath, index)

            winBuild.upload(ciConfig, 'Daily', env.buildBranch, daily_tag_name, driverType, driverPath, index)
        } catch (e) {
            echo "Error processing ${type} with index ${index}: ${e.message}"
        } finally {
            index++
        }
    }
}

runner.start(env.runChoice) {
    //init
    // new common().initEnv(env.NODE_NAME, "192.168.${env.NODE_NAME.split('_')[-1]}")

    def workflow = [
        'checkout': [closure: { fetchCode(daily_tag_name) }],

        'build': [closure: { winBuild.integratedBuildProcess(ciConfig, tasksToRemove, env.driver_env, env.driver_type, env.chip_type, env.build_type) }],

        'pkgProducts' : [closure: { winBuild.pkgProducts(ciConfig, env.driver_type, env.build_type, tasksToRemove) }],

        'symuploader' : [closure: { winBuild.symuploader(ciConfig, env.driver_type, env.build_type) }],

        'signature' : [closure: { winBuild.signature(ciConfig, env.driver_type, version) }],

        'upload' : [closure: { winBuild.upload(ciConfig, 'Daily', env.buildBranch, env.buildCommitId, env.driver_type) }],

        'extendDriver' : [closure: { extendDriver(ciConfig, env.driver_type, version, env.buildCommitId) }],
    ]

    runPipeline(workflow, [disablePost:true])
}
