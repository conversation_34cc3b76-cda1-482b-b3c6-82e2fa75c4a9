@Library('swqa-ci')

import org.swqa.tools.git

def projectName = 'mt-pes'
def latest_url_wddm = "https://oss.mthreads.com/sw-build/wddm/${env.wddm_branch}/latest_new.txt"
def latest_url_audio = "https://oss.mthreads.com/release-ci/audio/Windows/${env.wddm_branch}/latest.txt"

def generateFileName(wddm_commitId, kmd_commitId, pes_commitId, audio_commitId, install_ver) {
    return ([wddm_commitId, kmd_commitId, pes_commitId, audio_commitId, install_ver]
        .findAll { it })
        .join('_') + '-musa-desktop-win10-64bit'
}

node(env.Test_Nodes) {
    deleteDir()
    currentBuild.description = "Test Node: ${env.NODE_NAME} <br>"
    def wddm_pkg_url = sh(script: "curl --insecure ${latest_url_wddm}", returnStdout: true).trim() + '_hw_mtcc.tar.gz'
    def audio_pkg_url = sh(script: "curl --insecure ${latest_url_audio}", returnStdout: true).trim() + '_mt-audio-windows-driver.tar.gz'
    if (env.wddm_driver_url) {
        wddm_pkg_url = env.wddm_driver_url
    }
    if (env.audio_driver_url) {
        audio_pkg_url = env.audio_driver_url
    }
    stage('get commitid') {
        print(wddm_pkg_url)
        print(audio_pkg_url)

        def matcher = (wddm_pkg_url =~ /([a-f0-9]{9})/)

        wddm_commitId = matcher.find() ? matcher.group(1) : null
        kmd_commitId = matcher.find() ? matcher.group(1) : null

        if (wddm_commitId == kmd_commitId) {
            kmd_commitId = null
        }

        audio_commitId = audio_pkg_url.find(/([a-f0-9]{9})_/)
        if (audio_commitId) {
            audio_commitId = audio_commitId.replaceAll(/_/, '')
        }

        currentBuild.description += "wddm_commitId: ${wddm_commitId}<br>"
        currentBuild.description += "kmd_commitId: ${kmd_commitId}<br>"
        currentBuild.description += "audio_commitId: ${audio_commitId}<br>"
    }

    stage('fetch Code') {
        repoCommitId = new git().fetchCode(projectName, env.pes_branch, null, [preBuildMerge: false, updateBuildDescription: true])
        def pes_commitId = repoCommitId[0..8]
        currentBuild.description += "pes_commitId: ${pes_commitId}<br>"

        file_name = generateFileName(wddm_commitId, kmd_commitId, pes_commitId, audio_commitId, install_ver) + '_Signed.exe'
        pdb_file_name = generateFileName(wddm_commitId, kmd_commitId, pes_commitId, audio_commitId, install_ver) + '_pdb.tar.gz'
        oem_file_name = generateFileName(wddm_commitId, kmd_commitId, pes_commitId, audio_commitId, install_ver) + '_oem_Signed.exe'
        vdi_file_name = generateFileName(wddm_commitId, kmd_commitId, pes_commitId, audio_commitId, install_ver) + '_vdi_Signed.exe'
    }

    dir("${projectName}\\Coding\\script") {
        try {
            stage('build') {
                bat """
                    python ci_script.py -b all -a x86_64 -o windows -sign -pv ${env.pes_version} -iv ${env.install_ver} -dv ${env.driver_ver}
                """
            }
        }catch (e) {
            echo 'pes build fail!'
            currentBuild.result = 'FAIL'
            throw(e)
        }

        stage('Sign exe and dll') {
            def signature_lists = bat(script: "cat ${env.WORKSPACE}/mt-pes/release/bin/signature_lists.txt", returnStdout: true).trim().split('\r\n')
            bat 'md signature_dir'
            for (int i = 1; i < signature_lists.length; i++) {
                def signature = signature_lists[i]
                bat """
                    move ${env.WORKSPACE}\\mt-pes\\release\\bin\\${signature} ${env.WORKSPACE}\\${projectName}\\Coding\\script\\signature_dir\\
                """
            }
            stash name:'signature_dir', includes:'signature_dir/'

            node(env.iTrusSign_Nodes) {
                deleteDir()
                stage('unstash signature_dir') {
                    unstash 'signature_dir'
                }

                timeout(2) {
                    stage('iTrusSign') {
                        bat"""
                            signtool sign /fd sha256 /sha1 ${constants.signtoolSha1} /t http://timestamp.globalsign.com/?signature=sha2 signature_dir/*
                        """
                        sleep 15
                    }
                }

                stage('stash dir_signed') {
                    stash name:'dir_signed', includes:'signature_dir/'
                }
            }

            dir('signed') {
                stage('unstash dir_signed') {
                    unstash 'dir_signed'
                }
                bat """
                    move signature_dir\\* ${env.WORKSPACE}\\mt-pes\\release\\bin\\
                """
            }
        }

        stage('fetch wddm driver pkg') {
            def wddm_pkg_name = 'wddm_driver.tar.gz'
            def wddm_file_url = "${env.WORKSPACE}/${projectName}/release/installer${env.package_platform_type == 'pes-oem' ? '_oem' : ''}${env.package_platform_type == 'pes-vdi' ? '_vdi' : ''}_build/drivers/wddm/"
            dir(wddm_file_url) {
                bat 'rm -rf *'
                if (wddm_pkg_url.contains('zip')) {
                    bat"""
                        md bin
                        wget -q ${wddm_pkg_url}
                        unzip *.zip
                        move drivers\\mtgpu bin\\fre_win10_amd64 || exit 1
                        tar -cvzf ${wddm_pkg_name} -C bin\\  *
                        rm -rf bin && rm -rf *.zip && rm -rf drivers
                    """
                }else {
                    bat"wget -q ${wddm_pkg_url} -O ${wddm_pkg_name}"
                }
            }
        }

        if (env.package_platform_type != 'pes-vdi') {
            stage('fetch audio driver pkg') {
                def audio_pkg_name = 'audio_driver.tar.gz'
                def audio_file_url = "${env.WORKSPACE}/${projectName}/release/installer${env.package_platform_type == 'pes-oem' ? '_oem' : ''}_build/drivers/audio/"
                dir(audio_file_url) {
                    bat 'rm -rf *'
                    if (audio_pkg_url.contains('zip')) {
                        bat"""
                            md bin
                            wget -q ${audio_pkg_url}
                            unzip *.zip
                            move drivers\\mtaudio mtaudiokm || exit 1
                            tar -cvzf ${audio_pkg_name} -C bin\\  *
                            rm -rf bin && rm -rf *.zip && rm -rf drivers
                        """
                    }else {
                        bat"wget -q ${audio_pkg_url} -O ${audio_pkg_name}"
                    }
                }
            }
        }

        if (env.package_platform_type == 'pes' && env.pes_branch != 'release_1.5') {
            stage('fetch install') {
                def base_url = 'https://oss.mthreads.com/backup/sdk_team/QtInstaller'
                def branch_modifier = pes_branch
                if (pes_branch.startsWith('release_') && pes_branch.indexOf('.') != -1 && pes_branch.lastIndexOf('.') != pes_branch.indexOf('.')) {
                    branch_modifier = pes_branch.substring(0, pes_branch.lastIndexOf('.'))
                }

                def latest_url_installer = "${base_url}/${branch_modifier}/latest.txt"
                def installer_url = sh(script: "curl --insecure ${latest_url_installer} -k", returnStdout: true).trim()
                def installer_pkg_name = installer_url.split('/')[-1]
                dir('installer_pkg') {
                    bat"""
                        wget -q ${installer_url} --no-check-certificate
                        mkdir -p ${env.WORKSPACE}\\${projectName}\\release\\installer_build\\x86_64 && cp -rf ${installer_pkg_name} ${env.WORKSPACE}\\${projectName}\\release\\installer_build\\x86_64\\
                        cd ${env.WORKSPACE}\\${projectName}\\release\\installer_build\\x86_64\\
                        tar xvzf ${installer_pkg_name}
                    """
                }
            }
        }

        stage('package') {
            def baseCommand = "python ci_script.py -a x86_64 -o windows -pv ${env.pes_version} -iv ${env.install_ver} -dv ${env.driver_ver}"
            def additionalCommand = ''

            switch (env.package_platform_type) {
                case 'pes':
                    additionalCommand = '-p'
                    break
                case 'pes-oem':
                    additionalCommand = '-pt oem'
                    break
                case 'pes-vdi':
                    additionalCommand = '-pt vdi'
                    break
                default:
                    error "Unsupported package platform type: ${env.package_platform_type}"
            }

            bat "${baseCommand} ${additionalCommand}"
        }
    }

    def fileName = env.package_platform_type == 'pes-vdi' ? vdi_file_name : (env.package_platform_type == 'pes-oem' ? oem_file_name : file_name)

    stage('stash pes_exe') {
        def paths = [
            'pes'     : "${env.WORKSPACE}\\${projectName}\\release\\installer_build",
            'pes-oem' : "${env.WORKSPACE}\\${projectName}\\release\\installer_oem_build",
            'pes-vdi' : "${env.WORKSPACE}\\${projectName}\\release\\installer_vdi_build"
        ]

        def fileNames = [
            'pes'     : 'mt-pes-windows-amd64.exe',
            'pes-oem' : 'mt-pes-windows-amd64-oem-wddm.exe',
            'pes-vdi' : 'mt-pes-windows-amd64-vdi-wddm.exe'
        ]

        def targetPath = paths[env.package_platform_type]
        def targetFileName = fileNames[env.package_platform_type]

        if (!targetPath || !targetFileName) {
            error "Unsupported package platform type: ${env.package_platform_type}"
        }

        bat "move ${targetPath}\\${targetFileName} ${targetPath}\\${fileName}"
        dir(targetPath) {
            stash name: 'pes_exe', includes: fileName
        }
    }

    node(env.iTrusSign_Nodes) {
        deleteDir()
        stage('unstash pes_exe') {
            unstash 'pes_exe'
        }

        timeout(2) {
            stage('iTrusSign') {
                bat"""
                    signtool sign /fd sha256 /sha1 ${constants.signtoolSha1} /t http://timestamp.globalsign.com/?signature=sha2 ${fileName}
                """
                sleep 15
            }
        }

        stage('stash signed files') {
            stash name:'signed_files', includes:"${fileName}"
        }
    }

    stage('unstash signed files') {
        unstash 'signed_files'
    }

    stage('upload') {
        oss.setUp()
        def ossCmd = "mc cp ${fileName} oss/${env.oss_save_path}/"
        def ossLatestCmd = "mc cp pes_latest.txt oss/${env.oss_save_path}/"
        def ossUrlPrefix = 'https://oss.mthreads.com/'
        if (env.wddm_driver_url && env.wddm_driver_url.contains('mt-vgpu')) {
            ossCmd = "mc cp ${fileName} swci-oss/${env.oss_save_path}/"
            ossLatestCmd = "mc cp pes_latest.txt swci-oss/${env.oss_save_path}/"
            ossUrlPrefix = 'https://swci-oss.mthreads.com/'
        }
        bat"""
            ${ossCmd}
            echo ${ossUrlPrefix}${env.oss_save_path}/${fileName} > pes_latest.txt
            ${ossLatestCmd}
        """
        if (env.package_platform_type == 'pes') {
            def pdbOssCmd = "mc cp ${pdb_file_name} oss/${env.oss_save_path}/pdb/"
            if (env.wddm_driver_url && env.wddm_driver_url.contains('mt-vgpu')) {
                pdbOssCmd = "mc cp ${pdb_file_name} swci-oss/${env.oss_save_path}/pdb/"
            }
            bat"""
                move ${env.WORKSPACE}\\${projectName}\\release\\mt-pes-windows-x86_64-pdb.tar.gz ${pdb_file_name}
                ${pdbOssCmd}
            """
            currentBuild.description += "mt-pes pdb pkg    : ${ossUrlPrefix}${env.oss_save_path}/pdb/${pdb_file_name} <br>"
        }
        currentBuild.description += "mt-pes pkg    : ${ossUrlPrefix}${env.oss_save_path}/${fileName} <br>"
        pes_pkg_url = "${ossUrlPrefix}${env.oss_save_path}/${fileName}"
    }

    stage('allinone') {
        if (env.buildAllinone == 'true' && env.package_platform_type == 'pes-vdi') {
            def gmi_branch = env.wddm_branch
            def version = '1.0.0'
            def branchVersionMap = [
                'release_vgpu_2.7.0': ['release_2.0',  '2.7.0-004'],
                'release_vgpu_2.5.6': ['release_1.12', '2.5.6-007'],
                'release_vgpu_2.7.5': ['release_2.0',  '2.7.5-003']
            ]
            if (branchVersionMap.containsKey(env.wddm_branch)) {
                (gmi_branch, version) = branchVersionMap[env.wddm_branch]
            }
            runPipeline.runJob([
                job: 'allinone-build-tool',
                wait:false,
                parameters: [
                    branch: env.wddm_branch,
                    version: version,
                    gmi_branch: gmi_branch,
                    pes_pkg_url: pes_pkg_url,
                    oss_save_path: env.oss_save_path,
                    host_pkg_save_path: env.host_pkg_save_path
                ]
            ])
        }
    }
}
