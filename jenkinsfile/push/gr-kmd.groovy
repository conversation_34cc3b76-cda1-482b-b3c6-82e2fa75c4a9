@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

def build(config) {
    def builds = config.buildList
    def buildTasks = [:]
    for (buildConfig in builds) {
        Map _buildConfig = buildConfig
        def pkgName = _buildConfig.pkgName.replace('rpm-', '')
        def kmdVersion = ''
        if (env.releaseVersion) {
            kmdVersion = ' -x ' + env.releaseVersion
        } else if (_buildConfig.releaseVersion) {
            kmdVersion = ' -x ' + _buildConfig.releaseVersion
        }
        buildTasks["${_buildConfig.pkgName}"] = {
            runPipeline.runJob([
                job: 'build.gr-kmd',
                parameters: [
                    repo: env.gitlabSourceRepoName ?: 'gr-kmd',
                    branch: env.gitlabTargetBranch ?: env.branch,
                    commitId: env.gitlabMergeRequestLastCommit ?: env.commitId,
                    triggerInfo: env.triggerInfo,
                    packageName: pkgName,
                    cmd: _buildConfig.cmd + kmdVersion,
                    exports: _buildConfig.env ? _buildConfig.env.join('\n') : '',
                    containerImage: _buildConfig.dockerImage ?: config.dockerImage
                ]
            ])
        }
    }
    parallel buildTasks
}

def updateLatestTxt() {
    constants.updateLatestTxt('gr-kmd', env.gitlabTargetBranch ?: env.branch, env.gitlabMergeRequestLastCommit ?: env.commitId)
}

runner.start(env.runChoice) {
    def config = [:]
    if (env.buildConfig) {
        config = readJSON text: env.buildConfig
        log.info('Get config from env')
    } else {
        config = ddk.loadBuildConfig('gr-kmd', env.gitlabTargetBranch ?: env.branch, [
            'pkgType': env.pkgType ?: 'release',
            'date': new Date().format('yyyy.MM.dd'),
        ])
        log.info('Get config from oss')
    }

    runPipeline([
        'buildPkg': [closure: { build(config) }],
        'update latest txt': [closure: { updateLatestTxt() }]
    ])
}
