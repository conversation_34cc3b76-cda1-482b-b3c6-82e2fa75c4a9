@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

def build() {
    runPipeline.runJob([
        job: 'build.mtgpu_snapshot',
        parameters: [
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            triggerInfo: env.triggerInfo,
            uploadPackage: 'true'
        ]
    ])
}

def updateLatestTxt() {
    constants.updateLatestTxt('mtgpu_snapshot', env.gitlabTargetBranch, env.gitlabMergeRequestLastCommit)
}

runner.start(env.runChoice) {
    runPipeline([
        'buildPkg': [closure: { build() }],
        'update latest txt': [closure: { updateLatestTxt() }]
    ])
}
