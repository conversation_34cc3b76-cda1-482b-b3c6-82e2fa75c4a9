@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

def build(config) {
    def builds = config.builds
    def buildTasks = [:]
    for (buildConfig in builds) {
        Map _buildConfig = buildConfig
        def name = _buildConfig.name ?: _buildConfig.job
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            repo: env.gitlabSourceRepoName,
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            triggerInfo: env.triggerInfo,
            testLabel: name
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        buildTasks["${_buildConfig.name ?: _buildConfig.job}"] = {
            runPipeline.runJob([
                job: "${_buildConfig.job}",
                parameters: parameters
            ])
        }
    }
    parallel buildTasks
}

def updateLatestTxt() {
    constants.updateLatestTxt(env.gitlabSourceRepoName, env.gitlabTargetBranch ?: env.branch, env.gitlabMergeRequestLastCommit ?: env.commitId)
}

runner.start(env.runChoice) {
    def config = null
    gitLib.fetchCode(env.gitlabSourceRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])

    dir(env.gitlabSourceRepoName) {
        config = commonLib.loadPipelineConfig('.ciConfig.yaml', "${env.gitlabSourceRepoName}/${env.gitlabTargetBranch}.yaml", [
            branch: env.gitlabSourceBranch,
            gitlabTargetBranch: env.gitlabTargetBranch,
            gitlabMergeRequestIid: env.gitlabMergeRequestIid,
            BUILD_ID: env.build_ID
        ], env.gitlabSourceRepoName)
    }

    runPipeline([
        'buildPkg': [closure: { build(config) }],
        'update latest txt': [closure: { updateLatestTxt() }]
    ])
}
