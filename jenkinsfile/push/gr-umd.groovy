@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

def build(config) {
    def builds = config.buildList
    def buildTasks = [:]
    for (buildConfig in builds) {
        Map _buildConfig = buildConfig
        buildTasks["${_buildConfig.pkgName}"] = {
            runPipeline.runJob([
                job: 'build.gr-umd',
                parameters: [
                    branch: env.gitlabTargetBranch ?: env.branch,
                    commitId: env.gitlabMergeRequestLastCommit ?: env.commitId,
                    triggerInfo: env.triggerInfo,
                    packageName: _buildConfig.pkgName,
                    cmd: _buildConfig.cmd,
                    exports: _buildConfig.env ? _buildConfig.env.join('\n') : '',
                    containerImage: _buildConfig.dockerImage ?: config.dockerImage
                ]
            ])
        }
    }
    parallel buildTasks
}

def uploadCase() {
    def latestCommitId = 0
    try {
        latestCommitId = constants.getLatestPackageCommitId('gr-umd', env.gitlabTargetBranch ?: env.branch)
    } catch (exc) {
        log.info(exc.message)
    }
    gitLib.fetchCode('gr-umd', env.gitlabTargetBranch ?: env.branch, env.gitlabMergeRequestLastCommit ?: env.commitId, [
        preBuildMerge: false,
        disableSubmodules: true
    ])
    dir('gr-umd') {
        if (fileExists('cts') && gitLib.isNewer(env.gitlabMergeRequestLastCommit ?: env.commitId, latestCommitId)) {
            oss.install()
            oss.cp('cts/*', "oss/sw-build/gr-umd/${env.gitlabTargetBranch ?: env.branch}/test/cts/")
        }
    }
}

def updateLatestTxt() {
    constants.updateLatestTxt('gr-umd', env.gitlabTargetBranch ?: env.branch, env.gitlabMergeRequestLastCommit ?: env.commitId)
}

runner.start(env.runChoice) {
    def config = [:]
    if (env.buildConfig) {
        config = readJSON text: env.buildConfig
        log.info('Get config from env')
    } else {
        config = ddk.loadBuildConfig('gr-umd', env.gitlabTargetBranch ?: env.branch, [
            'pkgType': env.pkgType ?: 'release',
            'date': new Date().format('yyyy.MM.dd'),
        ])
        log.info('Get config from oss')
    }

    runPipeline([
        'buildPkg': [closure: { build(config) }],
        'upload case': [closure: { uploadCase() }],
        'update latest txt': [closure: { updateLatestTxt() }]
    ])
}
