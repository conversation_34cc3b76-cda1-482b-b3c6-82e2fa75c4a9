@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

def build() {
    def buildTasks = [:]

    buildTasks['x86_build'] = {
        runPipeline.runJob([
            job: 'build.vgpu_daemon',
            parameters: [
                needUpload: 'true',
                triggerInfo: env.triggerInfo
            ]
        ])
    }

    buildTasks['aarch64_build'] = {
        runPipeline.runJob([
            job: 'build.vgpu_daemon',
            parameters: [
                containerImage: 'sh-harbor.mthreads.com/build-env/vgpu-daemon-aarch64:v1',
                packageName: 'vgpu_daemon_linux_arm64_Release.tar.gz',
                isAarch64: 'true',
                needUpload: 'true',
                triggerInfo: env.triggerInfo
            ]
        ])
    }

    buildTasks['win_build'] = {
        runPipeline.runJob([
            job: 'build.vgpu_daemon.win',
            parameters: [
                needUpload: 'true',
                triggerInfo: env.triggerInfo
            ]
        ])
    }

    if (!shouldIncludeLinuxBuilds()) {
        buildTasks.remove('x86_build')
        buildTasks.remove('aarch64_build')
        echo "Removed Linux builds for odd minor version: ${env.gitlabTargetBranch ?: env.branch}"
    }

    parallel buildTasks
}

def shouldIncludeLinuxBuilds() {
    def branch = env.gitlabTargetBranch ?: env.branch
    def versionPattern = ~/release_vgpu_(\d+)\.(\d+)\.(\d+)/
    def matcher = branch =~ versionPattern

    if (matcher.find()) {
        def minorVersion = matcher.group(2).toInteger()
        return minorVersion % 2 == 0
    }
    return true
}

def updateLatestTxt() {
    constants.updateLatestTxt('vgpu_daemon', env.gitlabTargetBranch ?: env.branch, env.gitlabMergeRequestLastCommit ?: env.commitId)
}

runner.start(env.runChoice) {
    runPipeline([
        'build': [ closure: { build() } ],
        'update latest txt': [closure: { updateLatestTxt() }]
    ])
}
