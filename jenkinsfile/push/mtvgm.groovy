@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

def build() {
    runPipeline.runJob([
        job: 'build.mtvgm',
        parameters: [
            needUpload: 'true',
            branch: env.branch,
            commitId: env.commitId,
            triggerInfo: env.triggerInfo
        ]
    ])
}

def updateLatestTxt() {
    constants.updateLatestTxt('mtvgm', env.gitlabTargetBranch ?: env.branch, env.gitlabMergeRequestLastCommit ?: env.commitId)
}

runner.start(env.runChoice) {
    runPipeline([
        'buildPkg': [closure: { build() }],
        'update latest txt': [closure: { updateLatestTxt() }]
    ])
}
