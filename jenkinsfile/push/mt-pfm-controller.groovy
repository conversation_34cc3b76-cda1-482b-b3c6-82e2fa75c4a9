@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

def build() {
    def buildTasks = [:]

    buildTasks['linux_build'] = {
        runPipeline.runJob([
            job: 'build.mt-pfm-controller',
            parameters: [
                triggerInfo: env.triggerInfo,
                branch: env.gitlabTargetBranch ?: env.branch,
                commitId: env.gitlabMergeRequestLastCommit ?: env.commitId,
                nodeLabel: 'Linux_build',
                pkgName: 'linux_mt_pfm_controller.tar.gz'
            ]
        ])
    }

    buildTasks['windows_build'] = {
        runPipeline.runJob([
            job: 'build.mt-pfm-controller',
            parameters: [
                triggerInfo: env.triggerInfo,
                branch: env.gitlabTargetBranch ?: env.branch,
                commitId: env.gitlabMergeRequestLastCommit ?: env.commitId,
                nodeLabel: 'Win11 && Build',
                cmd: 'python build.py --ci',
                containerImage: '',
                pkgName: 'windows_mt_pfm_controller.tar.gz'
            ]
        ])
    }

    parallel buildTasks
}

def updateLatestTxt() {
    constants.updateLatestTxt('mt-pfm-controller', env.gitlabTargetBranch ?: env.branch, env.gitlabMergeRequestLastCommit ?: env.commitId)
}

runner.start(env.runChoice) {
    runPipeline([
        'build': [closure: { build() }],
        'update latest txt': [closure: { updateLatestTxt() }]
    ])
}
