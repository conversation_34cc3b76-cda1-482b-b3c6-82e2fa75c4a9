@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

env.repo = 'muBLAS'

def build(config) {
    def buildTasks = [:]
    def builds = config.builds
    for (buildConfig in builds) {
        def _buildConfig = buildConfig
        if (_buildConfig.buildAfterMerged == 'true') {
            def defaultParameters = _buildConfig.parameters ?: [:]
            def parameters = [
                repo: env.repo,
                branch: env.gitlabSourceBranch,
                commitId: env.gitlabMergeRequestLastCommit,
                triggerInfo: env.triggerInfo,
                linuxDdkPackageUrl: "https://oss.mthreads.com/release-ci/computeQA/cuda_compatible/CI/stable/${defaultParameters.linuxDdkBranch}/musa_Ubuntu_amd64.deb",
                mtccPackageUrl: "https://oss.mthreads.com/release-ci/computeQA/cuda_compatible/CI/stable/${defaultParameters.linuxDdkBranch}/mtcc-nightly-x86_64-linux-gnu-ubuntu-22.04.tar.gz"
            ]
            defaultParameters.each { key, value ->
                parameters[key] = value
            }
            buildTasks["${_buildConfig.name ?: _buildConfig.job}"] = {
                runPipeline.runJob([
                    job: "${_buildConfig.job}",
                    parameters: parameters
                ])
            }
        }
    }
    parallel buildTasks
}

def updateLatestTxt() {
    constants.updateLatestTxt('muBLAS', env.gitlabTargetBranch, env.gitlabMergeRequestLastCommit)
}

runner.start(env.runChoice) {
    def config = null
    stage('checkout') {
        gitLib.fetchCode(repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])
        dir(repo) {
            config = commonLib.loadPipelineConfig('.ciConfig.yaml', "muBLAS/${env.gitlabTargetBranch}.yaml", [
            gitlabMergeRequestIid: env.gitlabMergeRequestIid,
            BUILD_ID: env.build_ID,
            commitId: env.gitlabMergeRequestLastCommit])
        }
    }
    def workflow = [:]
    if (config.builds && config.builds.size() > 0) {
        workflow['build'] = [ closure: { build(config) }, setGitlabStatus: true ]
    }
    workflow['upload lastest txt'] = [closure: { updateLatestTxt() }]
    runPipeline(workflow)
}
