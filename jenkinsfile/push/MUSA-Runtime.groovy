@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

env.repo = 'MUSA-Runtime'

def build(config) {
    def buildTasks = [:]
    def builds = config.builds
    for (buildConfig in builds) {
        def _buildConfig = buildConfig
        if (_buildConfig.buildAfterMerged == 'true') {
            def defaultParameters = _buildConfig.parameters ?: [:]
            def parameters = [
                repo: env.repo,
                branch: env.gitlabSourceBranch,
                commitId: env.gitlabMergeRequestLastCommit,
                triggerInfo: env.triggerInfo
            ]
            defaultParameters.each { key, value ->
                parameters[key] = value
            }
            buildTasks["${_buildConfig.name ?: _buildConfig.job}"] = {
                runPipeline.runJob([
                    job: "${_buildConfig.job}",
                    parameters: parameters
                ])
            }
        }
    }
    parallel buildTasks
}

def updateLatestTxt() {
    constants.updateLatestTxt('MUSA-Runtime', env.gitlabTargetBranch, env.gitlabMergeRequestLastCommit)
}

runner.start(env.runChoice) {
    def config = null
    stage('checkout') {
        gitLib.fetchCode(repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])
        dir(repo) {
            config = commonLib.loadPipelineConfig('.ciConfig.yaml', "MUSA-Runtime/${env.gitlabTargetBranch}.yaml", [
            gitlabMergeRequestIid: env.gitlabMergeRequestIid,
            BUILD_ID: env.build_ID,
            commitId: env.gitlabMergeRequestLastCommit])
        }
    }
    def workflow = [:]
    if (config.builds && config.builds.size() > 0) {
        workflow['build'] = [ closure: { build(config) }, setGitlabStatus: true ]
    }
    workflow['upload lastest txt'] = [closure: { updateLatestTxt() }]
    runPipeline(workflow)
}
