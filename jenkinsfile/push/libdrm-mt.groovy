@Library('swqa-ci')

import org.swqa.tools.common

commonLib = new common()

runner.start(env.runChoice) {
    // get config from repo gr-kmd
    // def config = gitLib.getFileContentByApi('gr-kmd', env.branch, 'buildConfig.yaml', 'yaml')

    // def configName = env.gitlabTargetBranch ?: 'develop'
    // def config = commonLib.loadConfig("gr-kmd/${configName}.yaml")
    def parameters = [
        updateLatestFile: 'true',
        triggerInfo: env.triggerInfo
    ]
    def workflow = [:]
    workflow['build'] = [ job: 'build.libdrm-mt', parameters: parameters, setGitlabStatus: true ]

    runPipeline(workflow)
}
