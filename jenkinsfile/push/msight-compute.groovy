@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

def build(config, type = 'linux/') {
    def builds = config.builds
    def buildTasks = [:]
    for (buildConfig in builds) {
        Map _buildConfig = buildConfig
        if (_buildConfig.buildAfterMerged == true) {
            def name = _buildConfig.name ?: _buildConfig.job
            def defaultParameters = _buildConfig.parameters ?: [:]
            def parameters = [
                repo: env.gitlabSourceRepoName,
                branch: env.gitlabSourceBranch,
                commitId: env.gitlabMergeRequestLastCommit,
                triggerInfo: env.triggerInfo
            ]

            if (_buildConfig.name.startsWith(type)) {
                defaultParameters.each { key, value ->
                    parameters[key] = value
                }
                buildTasks[name] = {
                    runPipeline.runJob([
                        job: _buildConfig.job,
                        parameters: parameters
                    ])
                }
            }
        }
    }
    parallel buildTasks
}

def updateLatestTxt() {
    constants.updateLatestTxt(env.gitlabSourceRepoName, env.gitlabTargetBranch, env.gitlabMergeRequestLastCommit)
}

runner.start(env.runChoice) {
    def config = null
    def currentDate = new Date().format('yyyy.MM.dd')
    env.commitId = gitLib.fetchCode(env.gitlabSourceRepoName, env.gitlabTargetBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])
    dir(env.gitlabSourceRepoName) {
        config = commonLib.loadPipelineConfig('.ciConfig.yaml', "${env.gitlabSourceRepoName}/${env.gitlabTargetBranch}.yaml", [
            branch: env.gitlabTargetBranch,
            commitId: env.commitId,
            gitlabActionType: env.gitlabActionType,
            currentDate: currentDate
        ], "${env.gitlabSourceRepoName}/default.yaml")
    }

    def workflow = [
        'linux-build': [closure: { build(config, 'linux/') }],
        'windows-build': [closure: { build(config, 'windows/') }],
        'update latest txt': [closure: { updateLatestTxt() }]
    ]

    runPipeline(workflow)
}
