@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

def build(config) {
    def builds = config.builds
    def buildTasks = [:]
    for (buildConfig in builds) {
        Map _buildConfig = buildConfig
        def name = _buildConfig.name ?: _buildConfig.job
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            repo: env.gitlabSourceRepoName,
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            triggerInfo: env.triggerInfo
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        buildTasks[name] = {
            runPipeline.runJob([
                job: _buildConfig.job,
                parameters: parameters
            ])
        }
    }
    parallel buildTasks
}

def updateLatestTxt() {
    constants.updateLatestTxt(env.gitlabSourceRepoName, env.gitlabTargetBranch, env.gitlabMergeRequestLastCommit)
}

runner.start(env.runChoice) {
    def config = null
    gitLib.fetchCode(env.gitlabSourceRepoName, env.gitlabTargetBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])
    dir(env.gitlabSourceRepoName) {
        config = commonLib.loadPipelineConfig('.ciConfig.yaml', "${env.gitlabSourceRepoName}/${env.gitlabTargetBranch}.yaml")
    }

    runPipeline([
        'buildPkg': [closure: { build(config) }],
        'update latest txt': [closure: { updateLatestTxt() }]
    ])
}
