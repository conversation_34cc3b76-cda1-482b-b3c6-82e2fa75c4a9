@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

def build(config) {
    def buildTasks = [:]
    def builds = config.builds

    for (buildConfig in builds) {
        def _buildConfig = buildConfig
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            repo: env.repo,
            branch: env.gitlabTargetBranch ?: env.branch,
            commitId: env.gitlabMergeRequestLastCommit ?: env.commitId,
            triggerInfo: env.triggerInfo
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }

        buildTasks["${_buildConfig.name ?: _buildConfig.job}"] = {
            runPipeline.runJob([
                job: "${_buildConfig.job}",
                parameters: parameters
            ])
        }
    }

    parallel buildTasks
}

runner.start(env.runChoice) {
    def config = null
    def branch = env.gitlabSourceBranch
    env.repo = env.gitlabSourceRepoName
    gitLib.fetchCode(env.repo, branch)
    dir(env.repo) {
        config = commonLib.loadPipelineConfig('.ciConfig.yaml', "${env.repo}/${branch}.yaml", [
            gitlabTargetBranch: env.gitlabTargetBranch,
            gitlabMergeRequestLastCommit: env.gitlabMergeRequestLastCommit
        ])
    }

    def workflow = [
        'build': [closure: { build(config) }]
    ]

    runPipeline(workflow)
}
