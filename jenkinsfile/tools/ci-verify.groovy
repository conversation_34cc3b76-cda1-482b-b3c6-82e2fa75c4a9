@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

needModifyContentRepos = '100000000'
ciVerifyNO = 'null'

// repos
// [
//     {
//         "repoName": "wddm",
//         "id": 536,
//         "sourceBranch": "ci-verify",
//         "targetBranch": "develop"
//     },
//     {
//         "repoName": "libdrm-mt",
//         "id": 546,
//         "sourceBranch": "ci-verify",
//         "targetBranch": "master"
//         "readme_file": "README%2Erst"
//     }
// ]

def installPackage() {
    utils.catchErrorContinue { timeout(5) { deleteDir() } }
    commonLib.retryByRandomTime({
        sh '''
            sudo apt update
            sudo dpkg --configure -a
            sudo apt install python3-pip -y
            sudo pip3 install --trusted-host nexus.infra.shg1.mthreads.com requests mysql-connector-python
        '''
    })
}

def getNeedModifyRepos() {
    if (env.isLoop == 'true') {
        ciVerifyNO = utils.getStatus('ci_verify_no')
        no = ciVerifyNO.toInteger()
        def newNO = no == 2 ? 0 : no + 1
        utils.setStatus('ci_verify_no', newNO)
    }
    def repos = readJSON text: env.repos
    repos.each { repo ->
        def repoName = repo['repoName']
        // def sourceBranch = repo['sourceBranch']
        def targetBranch = repo['targetBranch']

        credentials.runWithCredential('SSH_GITLAB') {
            println "Clone repo: ${repoName}"
            sh """
                rm -rf ${repoName} ||:
                git config --global user.email "<EMAIL>"
                git config --global user.name "git-robot"
                <NAME_EMAIL>:sw/${repoName}.git
            """
            dir(repoName) {
                def triggerNumber = repo['triggerNumber'] ?: 1
                for (int i = 0; i < triggerNumber; i++) {
                    println "Iteration $i"
                    def _targetBranch = i == 0 ? targetBranch : "${targetBranch}_${i}"
                    if (env.isLoop == 'true') {
                        _targetBranch = no == 0 ? targetBranch : "${targetBranch}_${no}"
                    }
                    def _sourceBranch = "ci_verify_for_${_targetBranch}"
                    def hasBranch = true

                    try {
                        sh "git ls-remote --heads origin | grep '${_sourceBranch}\$'"
                    } catch (e) {
                        hasBranch = false
                    }

                    if (!hasBranch) {
                        sh """
                            git checkout ${targetBranch}
                            git checkout -b ${_sourceBranch}
                            git push origin ${_sourceBranch}
                        """
                    }
                }
            }
        }
    }
}

def createMR() {
    def ci_verify_script = libraryResource('scripts/ci-verify.py')
    writeFile(file: 'ci-verify.py', text: ci_verify_script)
    writeFile(file: 'repos.json', text: env.repos)
    credentials.runWithCredential('API_GITLAB') {
        def apiToken = API_TOKEN_GITLAB
        credentials.runWithCredential('DB-SWQA-CI') {
            def databasePwd = PASSWORD
            sh(script: "python3 ci-verify.py 60 ${needModifyContentRepos} ${apiToken} ${databasePwd} ${ciVerifyNO}")
        }
    }
    def result = sh(script: 'cat desc.txt', returnStdout: true).trim()
    currentBuild.description = result
    if (result.contains('CI verify failed')) {
        error 'failed'
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'Install package': [ closure: { installPackage() } ],
        'Get need modify repos': [ closure: { getNeedModifyRepos() } ],
        'Create MR and get pipeline status': [ closure: { createMR() } ]
    ]

    runPipeline(workflow)
}
