@Library('swqa-ci')

import org.swqa.tools.git

runner.start(env.runChoice) {
    def repos = env.bases.split('\n')
    def tasks = [:]
    repos.each { line ->
        // the adding :default is to make sure split result contains at lease 3 contents
        def (repo, base, name) = "${line}:${env.branchName}".split(':')
        tasks["${repo}@${base} -> ${name}"] = {
            def commitId = null
            catchError(stageResult: 'FAILURE', buildResult: 'FAILURE') { commitId = new git().createGitlabBranchByApi(repo, name, base) }
            currentBuild.description += "${repo}: ${base} -> ${name}@${commitId}<br />"
        }
    }
    parallel tasks
}
