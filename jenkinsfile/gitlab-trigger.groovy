@Library('swqa-ci')

import org.apache.commons.lang3.StringEscapeUtils

import org.swqa.tools.common
import org.swqa.tools.jenkins
import org.swqa.tools.git

gitLib = new git()

GITLAB_PARAMS = [
    'gitlabSourceRepoHomepage',
    'gitlabSourceRepoSshUrl',
    'gitlabSourceRepoHttpUrl',
    'gitlabSourceRepoURL',
    'gitlabSourceNamespace',
    'gitlabSourceRepoName',
    'gitlabSourceBranch',
    '**********************',
    'gitlabTargetRepoHttpUrl',
    'gitlabTargetNamespace',
    'gitlabTargetRepoName',
    'gitlabTargetBranch',
    'gitlabBranch',
    'gitlabMergeRequestLastCommit',
    'gitlabActionType',
    'gitlabMergeRequestTitle',
    'gitlabMergeRequestDescription',
    'gitlabMergeRequestId',
    'gitlabMergeRequestIid',
    'gitlabMergeRequestState',
    'gitlabMergedByUser',
    'gitlabUserName',
    'gitlabUserEmail',
    'gitlabTriggerPhrase'
]

def extractDependencies(String line) {
    def pattern = ~/(?i)dependency\s*:\s*\[(.*?)\]/ // 不区分大小写，提取 [...] 中内容
    def matcher = pattern.matcher(line)
    if (!matcher.find()) {
        return [:] // 如果没匹配到 dependency，则返回空 map
    }
    def deps = matcher.group(1) // 拿到中括号里的内容
    def result = [:]
    deps.split(/\s*,\s*/).each { item ->
        def (name, version) = item.split(':', 2) + ['latest'] // 如果没有 :，默认加 latest
        result[name.trim()] = version.trim()
    }
    return result
}

def main() {
    if (env.gitlabTriggerPhrase =~ /(?i)aipass/) {
        gitLib.setGitlabStatus('jenkins/ai_analysis', 'success')
        return
    }
    Map conf = new common().loadConfig('triggers.yaml')
    log.info(conf)
    if (!(env.gitlabSourceRepoName in conf)) {
        throw new Exception("No trigger config found for repo: ${env.gitlabSourceRepoName}")
    }
    if (gitLib.triggeredByMR() && gitLib.isMRconflict()) {
        gitLib.setGitlabStatus('jenkins/isMRconflict', 'failed')
        return
    }
    def jobCandidates = []
    for (def job in conf[env.gitlabSourceRepoName]) {
        def genJobName = env.gitlabActionType ==~ /MERGE|NOTE/ ? 'mr.scheduler' : 'push.scheduler'
        def jobName = job?.name ?: genJobName
        if (env.gitlabActionType == 'MERGE' && env.gitlabMergeRequestState == 'closed') {
            new jenkins().cancelPreviousBuild(jobName)
            continue
        }
        def triggers = job.triggers ?: conf['default-triggers']
        Boolean shouldRun = triggers.any { trigger ->
            trigger.every {
                k, v -> env[k] =~ v
            }
        }
        if (shouldRun) {
            try {
                condition = [
                    status: ['=', 'blocked'],
                    repo: ['=', env.gitlabSourceRepoName],
                    branch: ['=', env.gitlabTargetBranch],
                ]
                def data = db.selectData('swqa_ci', 'ci_block', condition, 'repo, branch, test, status, asignee, comments')
                if (data) {
                    // blocked
                    def row = data.split('\n')[-1]
                    def (repo, branch, test, status, asignee, comments) = row.split('\t')
                    log.debug("found block issue - repo(${repo}) branch(${branch}) test(${test}) status(${status}) asignee(${asignee}) comments(${comments})")
                    if (!asignee.contains(env.gitlabUserName)) {
                        def message = "${repo}@${branch} has block issue: ${comments}, only ${asignee} can trigger CI now."
                        log.error(message)
                        gitLib.addCommentForMR(message)
                        return
                    }
                }
            } catch (e) {
                log.error("failed to get block issue info: ${e.message}")
            }
            jobCandidates.add(job)
            new jenkins().cancelPreviousBuild(jobName)
            def triggerInfoList = GITLAB_PARAMS.findAll {
                !utils.isEmpty(env[it])
            }.collect {
                "${it}:${StringEscapeUtils.escapeJava(env[it])}"
            }
            // this debug will be passed to downstream jobs within triggerInfo
            def debug = job.containsKey('debug') ? job.debug : env.debug
            triggerInfoList.add("debug:${debug}")
            String triggerInfo = triggerInfoList.join(constants.triggerInfoDelimiter)
            runPipeline.runJob([job: jobName, parameters: ['triggerInfo': triggerInfo], wait: false])
        } else {
            log.info("Skip job ${jobName} as trigger event does not match configuration")
        }
    }
    if (jobCandidates.size() > 0) {
        String link = "http://swqa.mthreads.com/jenkins_pipeline?git_mr_repo=${env.gitlabSourceRepoName}&git_mr_id=${env.gitlabMergeRequestIid}&git_mr_latest_commit=${env.gitlabMergeRequestLastCommit}"
        String message = "🚀 Pipeline started. Track it via ➡️ [CI Dashboard](${link})"
        catchError(buildResult: null, stageResult: null) {
            if (env.gitlabActionType == 'NOTE') {
                def statuses = gitLib.getGitlabCommitAllStatuses(env.gitlabSourceRepoName, env.gitlabMergeRequestLastCommit, env.gitlabSourceBranch)
                statuses = statuses.findAll { it.status != 'success' }
                message += "  \nTriggered by comment [${env.gitlabTriggerPhrase}]"
                if (statuses && statuses.size() > 0) {
                    message += ', Unsuccess pipelines:'
                    statuses.each { message += "\n- ${it.name}: ${it.status}" }
                }
            // TODO: Add support for cirerun dependency: [repo1:commit1,repo2]
            // def dependency = extractDependencies(env.gitlabTriggerPhrase)
            // check all packages in dependency
            // message += "\nDependency: ${dependency}"
            }
        }
        gitLib.addCommentForMR(message)
    }
}
runner.start(env.runChoice) {
    def workflow = [
        'Trigger jobs': [ closure: { main() } ]
    ]
    runPipeline(workflow)
}
