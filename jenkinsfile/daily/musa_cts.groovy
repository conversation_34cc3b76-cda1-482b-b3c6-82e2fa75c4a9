@Library('swqa-ci@dailyCI')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (String) - Linux_build
 * cluster (String) - shfarm
 * podNodeSelector (String) - In=mt=buildserver
 * podResources (String) - requests=cpu=8;requests=memory=64Gi;limits=cpu=64;limits=memory=128Gi;
 * containerImage (String) - sh-harbor.mthreads.com/qa/m3d_test:v10
 * musa_cts_branch (String) - 'm3d_master'
 * linuxDdkPackageUrl (String) - ''
 * musaToolkitsPackageUrl (String) - ''
 * envExport (String) - ''
 * compileParallelNumber (int) - 12
 * compileArgs (String) - ''
 * musaAsmPackageUrl (String) - 'https://oss.mthreads.com/release-ci/computeQA/cuda_compatible/newest/master/musa_asm.tar.gz'
 * work_dir (String) - 'pytest'
 * test_cmd (String) - ''
 * gpuArch (String) - 'mp_31'
*/

env.repo = 'musa_cts'
env.mtcc_test_repo = 'mtcc_test'
coverage_report_name = ''
WORK_SPACE_MTCC = '/home/<USER>/agent/workspace/build.mtcc'

def fetchCode() {
    gitLib.fetchCode(env.repo, env.musa_cts_branch)
    // coverage
    if ((env.gCover == 'true') && (env.test_cmd.contains('mtcc')) && (env.podNodeSelector.contains('s5000'))) {
        gitLib.fetchCode(env.mtcc_test_repo, env.mtcc_test_branch)
    }
}

def setupDepends() {
    if (env.setup_ddk == 'true' && env.linuxDdkPackageUrl) {
        ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    }
    if (env.runChoice == 'node' && env.containerImage == '') {
        ddk.installLinuxDdk(env.linuxDdkPackageUrl)
        try {
            commonLib.restartLinuxNode()
        } finally {
            sleep time: 3, unit: 'MINUTES'
        }
    }

    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(env.musaToolkitsPackageUrl)
        // coverage
        if ((env.gCover == 'true') && (env.test_cmd.contains('mtcc')) && (env.podNodeSelector.contains('s5000'))) {
            if (env.mtccPackageUrl) {
                installDependency(['mtcc': env.mtccPackageUrl])
            }
            if (env.mtccSrcPackageUrl) {
                constants.downloadAndUnzipPackage(env.mtccSrcPackageUrl, WORK_SPACE_MTCC)
            }
        }
    }
    else if (env.musaSdkPackageUrl) {
        sh """
            wget ${env.musaSdkPackageUrl}
            dpkg -i ${env.musaSdkPackageUrl.split('/')[-1]}
            modprobe -rv mtgpu
            modprobe -v mtgpu
        """
    }

    if (env.muAlgPackageUrl) {
        musa.installmuAlg(env.muAlgPackageUrl)
    }
    else {
        gitLib.fetchCode('muAlg', 'develop')
        dir('muAlg') {
            sh './mt_build.sh -i'
        }
    }

    if (env.muThrustPackageUrl) {
        musa.installmuThrust(env.muThrustPackageUrl)
    }
    else {
        gitLib.fetchCode('muThrust', 'develop')
        dir('muThrust') {
            sh './mt_build.sh -i'
        }
    }

    dir(env.repo) {
        if (env.gpuArch == 'mp_31') {
            constants.downloadAndUnzipPackage(env.musaAsmPackageUrl)
            sh '''
                cd musa_asm/build/bin
                cp musaasm /usr/local/musa/bin/
            '''

            sh """
                wget --no-check-certificate ${env.mtcToolUrl} -O mtc_tool
                chmod +x mtc_tool
                cp mtc_tool /usr/local/bin/
            """
        }
        else if (env.gpuArch == 'm1000') {
            constants.downloadAndUnzipPackage(env.musaAsmPackageUrl)
            sh '''
                cd musa_asm/build/bin
                cp musaasm /usr/local/musa/bin/
            '''
        }
    }
}

def runCtsTest() {
    timeout(time: env.TIMEOUT.toInteger(), unit: 'HOURS') {
        // build
        def envVars = """
            export PATH=/usr/local/musa/bin:\${PATH}
            export LD_LIBRARY_PATH=/usr/local/musa/lib:\${LD_LIBRARY_PATH}
        """
        dir(env.repo) {
            sh """
                ${envVars}
                ${env.envExport}
                make porting-cuda-to-musa
                make build-all
            """
        }
        // test
        dir(env.repo) {
            sh """
                ${envVars}
                ${env.envExport}
                cd ${env.work_dir}
                ${test_cmd} --alluredir=allure_result_musa_cts ||:
                tar -czf m3d_musa_cts_allure_result.tar.gz allure_result_musa_cts/
            """
        }
    }

    if (env.TIMEDEBUG?.trim()) {
        timeout(time: env.TIMEDEBUG.toInteger(), unit: 'HOURS') {
            input message: "CI环境将保留 ${env.TIMEDEBUG} 小时，请选择:\nProceed(继续流水线)\n Abort(终止流水线)"
        }
    }
}

def runCoverageMtcc() {
    timeout(time: env.TIMEOUT.toInteger(), unit: 'HOURS') {
        // mtcc coverage 通过编译musa_cts和编译mtcc_test进行统计
        if ((env.gCover == 'true') && (env.test_cmd.contains('mtcc')) && (env.podNodeSelector.contains('s5000'))) {
            def arch_list = ['sudi': 'mp_10', 'quyuan1': 'mp_21', 'quyuan2': 'mp_22', 'ph1': 'mp_31', 'ph1s': 'mp_32']
            for (arch in arch_list.entrySet()) {
                stage("${arch.key}") {
                    println " ******** currently compile based on the MTGPU_ARCH: ${arch.key}. ******** "
                    def envVars_gcov = """
                        export MUSA_LIB_PATH=/usr/local/musa/lib
                        # export MTCC_LIB_PATH=${WORK_SPACE_MTCC}/mtcc/build/lib
                        export PATH=/usr/local/musa/bin:\${PATH}
                        # export PATH=${WORK_SPACE_MTCC}/mtcc/build/bin:\${PATH}
                        export LIBRARY_PATH=/usr/local/musa/lib:\${LIBRARY_PATH}
                        export LD_LIBRARY_PATH=/usr/local/musa/lib:\${LD_LIBRARY_PATH}
                        # export MUSA_PATH=${WORK_SPACE_MTCC}/mtcc/build
                        # export MUSA_LIBDEVICE_PATH=${WORK_SPACE_MTCC}/mtcc/build/mtgpu/bitcode
                        # export CMAKE_C_COMPILER=${WORK_SPACE_MTCC}/mtcc/build/bin/clang
                        # export CMAKE_CXX_COMPILER=${WORK_SPACE_MTCC}/mtcc/build/bin/clang++
                        export MTGPU_ARCH=${arch.value}
                    """
                    dir(env.repo) {
                        sh """
                            ${envVars_gcov}
                            ${env.envExport}
                            make porting-cuda-to-musa
                            make build-all
                        """
                    }
                    dir(env.mtcc_test_repo) {
                        sh """
                            ${envVars_gcov}
                            ${env.envExport}
                            cd tools && ./porting_for_ci.sh > /dev/null
                            cd ..
                            mkdir -p ${env.WORKSPACE}/mtcc_test/compile_log
                            export PATH=${env.WORKSPACE}/mtcc_test/utils/lit:\${PATH}
                            export RUN_TYPE=compile
                            lit.py cuda testsuit -Ddevice=${arch.key} -Dtest_run=false -j64 --timeout 60 ||:
                        """
                    }
                }
            }
            sh """
                cd ${WORK_SPACE_MTCC}
                ls -lh `find . -name *.gcda`
            """
            gcov_plan = mathxCoverage.gcov_plan.get('mtcc')
            coverage_report_name = mathxCoverage.mtccGenerateCoverage(gcov_plan.'gcov_directory', gcov_plan.'src_remove', 'mtcc')
            println "coverage_report_name: ${coverage_report_name}"  // mtcc_coverage_report
            if (coverage_report_name) {
                // 读取代码覆盖率数据，并写入influxdb
                catchError(stageResult: 'FAILURE') {
                    commonLib.loadScript('mtcc_coverage_writetoInfluxDB.py', 'coverage', false)
                    constants.downloadAndUnzipPackage(env.anacondaPackageUrl, '/home/<USER>')
                    sh """
                        ${constants.genCondaActivate('mathx')}
                        ls -l
                        python mtcc_coverage_writetoInfluxDB.py --indexfile ${env.WORKSPACE}/${coverage_report_name}/index.html --product mtcc
                    """
                }
                commonLib.publishHTML(coverage_report_name, coverage_report_name)
            }
            else {
                println 'generate coverage fail!'
                currentBuild.description += '<b>generate coverage fail!</b><br>'
            }
        }
        else {
            println ' ******** No need to run coverage of mtcc on the build! ******** '
        }
    }

    if (env.TIMEDEBUG?.trim()) {
        timeout(time: env.TIMEDEBUG.toInteger(), unit: 'HOURS') {
            input message: "CI环境将保留 ${env.TIMEDEBUG} 小时，请选择:\nProceed(继续流水线)\n Abort(终止流水线)"
        }
    }
}

def checkResult() {
    dir(env.repo) {
        commonLib.allure("${env.work_dir}/allure_result_musa_cts")
        if (currentBuild.result == 'UNSTABLE') { error "Test failed, please check: ${allureReportUrl}" }
    }
}

def uploadTestResult() {
    dir(env.repo) {
        sh "tar -czvf ${env.repo}_allure_result_${BUILD_NUMBER}.tar.gz ${env.work_dir}/allure_result_musa_cts"
        artifact.uploadTestReport("${env.repo}_allure_result_${BUILD_NUMBER}.tar.gz", env.reportOssPath)
    }

    catchError(stageResult: 'FAILURE') {
        if (env.gCover == 'true') {
            sh"""
                pwd
                ls -l
                tar -czvf ${coverage_report_name}_${BUILD_NUMBER}.tar.gz ${coverage_report_name}
                ls -l
            """
            artifact.uploadTestReport("${coverage_report_name}_${BUILD_NUMBER}.tar.gz", env.reportOssPath)
        }
    }
}

def unintallMusaSDK() {
    if (env.gpuArch == 'm1000') {
        sh 'dpkg -P musa-sdk ||:'
    }
}

runner.start(env.runChoice, [
    main: {
        runPipeline([
            'checkout': [closure: { fetchCode() }],
            'setup in docker': [closure: { setupDepends() }],
            'musa_cts test': [closure: { runCtsTest() }],
            'run Coverage Mtcc': [closure: { runCoverageMtcc() }],
        ], [disablePre: true, disablePost: true])
    },
    post: {
        runPipeline([
            'upload result': [closure: {
                catchError(stageResult: 'FAILURE') {
                        uploadTestResult()
                }
            }
            ],
            'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
            'teardown': [closure: { unintallMusaSDK() }],
        ], [disablePre: true])
    }
])
