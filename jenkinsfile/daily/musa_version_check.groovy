@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

/*
 * parameters
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (String) - Linux_build
 * cluster (String) - shfarm
 * podNodeSelector (String) - In=mt=buildserver
 * podResources (String) - requests=cpu=8;requests=memory=64Gi;limits=cpu=64;limits=memory=128Gi;
 * containerImage (String) - sh-harbor.mthreads.com/qa/m3d_test:v10
 * musaToolkitsPackageUrl (String) - ''
 * muDNNPackageUrl (String) - ''
 * mcclPackageUrl (String) - ''
 * envExport (String) - ''
*/

gitLib = new git()
commonLib = new common()

def setupDepends() {
    if (env.musaSdkPackageUrl) {
        sh """
            dpkg -P musa-sdk ||:
            wget ${env.musaSdkPackageUrl}
            dpkg -i ${env.musaSdkPackageUrl.split('/')[-1]}
        """
    } else {
        if (env.musaToolkitsPackageUrl) {
            musa.installMusaToolkits(env.musaToolkitsPackageUrl)
        }

        if (env.muDNNPackageUrl) {
            sh """
                wget ${env.muDNNPackageUrl}
                tar -xf ${env.muDNNPackageUrl.split('/')[-1]}
                cd mudnn
                ./install_mudnn.sh
            """
        }

        if (env.mcclPackageUrl) {
            sh """
                wget ${env.mcclPackageUrl}
                tar -xf ${env.mcclPackageUrl.split('/')[-1]}
                cd mccl
                ./install.sh
            """
        }
    }
}

def checkVersions() {
    def versionChecks = [:]

    if (env.musaToolkitsPackageUrl) {
        versionChecks['musa_runtime'] = [
            header: 'musart_version.h',
            macro: 'MUSART_VERSION',
            expect: env.musa_runtime_version
        ]
    }

    if (env.mcclPackageUrl) {
        versionChecks['mccl'] = [
            header: 'mccl.h',
            macro: 'MCCL_VERSION_CODE',
            expect: env.mccl_version
        ]
    }

    if (env.mudnnPackageUrl) {
        versionChecks['mudnn'] = [
            header: 'mudnn_version.h',
            macro: 'MUDNN_VERSION',
            expect: env.mudnn_version
        ]
    }

    def results = [:]

    versionChecks.each { name, config ->
        def versionOutput = sh(
            script: """
                if [ ! -f "/usr/local/musa/include/${config.header}" ]; then
                    echo "Header file not found: ${config.header}"
                    exit 1
                fi

                cat > /tmp/version_check.cpp << 'EOF'
                #include <stdio.h>
                #include "${config.header}"
                int main() {
                    printf("%d\\n", ${config.macro});
                    return 0;
                }
EOF

                g++ -I/usr/local/musa/include /tmp/version_check.cpp -o /tmp/version_check 2>/dev/null
                /tmp/version_check
            """,
            returnStdout: true
        ).trim()

        if (config.expect != versionOutput) {
            results[name] = "Failed, expect: ${config.expect}, actual: ${versionOutput}"
        } else {
            results[name] = "Pass, expect: ${config.expect}, actual: ${versionOutput}"
        }

        echo "Version of ${name}: ${versionOutput}"
    }

    // 输出所有版本信息到文件
    def outputLines = []
    if (results['musa_runtime']) {
        outputLines.add("MUSA Runtime Version: ${results['musa_runtime']}")
    }
    if (results['mccl']) {
        outputLines.add("MCCL Version: ${results['mccl']}")
    }
    if (results['mudnn']) {
        outputLines.add("MUDNN Version: ${results['mudnn']}")
    }

    sh """
        cat > musa_versions.txt << 'EOF'
${outputLines.join('\n')}
EOF
        cat musa_versions.txt
    """
}

def runVersionCheck() {
    timeout(time: 30, unit: 'MINUTES') {
        checkVersions()
    }
}

def reportResults() {
    sh '''
        if [ -f "musa_versions.txt" ]; then
            echo "===== MUSA Components Version Check Results ====="
            cat musa_versions.txt
            echo "================================================"
        else
            echo "ERROR: Version check results not found"
            exit 1
        fi
    '''

    sh '''
        if grep -q Failed musa_versions.txt; then
            exit 1
        fi
    '''
}

runner.start(env.runChoice) {
    def workflow = [:]
    workflow.setupDepends = [closure: { setupDepends() }]
    workflow.checkVersions = [closure: { runVersionCheck() }]
    workflow.report = [closure: { reportResults() }]
    runPipeline(workflow)
}
