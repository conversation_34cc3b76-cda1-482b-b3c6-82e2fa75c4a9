/* groovylint-disable-next-line UnusedImport */
@Library('swqa-ci')

import org.swqa.tools.common

def main() {
    def config = readJSON text: env.config
    def date = env.date ?: new Date().format('yyyy-MM-dd')
    config.each { repo, branches ->
        branches.each { branch ->
            def latestCommitId = constants.getLatestPackageCommitId(repo, branch)
            def sourcePath = constants.genOssPath(repo, branch, latestCommitId)
            def destPath = constants.genOssPath(repo, branch)
            oss.cp("oss/${sourcePath}", './')
            dir(latestCommitId) {
                oss.cp('./*', "oss/${destPath}/${date}/")
            }
        }
    }
}

runner.start(env.runChoice, {
    def workflow = [
        'main': [closure: { main() }],
    ]
    runPipeline(workflow)
})
