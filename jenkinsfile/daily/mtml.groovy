@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

//para: dockerImage，runChoice，linuxDdkBranch，linuxDdkPackageUrl，branch，commit，mtml_version，GPU，exclude_cases，mailReceiver(run自带发送邮件功能),gmi_case_file,testMtml,testGmi

//install ddk2.0
def installDriver() {
    ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
}

//mtml test
def mtmlTest() {
    docker.image(env.dockerImage).inside('-i -u 0:0 --privileged=true -v /dev:/dev -v /sys/:/sys') {
        env.mtmlCommitId = gitLib.fetchCode('mt-management', env.branch, env.mtmlCommitId)
        dir('mt-management') {
            sh """
                ./build_ci.sh RELEASE x86_64 ${env.mtml_version} NO
                cp sdk/LINUX/x86_64/RELEASE/lib/* /usr/lib/

                rm -rf /usr/include/gtest
                cp src/mtml_build_info.inc /usr/include/
                cp include/mtml/* /usr/include/

                apt install pciutils -y
            """
        }
        env.gfxCommitId = gitLib.fetchCode('gfxswtest', 'sdk_test', env.gfxCommitId)
        dir('gfxswtest/modules/sdk/mtml') {
            sh """
                cmake -B build . -DBUILD_MTML=OFF -DDYNAMIC=ON -DBUILD_VERSION=${env.mtml_version}
                cmake --build build
            """
            filter_info = ''
            card = env.GPU
            if (env.exclude_cases) {
                print(env.exclude_cases)
                filter_info = '--gtest_filter=-'
                for (line in env.exclude_cases.split('\n')) {
                    filter_info += ":${line}"
                }
            }
            sh "./build/sdk_gtest ${card} ${filter_info}"
        }
    }
}

//gmi test
def gmiTest() {
    def hostIp = commonLib.getNodeIP()
    env.commitId = gitLib.fetchCode('qa_sdk', 'develop', env.commitId)
    dir('qa_sdk/gmi') {
        sh """
            pip3 install -r requirements.txt
            cd test_cases
            pytest ${env.gmi_case_file} --host=${hostIp} --username=root --password=Passw0rd!
        """
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'install driver': [closure: { installDriver() }]
    ]
    if (env.testMtml == 'true') {
        workflow['test mtml'] = [closure: { mtmlTest() }]
    }
    if (env.testGmi == 'true') {
        workflow['test gmi'] = [closure: { gmiTest() }]
    }

    runPipeline(workflow)
}
