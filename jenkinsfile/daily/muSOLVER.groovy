@Library('swqa-ci')
import org.swqa.tools.git

gitLib = new git()

muSOLVER_branch = env.useDatetag == 'true' ? constants.genDateTag(env.dateTag) + '_' + env.muSOLVER_branch : env.muSOLVER_branch
muSOLVER_cts_branch = env.useDatetag == 'true' ? constants.genDateTag(env.dateTag) + '_' + env.muSOLVER_cts_branch : env.muSOLVER_cts_branch
packageName = 'muSOLVER.tar.gz'

def getCommit() {
    //get muSOLVER commitID and paras
    muSOLVERcommitId = constants.formatCommitID(gitLib.getGitlabRepocommitByApi('muSOLVER', muSOLVER_branch))
    currentBuild.description += "muSOLVER: [branch:${muSOLVER_branch},commitID:${muSOLVERcommitId}]<br>"
    //get muSOLVER_cts commitID and paras
    muSOLVERctscommitId = constants.formatCommitID(gitLib.getGitlabRepocommitByApi('muSOLVER_cts', muSOLVER_cts_branch))
    currentBuild.description += "muSOLVER_cts: [branch:${muSOLVER_cts_branch},commitID:${muSOLVERctscommitId}]<br>"
}

def buildMusolver() {
    packageOssPath = constants.genPackagePath('muSOLVER', muSOLVER_branch, muSOLVERcommitId, packageName)
    if (artifact.packageExistsOnOss(packageOssPath)) {
        currentBuild.description += "muSOLVER pkg PATH: ${packageOssPath}<br>"
    }else {
        runPipeline.runJob([job: 'build.muSOLVER', parameters: ['branch': muSOLVER_branch]])
    }
}

def testMusolver(String podNodeSelector) {
    musolverctsTestParam = [
        'branch': muSOLVER_cts_branch,
        'muSOLVERcommitId': muSOLVERcommitId,
        'test_type': env.test_type,
        'muSOLVERPackageUrl': constants.ossPathToUrl(packageOssPath),
        'podNodeSelector': podNodeSelector,
    ]
    runPipeline.runJob([job: 'test.muSOLVER_cts', parameters: musolverctsTestParam])
}

def checkRunreport() {
    musolverctsDirPath = "oss/${constants.genOssPath('muSOLVER_cts', muSOLVER_cts_branch, "${muSOLVERcommitId}_${muSOLVERctscommitId}")}"
    genReportparam = [
        'report_rely_pkg_url':musolverctsDirPath,
        'report_generate_pkg_url':musolverctsDirPath
    ]
    runPipeline.runJob([job: '890_gen_report_gitlab', parameters: genReportparam])
}

node {
    def workflow = [
        'checkout': [closure: { getCommit() }],
        'buildMusolver': [closure: { buildMusolver() }],
        'testMusolver-S80': [closure: { testMusolver('In=PLATFORM=gpu;In=GPU_TYPE=s80') }, async: true],
        'testMusolver-S3000': [closure: { testMusolver('In=PLATFORM=gpu;In=GPU_TYPE=s3000') }, async: true],
        'testMusolver-S4000': [closure: { testMusolver('In=PLATFORM=gpu;In=GPU_TYPE=s4000') }, async: true],
        'checkRunreport': [closure: { checkRunreport() }],
    ]
    runPipeline(workflow)
}
