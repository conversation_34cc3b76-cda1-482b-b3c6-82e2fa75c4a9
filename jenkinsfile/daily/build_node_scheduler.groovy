@Library('swqa-ci')

import org.swqa.tools.common

commonLib = new common()

def loadScript() {
    commonLib.loadScript('build_node_scheduler.py', 'linux')
}

def runScheduler() {
    sh "python build_node_scheduler.py --return_hour=${env.returnHour} --min_buildserver=${env.minBuildServer} --min_vps=${env.minVps} --computebuild_ratio=${env.computeBuildRatio}"
}

runner.start(env.runChoice, [main: {
    runPipeline([
        'loadScript': [closure: { loadScript() }],
        'runScheduler': [closure: { runScheduler() }],
    ])
}])
