@Library('swqa-ci')
import org.swqa.tools.common

commonLib = new common()
/*
 * parameters
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (String) - Linux_jump
 * cluster_build (String) - releaseFarm
 * cluster_test (String) - releaseFarm
 * podNodeSelector (String) - In=mt=buildserver 编译job的选择器
 * ddk_branch (String) - ''
 * OSS_SAVE_URL (String) - oss/release-ci/computeQA/cuda_compatible/CI/${ddk_branch}/${BUILD_TIMESTAMP}/
 * aoto_subfolder (boolean) - true
 * ciConfig (String) - https://oss.mtheads.com/release_ci/computeQA/cuda_compatible/CI/{ddk_branch}/.ciConfig.yaml
*/

//constant values
SAVE_URI = "${constants.OSS.URL_PREFIX}/${env.OSS_SAVE_URL}"
SAVE_OSS_PATH = "oss/${env.OSS_SAVE_URL}"
if (SAVE_OSS_PATH.endsWith('/')) {
    SAVE_OSS_PATH = SAVE_OSS_PATH.substring(0, SAVE_OSS_PATH.length() - 1)
}
if (SAVE_URI.endsWith('/')) {
    SAVE_URI = SAVE_URI.substring(0, SAVE_URI.length() - 1)
}

if (env.auto_subfolder == 'true') {
    BUILD_TIMESTAMP = env.BUILD_TIMESTAMP.split('_')[0]
    SAVE_URI = "${SAVE_URI}/${BUILD_TIMESTAMP}"
    SAVE_OSS_PATH = "${SAVE_OSS_PATH}/${BUILD_TIMESTAMP}"
}

//value will be set by confirm_ddk()
ddk_url = ''
ddk_commit = ''
gCover = 'false'
target_toolkit_name = ''
target_toolkitsrc_name = ''
target_mtcc_name = ''
target_mtccsrc_name = ''
target_mudnn_name = ''
target_mudnnsrc_name = ''
// musaRuntimePackageUrl = ''

//value will be set by adjust_ddk()
target_ddk_name = ''

// PackageUrl
linuxDdkPackageUrl = ''
musaToolkitsPackageUrl = ''
musaToolkitsSrcPackageUrl = ''
mtccPackageUrl = ''
mtccSrcPackageUrl = ''
muDNNPackageUrl = ''
muDNNPerfPackageUrl = ''
mcclPackageUrl = ''

/*
1. fetch latest ddk
*/
def confirm_ddk(ddk_branch) {
    def pkg_suffix = env.ddk_suffix
    ddk_commit = constants.getLatestPackageCommitId('linux-ddk', ddk_branch)
    try {
        if (SAVE_OSS_PATH && !SAVE_OSS_PATH.endsWith('others')) {
            utils.appendVersionInfo('linux-ddk', ddk_branch, ddk_commit, "${SAVE_OSS_PATH}/others", 'musa_sdk_commit.txt')
        }
    } catch (e) {
        println "追加版本信息文件到OSS失败: ${e.getMessage()}"
    }

    // copy latest.txt of linux-ddk to daily build dir of oss.
    String ossPath_ddk = constants.genLatestOssPath('linux-ddk', ddk_branch)
    def ossPath_ddk_latest = "${constants.genOssAlias(ossPath_ddk)}/${ossPath_ddk}/latest.txt"
    println "ddk_latest: ${ossPath_ddk_latest}"
    oss.setUp()
    oss.cp(ossPath_ddk_latest, "${SAVE_OSS_PATH}")

    def ddk_url_prefix = constants.genOssPath('linux-ddk', ddk_branch, ddk_commit)
    ddk_url = "oss/${ddk_url_prefix}/${ddk_commit}_${pkg_suffix}"
    def dayOfWeek = Calendar.getInstance().get(Calendar.DAY_OF_WEEK)
    println "当前星期几(1=周日, 7=周六): ${dayOfWeek}"
    if (dayOfWeek in [1, 4, 7]) {
        gCover = 'true'
        target_toolkit_name = 'musa_toolkits_install_full_gcov.tar.gz'
        target_toolkitsrc_name = 'musa_toolkits_install_full_gcov_src.tar.gz'
        target_mtcc_name = 'mtcc-x86_64-linux-gnu-ubuntu_gcov.tar.gz'
        target_mtccsrc_name = 'mtcc_src.tar.gz'
        target_mudnn_name = ''  // todo
        target_mudnnsrc_name = ''  // todo
    }
    else {
        target_toolkit_name = 'musa_toolkits_install_full.tar.gz'
        target_mtcc_name = 'mtcc-x86_64-linux-gnu-ubuntu.tar.gz'
        target_mudnn_name = ''  // todo
    }
    println "ddk_url: ${ddk_url}"

// if (ddk_branch == 'master') {
//     musaRuntimePackageUrl = constants.genLatestPackageUrl('MUSA-Runtime', 'master', 'musaRuntime.tar.gz')
//     println "musaRuntimePackageUrl: ${musaRuntimePackageUrl}"
//     musaRuntimePackageOss = constants.urlToOSSPath(musaRuntimePackageUrl)
//     println "musaRuntimePackageOss: ${musaRuntimePackageOss}"
//     oss.cp(musaRuntimePackageOss, "${SAVE_OSS_PATH}")
// }
}

def adjust_ddk(ddk_branch) {
    oss.setUp()
    oss.cp(ddk_url)
    if (ddk_url.endsWith('.rpm')) {
        target_ddk_name = ddk_url.split('/')[-1]
    } else {
        sh '''
            rm *.asan.deb ||:
        '''
        def DEB_VERSION = env.DDK_VERSION ?: sh(script: "dpkg-deb -I *.deb|grep 'Version:'|awk '{print \$2}'", returnStdout: true).trim()
        sh '''
            pwd && ls -l
            dpkg-deb -R *_ddk2.0.deb ddk_2_deb
            rm -rf ddk_2_deb/home ||:
        '''

        if (ddk_branch == 'release_KUAE_2.0_for_PH1_M3D' || ddk_branch == 'release_musa_4.0.0') {
            sh """
                sed -i 's/mtgpu_drm_major=2/compute_only=1/g' ddk_2_deb/DEBIAN/preinst
            """
        }

        sh """
            chmod 755 ddk_2_deb/DEBIAN/preinst
            chmod 755 ddk_2_deb/DEBIAN/postinst
            chmod 755 ddk_2_deb/DEBIAN/prerm
            chmod 755 ddk_2_deb/DEBIAN/postrm
            dpkg-deb --build ddk_2_deb/ musa_${DEB_VERSION}_amd64.deb
        """
        target_ddk_name = "musa_${DEB_VERSION}_amd64.deb"
    }
    oss.cp(target_ddk_name, "${SAVE_OSS_PATH}")
}

def generate_packageurl() {
    // 计算目标URL
    linuxDdkPackageUrl = "${SAVE_URI}/${target_ddk_name}"
    musaToolkitsPackageUrl = "${SAVE_URI}/${target_toolkit_name}"
    musaToolkitsSrcPackageUrl = target_toolkitsrc_name ? "${SAVE_URI}/${target_toolkitsrc_name}" : ''
    mtccPackageUrl = "${SAVE_URI}/others/${target_mtcc_name}"
    mtccSrcPackageUrl = target_mtccsrc_name ? "${SAVE_URI}/others/${target_mtccsrc_name}" : ''

    // 设置Job描述
    currentBuild.description = """
    <p><b>cluster_test:</b> ${env.cluster_test}</p>
    <p><b>linuxDdkBranch:</b> ${env.ddk_branch}</p>
    <p><b>linuxDdkCommitId:</b> ${ddk_commit}</p>
    <p><b>linuxDdkPackageUrl:</b> ${linuxDdkPackageUrl}</p>
    <p><b>musaToolkitsPackageUrl:</b> ${musaToolkitsPackageUrl}</p>
    <p><b>musaToolkitsSrcPackageUrl:</b> ${musaToolkitsSrcPackageUrl}</p>
    <p><b>mtccPackageUrl:</b> ${mtccPackageUrl}</p>
    <p><b>mtccSrcPackageUrl:</b> ${mtccSrcPackageUrl}</p>
    """
}

def build_musa_toolkit(config) {
    def buildTasksHigh = [:]
    def buildTasksMiddle = [:]
    def builds = config.build_toolkits

    for (buildConfig in builds) {
        Map _buildConfig = buildConfig
        def taskName = _buildConfig.name ?: _buildConfig.job  // 获取任务名称
        def defaultParameters = _buildConfig.parameters ?: [:]

        // 合并参数（公共参数 + 任务自有参数）
        def parameters = utils.mergeParameters(defaultParameters, [
            packagePath: "${SAVE_OSS_PATH}/others",
            OSS_SAVE_URL: "${SAVE_OSS_PATH}",
            ddk_url: ddk_url,
            ddk_commit_id: ddk_commit,
            cluster: env.cluster_build,
            podNodeSelector: env.podNodeSelector
        ])

        // 根据任务名称划分到不同的任务组
        if (taskName == 'musa_toolkit') {
            // 只将 musa_toolkit 放入 high 优先级任务组
            buildTasksHigh[taskName] = {
                runPipeline.runJob([
                    job: _buildConfig.job,
                    parameters: parameters
                ])
            }
        } else {
            // 其他任务（musa_toolkit_gcov、mtcc等）放入 middle 优先级任务组
            buildTasksMiddle[taskName] = {
                runPipeline.runJob([
                    job: _buildConfig.job,
                    parameters: parameters
                ])
            }
        }
    }

    stage('Run build tasks in parallel') {
        parallel(
            'Run High Priority Tasks': {
                stage('High build') {
                    parallel buildTasksHigh
                }
            },
            'Run Middle Priority Tasks': {
                stage('Middle build') {
                    catchError(buildResult: 'FAILURE', stageResult: 'FAILURE') {
                        parallel buildTasksMiddle
                    }
                }
            }
        )
    }
}

def copy_mtcc() {
    if (env.ddk_branch == 'master') {
        try {
            def mtccPackagePath = "${SAVE_OSS_PATH}/others/mtcc-x86_64-linux-gnu-ubuntu.tar.gz"
            def unstableMtccPath = "oss/release-ci/computeQA/cuda_compatible/newest/${env.ddk_branch}/unstable/"
            oss.cp(mtccPackagePath, unstableMtccPath)
        } catch (e) {
            println "mtcc cp fail with dir: ${mtccPackagePath}"
        }
    }
}

def build(config) {
    def buildTasks = [:]
    def modules = config.build_modules
    for (moduleConfig in modules) {
        Map _moduleConfig = moduleConfig
        def defaultParameters = _moduleConfig.parameters ?: [:]
        def parameters = utils.mergeParameters(defaultParameters, [
            packagePath: "${SAVE_OSS_PATH}",
            musaToolkitsPackageUrl: musaToolkitsPackageUrl,
            linuxDdkPackageUrl: linuxDdkPackageUrl,
            cluster: env.cluster_build,
            podNodeSelector: env.podNodeSelector
        ])

        buildTasks["${_moduleConfig.name ?: _moduleConfig.job}"] = {
            runPipeline.runJob([
                job: "${_moduleConfig.job}",
                parameters: parameters
            ])
        }
    }

    def others = config.build_others
    for (moduleConfig in others) {
        Map _moduleConfig = moduleConfig
        def defaultParameters = _moduleConfig.parameters ?: [:]
        def parameters = utils.mergeParameters(defaultParameters, [
            packagePath: "${SAVE_OSS_PATH}/others",
            musaToolkitsPackageUrl: musaToolkitsPackageUrl,
            linuxDdkPackageUrl: linuxDdkPackageUrl,
            ddk_commit: ddk_commit,
            cluster: env.cluster_build,
            podNodeSelector: env.podNodeSelector
        ])

        buildTasks["${_moduleConfig.name ?: _moduleConfig.job}"] = {
            runPipeline.runJob([
                job: "${_moduleConfig.job}",
                parameters: parameters
            ])
        }
    }
    parallel buildTasks
}

def deploy(config) {
    def deployTasks = [:]
    def deploys = config.deploys
    for (deployConfig in deploys) {
        Map _deployConfig = deployConfig
        def defaultParameters = _deployConfig.parameters ?: [:]
        def parameters = utils.mergeParameters(defaultParameters, [
            mtmlPackageUrl: env.mtmlPackageUrl,
            linuxDdkPackageUrl: linuxDdkPackageUrl,
            context: env.cluster_test.toLowerCase(),
        ])
        deployTasks["${_deployConfig.name ?: _deployConfig.job}"] = {
            runPipeline.runJob([
                job: "${_deployConfig.job}",
                parameters: parameters
            ])
        }
    }
    println "deployTasks: ${deployTasks}"
    parallel deployTasks
}

/**
 * 根据GPU类型设置相关包URL
 * @param parameters 参数映射
 * @param gpuType GPU类型
 */
def generate_packageurl_from_gpu(gpuType) {
    if (gCover != 'true') {
        switch (gpuType) {
            case 's5000':
                muDNNPackageUrl = "${SAVE_URI}/mudnn.PH1.tar.gz"
                muDNNPerfPackageUrl = "${SAVE_URI}/mudnn.PH1.profiling.tar.gz"
                mcclPackageUrl = "${SAVE_URI}/mccl.PH1.tar.gz"
                break
            case 's4000':
                muDNNPackageUrl = "${SAVE_URI}/mudnn.QY2.tar.gz"
                muDNNPerfPackageUrl = "${SAVE_URI}/mudnn.QY2.profiling.tar.gz"
                mcclPackageUrl = "${SAVE_URI}/mccl.QY2.tar.gz"
                break
            case 's80':
                muDNNPackageUrl = "${SAVE_URI}/mudnn.QY1.tar.gz"
                break
            default:
                println "gpuType: ${gpuType} is invalid!"
        }
    }
    else {
        switch (gpuType) {
            case 's5000':
                muDNNPackageUrl = "${SAVE_URI}/mudnn_cov.PH1.tar.gz"
                muDNNPerfPackageUrl = "${SAVE_URI}/mudnn.PH1.profiling.tar.gz"
                mcclPackageUrl = "${SAVE_URI}/mccl.PH1.tar.gz"  // todo
                break
            case 's4000':
                muDNNPackageUrl = "${SAVE_URI}/mudnn_cov.QY2.tar.gz"
                muDNNPerfPackageUrl = "${SAVE_URI}/mudnn.QY2.profiling.tar.gz"
                mcclPackageUrl = "${SAVE_URI}/mccl.QY2.tar.gz"  // todo
                break
            case 's80':
                muDNNPackageUrl = "${SAVE_URI}/mudnn_cov.QY1.tar.gz"
                break
            default:
                println "gpuType: ${gpuType} is invalid!"
        }
    }
}

def test(tests_group) {
    def testTasks = [:]
    def tests = tests_group
    // 生成统一的基础路径
    def reportOssPath = "${SAVE_OSS_PATH}/driver_toolkits_test"

    for (testConfig in tests) {
        Map _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]

        // 从podNodeSelector中提取GPU_TYPE
        def gpuType = utils.getGpuTypeFromSelector(defaultParameters['podNodeSelector'])
        println "gpuType: ${gpuType}"
        // 设置GPU相关包URL
        generate_packageurl_from_gpu(gpuType)

        def parameters = utils.mergeParameters(defaultParameters, [
            cluster: env.cluster_test,
            ddkBranch: env.ddk_branch,
            ddkCommitId: ddk_commit,
            musaToolkitsPackageUrl: musaToolkitsPackageUrl,
            musaToolkitsSrcPackageUrl: musaToolkitsSrcPackageUrl,
            muDNNPackageUrl: muDNNPackageUrl,
            muDNNPerfPackageUrl: muDNNPerfPackageUrl,
            mcclPackageUrl: mcclPackageUrl,
            gCover: gCover
        ])

        // master分支时，传递mtcc给mtcc_test使用，测试反汇编相关功能；kuae等release分支不测试反汇编相关功能
        if (env.ddk_branch == 'master') {
            parameters = utils.mergeParameters(parameters, [
                mtccPackageUrl: mtccPackageUrl,
                mtccSrcPackageUrl: mtccSrcPackageUrl
            ])
        }

        // 生成最终的报告路径
        parameters['reportOssPath'] = "${reportOssPath}/${gpuType}/"
        println "reportOssPath: ${parameters['reportOssPath']}"
        testTasks["${_testConfig.name ?: _testConfig.job}"] = {
            runPipeline.runJob([
                job: "${_testConfig.job}",
                parameters: parameters
            ])
        }
    }
    parallel testTasks
}

runner.start(env.runChoice) {
    def config = null

    oss.install()
    constants.downloadPackage(env.ciConfig)
    yaml_file = "${env.ciConfig}".split('/')[-1]
    config = commonLib.loadPipelineConfig("${yaml_file}", '')
    def workflow = [:]
    workflow['confirm_ddk'] = [ closure: { confirm_ddk(env.ddk_branch) } ]
    workflow['build_musa_toolkit'] = [ closure: { build_musa_toolkit(config) }]
    workflow['copy mtcc'] = [ closure: { copy_mtcc() }]
    workflow['adjust_ddk'] = [ closure: { adjust_ddk(env.ddk_branch) } ]
    workflow['generate_packageurl'] = [ closure: { generate_packageurl() } ]
    workflow['build_pkgs'] = [ closure: {
        catchError(stageResult: 'FAILURE') {
            build(config)
        }
    }]
    workflow['deploy'] = [ closure: { deploy(config) }]
    workflow['test'] = [ closure: { test(config.tests) }]
    workflow['tests_exception'] = [ closure: { test(config.tests_exception) }]
    runPipeline(workflow)
}
