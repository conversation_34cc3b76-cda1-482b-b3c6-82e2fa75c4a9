@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commitInfo = [:]
today = new Date().format('yyyyMMdd')

def setTag(repo, base, tag) {
    dir("${repo}-${base}") {
        def commitId = gitLib.fetchCode(repo, base, null, [preBuildMerge: false, disableSubmodules: true])
        dir(repo) {
            log.info("setting tag ${tag} for ${repo}(${base}) now...")
            gitLib.setTag(tag)
        }
        return commitId
    }
}

def setRepoTags() {
    def repos = env.repos.split('\n')
    def tasks = [:]
    repos.each { line ->
        // the adding :default is to make sure split result contains at lease 3 contents
        def (repo, base, postfix) = "${line}:default".split(':')
        postfix = postfix == 'default' ? base : postfix
        def tag = "${today}_${postfix}"
        tasks["${repo} @${base}"] = {
            def commitId = null
            catchError(stageResult: 'FAILURE', buildResult: 'FAILURE') { commitId = gitLib.createGitlabTagByApi(repo, tag, base) }
            currentBuild.description += "${repo}: ${postfix}@${commitId} - ${tag}<br />"
            if (!(repo in commitInfo)) { commitInfo[repo] = [:] }
            commitInfo[repo][postfix] = commitId
        }
    }
    parallel tasks
}

def uploadCommitInfo() {
    def filename = "${today}.txt"
    def ossPath = 'oss/release-ci/repo_tags/'
    try {
        oss.install()
        oss.cp("${ossPath}/${filename}", '.')
        def currentInfo = readJSON file: filename
        def latestCommitInfo = new common().mergeMap([currentInfo, commitInfo])
        writeJSON file: filename, json: latestCommitInfo
    } catch (exc) {
        writeJSON file: filename, json: commitInfo
    }
    oss.cp(filename, "${ossPath}")
}

runner.start(env.runChoice) {
    def workflow = [
        'main': [closure: { setRepoTags() }],
    ]
    if (env.updateCommitInfoFile == 'true') {
        workflow['upload commit info'] = [closure: { uploadCommitInfo() }]
    }

    runPipeline(workflow)
}
