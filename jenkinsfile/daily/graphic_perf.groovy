/* groovylint-disable-next-line UnusedVariable */
@Library('swqa-ci') _

properties([
    parameters([
        string(name: 'linuxDdkPackageUrl', defaultValue: '', description: ''),
        string(name: 'nodeNames', defaultValue: '', description: 'split by ,'),
        choice(name: 'runChoice', choices: ['node', 'pod'], description: ''),
        string(name: 'nodeLabel', defaultValue: 'Linux_jump', description: ''),
    ])
])

runner.start(env.runChoice) {
    def workflow = [:]
    env.nodeNames.split(',').each { nodeName ->
        workflow[nodeName] = [
            job: 'test.perf',
            parameters: [
                linuxDdkPackageUrl: env.linuxDdkPackageUrl,
                nodeLabel: nodeName,
                async: true
            ]
        ]
    }
    runPipeline(workflow)
}
