.PHONY: update-kuae20 update-master update-ci-%

update-master: ## update master branch yaml to oss
	$(MAKE) update-ci-master

update-kuae20: ## update release_KUAE_2.0_for_PH1_M3D branch yaml to oss
	$(MAKE) update-ci-release_KUAE_2.0_for_PH1_M3D

update-ci-%:
	@echo "update branch $*"
	@mc cp ./$*/daily.musa_sdk.yaml oss/release-ci/computeQA/cuda_compatible/CI/$*

help: ## Show this help.
	@awk 'BEGIN {FS = ":.*##"; printf "\nUsage:\n  make \033[36m\033[0m\n"} /^[a-zA-Z_-]+:.*?##/ { printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2 } /^##@/ { printf "\n\033[1m%s\033[0m\n", substr($$0, 5) } ' $(MAKEFILE_LIST)
