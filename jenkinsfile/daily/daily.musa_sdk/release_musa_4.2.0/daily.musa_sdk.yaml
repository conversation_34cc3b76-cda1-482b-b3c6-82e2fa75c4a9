build_toolkits:
  - job: "build.musa_toolkit"
    name: "musa_toolkit"
    parameters:
      musa_toolkit_branch: "release_musa_4.2.0"
      mtcc_branch: "release_musa_4.2.0"
      musify_branch: "release_musa_4.2.0"
      mublas_branch: "release_musa_4.2.0"
      mufft_branch: "release_musa_4.2.0"
      mupp_branch: "release_musa_4.2.0"
      murand_branch: "release_musa_4.2.0"
      musparse_branch: "release_musa_4.2.0"
      musolver_branch: "release_musa_4.2.0"
      mupti_branch: "release_musa_4.2.0"
      mublaslt_branch: "release_musa_4.2.0"
      compileTargetArch: "x86_64"
      makecmds_musatoolkit: "--build_all --build_archs=21,22,31 --build_mupti --ddk_backend=m3d -j128"
      rename_pkg_musa_toolkits: "musa_toolkits_rc4.2.0.tar.gz"
  - job: "build.mtcc"
    name: "mtcc"
    parameters:
      branch: "release_musa_4.2.0"
      compileArgs: "--build_type release"
      packageName: "mtcc-x86_64-linux-gnu-ubuntu.tar.gz"
build_modules:
  - job: "build.mudnn"
    name: "mudnn_ph1"
    parameters:
      branch: "release_musa_4.2.0"
      packageName: "mudnn_rc3.0.0.PH1"
      cmd: "-m mp_31 -s"
      podResources: "requests=cpu=60;requests=memory=64Gi;limits=cpu=60;limits=memory=64Gi;"
  - job: "build.mudnn"
    name: "mudnn_qy2"
    parameters:
      branch: "release_musa_4.2.0"
      packageName: "mudnn_rc3.0.0.QY2"
      cmd: "-m mp_22 -s"
      podResources: "requests=cpu=40;requests=memory=64Gi;limits=cpu=40;limits=memory=64Gi;"
  - job: "build.mudnn"
    name: "mudnn_qy1"
    parameters:
      branch: "release_musa_4.2.0"
      packageName: "mudnn_rc3.0.0.QY1"
      cmd: "-m mp_21 -s"
      podResources: "requests=cpu=40;requests=memory=64Gi;limits=cpu=40;limits=memory=64Gi;"
  - job: "build.mccl"
    name: "mccl_qy2"
    parameters:
      mcclBranch: "release_musa_4.2.0_s4000"
      packageName: "mccl_rc2.0.0.tar.gz"
      compileArgs: 'GENCODE="--cuda-gpu-arch=mp_22"'
  - job: "build.mccl"
    name: "mccl_ph1"
    parameters:
      mcclBranch: "release_musa_4.2.0_s5000"
      packageName: "mccl_rc2.0.0.PH1.tar.gz"
      compileArgs: 'GENCODE="--cuda-gpu-arch=mp_31"'
  - job: "build.triton_musa"
    name: "triton_musa"
    parameters:
      branch: "release_musa_4.2.0"
      packageName: "triton_rc1.3.0.tar.gz"
      podResources: "requests=cpu=20;requests=memory=40Gi;limits=cpu=20;limits=memory=64Gi"
build_others:
  - job: "build.mudnn"
    name: "mudnn_ph1_perf"
    parameters:
      branch: "release_musa_4.2.0"
      packageName: "mudnn_rc3.0.0.PH1_profiling"
      cmd: "-m mp_31 -s -p"
      podResources: "requests=cpu=60;requests=memory=64Gi;limits=cpu=60;limits=memory=64Gi;"
  - job: "build.mudnn"
    name: "mudnn_qy2_perf"
    parameters:
      branch: "release_musa_4.2.0"
      packageName: "mudnn_rc3.0.0_profiling"
      cmd: "-m mp_22 -s -p"
      podResources: "requests=cpu=60;requests=memory=64Gi;limits=cpu=60;limits=memory=64Gi;"
