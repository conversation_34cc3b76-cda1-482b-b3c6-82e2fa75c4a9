build_toolkits:
  - job: "build.musa_toolkit"
    name: "musa_toolkit"
    parameters:
      musa_toolkit_branch: "release_KUAE_2.0_for_PH1_M3D"
      mtcc_branch: "release_KUAE_2.0_for_PH1_M3D"
      musify_branch: "release_KUAE_2.0_for_PH1_M3D"
      mublas_branch: "release_KUAE_2.0_for_PH1_M3D"
      mufft_branch: "release_KUAE_2.0_for_PH1_M3D"
      mupp_branch: "release_KUAE_2.0_for_PH1_M3D"
      murand_branch: "release_KUAE_2.0_for_PH1_M3D"
      mtjpeg_branch: "master"
      musparse_branch: "release_KUAE_2.0_for_PH1_M3D"
      musolver_branch: "release_KUAE_2.0_for_PH1_M3D"
      mupti_branch: "release_KUAE_2.0_for_PH1_M3D"
      compileTargetArch: "x86_64"
      makecmds_musatoolkit: "--build_all --build_archs=21,22,31 --build_mupti --ddk_backend=m3d -j128"
      podResources: "requests=cpu=16;requests=memory=100Gi;limits=cpu=64;limits=memory=200Gi;"
      containerImage: "sh-harbor.mthreads.com/qa/musa_compile:v7"
      rename_pkg_musa_toolkits: "musa_toolkits_install_full.tar.gz"
  - job: "build.mtcc"
    name: "mtcc"
    parameters:
      branch: "release_KUAE_2.0_for_PH1_M3D"
      compileArgs: "--build_type release"
      compileParallel: "48"
      rename_pkg: "mtcc-x86_64-linux-gnu-ubuntu.tar.gz"
      podResources: "requests=cpu=16;requests=memory=100Gi;limits=cpu=64;limits=memory=200Gi;"
      containerImage: "sh-harbor.mthreads.com/qa/musa_compile:v7"
build_modules:
  - job: "build.mudnn"
    name: "mudnn_ph1"
    parameters:
      branch: "release_KUAE_2.0_for_PH1_M3D"
      packageName: "mudnn_rc2.9.0.PH1"
      cmd: "-m mp_31 -s"
      podResources: "requests=cpu=60;requests=memory=64Gi;limits=cpu=60;limits=memory=64Gi;"
  - job: "build.mudnn"
    name: "mudnn_qy2"
    parameters:
      branch: "release_KUAE_2.0_for_PH1_M3D"
      packageName: "mudnn_rc2.9.0"
      cmd: "-m mp_22 -s"
      podResources: "requests=cpu=40;requests=memory=64Gi;limits=cpu=40;limits=memory=64Gi;"
  - job: "build.MUPTI"
    name: "MUPTI"
    parameters:
      branch: "release_KUAE_2.0_for_PH1_M3D"
      buildBranch: "release_KUAE_2.0_for_PH1_M3D"
      packageName: "MUPTI.deb"
      compileArgs: "-DCPACK_PACKAGING_INSTALL_PREFIX=/usr/local/musa -DCMAKE_BUILD_TYPE=Release"
      podResources: "requests=cpu=20;requests=memory=32Gi;limits=cpu=20;limits=memory=32Gi;"
  - job: "build.mccl"
    name: "mccl_qy2"
    parameters:
      mcclBranch: "release_KUAE_2.0_for_S4000_M3D"
      packageName: "mccl_rc1.9.0.tar.gz"
      compileArgs: 'GENCODE="--cuda-gpu-arch=mp_22"'
  - job: "build.mccl"
    name: "mccl_ph1"
    parameters:
      mcclBranch: "release_KUAE_2.0_for_PH1_M3D"
      packageName: "mccl_rc1.9.0.PH1.tar.gz"
      compileArgs: 'GENCODE="--cuda-gpu-arch=mp_31"'
  - job: "build.triton_musa"
    name: "triton_musa"
    parameters:
      branch: "release_KUAE_2.0_for_PH1_M3D"
      packageName: "triton_rc1.2.0.tar.gz"
      podResources: "requests=cpu=20;requests=memory=40Gi;limits=cpu=20;limits=memory=64Gi"
build_others:
  # - job: "build.alphacore"
  #   name: "alphacore"
  #   parameters:
  #     muAlgPackageUrl: "https://oss.mthreads.com/release-ci/computeQA/cuda_compatible/newest/release_KUAE_2.0_for_PH1_M3D/muAlg.tar"
  #     muThrustPackageUrl: "https://oss.mthreads.com/release-ci/computeQA/cuda_compatible/newest/release_KUAE_2.0_for_PH1_M3D/muThrust.tar"
  - job: "build.mudnn"
    name: "mudnn_ph1_perf"
    parameters:
      branch: "release_KUAE_2.0_for_PH1_M3D"
      packageName: "mudnn_rc2.9.0.PH1_profiling"
      cmd: "-m mp_31 -s -p"
      podResources: "requests=cpu=60;requests=memory=64Gi;limits=cpu=60;limits=memory=64Gi;"
  - job: "build.mudnn"
    name: "mudnn_qy2_perf"
    parameters:
      branch: "release_KUAE_2.0_for_PH1_M3D"
      packageName: "mudnn_rc2.9.0_profiling"
      cmd: "-m mp_22 -s -p"
      podResources: "requests=cpu=60;requests=memory=64Gi;limits=cpu=60;limits=memory=64Gi;"
deploys:
  - job: "test.operatorinstall"
    name: "deployFarm by operator"

tests:
  - job: "daily.musa_cts"
    name: "musa_cts.s5000"
    parameters:
      musa_cts_branch: "release_KUAE_2.0_for_PH1_M3D"
      envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4"
      compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
      test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ -k 'not skip' -m 'not XORG and not MUSA_EXCEPTION_TEST and not MULTI_DEV'"
      gpuArch: "mp_31"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "daily.musa_cts"
    name: "musa_ptsz.s5000"
    parameters:
      musa_cts_branch: "release_KUAE_2.0_for_PH1_M3D"
      envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4"
      work_dir: "pytest_ptsz"
      test_cmd: "pytest -v . -k 'not skip'"
      gpuArch: "mp_31"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "daily.musa_cts"
    name: "musa_multi_dev.s5000"
    parameters:
      musa_cts_branch: "release_KUAE_2.0_for_PH1_M3D"
      envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4"
      compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON"
      test_cmd: "pytest . -v -m 'MULTI_DEV'"
      gpuArch: "mp_31"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
  - job: "daily.musa_cts"
    name: "musa_cts.s4000"
    parameters:
      musa_cts_branch: "release_KUAE_2.0_for_PH1_M3D"
      envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4"
      compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
      test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ -k 'not skip' -m 'not XORG and not MUSA_EXCEPTION_TEST and not MULTI_DEV'"
      gpuArch: "mp_22"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "daily.musa_cts"
    name: "musa_ptsz.s4000"
    parameters:
      musa_cts_branch: "release_KUAE_2.0_for_PH1_M3D"
      envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4"
      work_dir: "pytest_ptsz"
      test_cmd: "pytest -v . -k 'not skip'"
      gpuArch: "mp_22"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "daily.musa_cts"
    name: "musa_multi_dev.s4000"
    parameters:
      musa_cts_branch: "release_KUAE_2.0_for_PH1_M3D"
      envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4"
      compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON"
      test_cmd: "pytest . -v -m 'MULTI_DEV'"
      gpuArch: "mp_22"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
  - job: "daily.musa_cts"
    name: "musa_cts.s80"
    parameters:
      musa_cts_branch: "release_KUAE_2.0_for_PH1_M3D"
      envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4"
      compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
      test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ -k 'not skip' -m 'not XORG and not MUSA_EXCEPTION_TEST and not MULTI_DEV'"
      gpuArch: "mp_21"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s80"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "daily.musa_cts"
    name: "musa_ptsz.s80"
    parameters:
      musa_cts_branch: "release_KUAE_2.0_for_PH1_M3D"
      envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4"
      work_dir: "pytest_ptsz"
      test_cmd: "pytest -v . -k 'not skip'"
      gpuArch: "mp_21"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s80"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mathLibs_perf"
    name: "test.mathLibs_perf.s5000"
    parameters:
      testType: "benchmark"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TEST_TYPE=benchmark;In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mathLibs_perf"
    name: "test.mathLibs_perf.s4000"
    parameters:
      testType: "benchmark"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TEST_TYPE=benchmark;In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.mathLibs_perf"
  #   name: "test.mathLibs_perf.s3000"
  #   parameters:
  #     testType: "benchmark"
  #     containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TEST_TYPE=benchmark;In=GPU_TYPE=s3000"
  #     podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.muBLAS_cts"
    name: "test.muBLAS_cts.s5000"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/mublas_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.muBLAS_cts"
    name: "test.muBLAS_cts.s4000"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/mublas_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.muBLAS_cts"
  #   name: "test.muBLAS_cts.s3000"
  #   parameters:
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/mublas_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s3000"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.muFFT_cts"
    name: "test.muFFT_cts.s5000"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
  - job: "test.muFFT_cts"
    name: "test.muFFT_cts.s4000"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
  # - job: "test.muFFT_cts"
  #   name: "test.muFFT_cts.s3000"
  #   parameters:
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s3000"
  #     podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
  - job: "test.muRAND_cts"
    name: "test.muRAND_cts.s5000"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/murand_test-ubuntu-22-04:v2"
      runChoice: "pod"
      compileArgs: "-DMUSA_ARCHS=31"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=50Gi;limits=ephemeral-storage=50Gi;requests=cpu=10;limits=cpu=10;requests=memory=27Gi;limits=memory=27Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.muRAND_cts"
    name: "test.muRAND_cts.s4000"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/murand_test-ubuntu-22-04:v2"
      runChoice: "pod"
      compileArgs: "-DMUSA_ARCHS=22"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=50Gi;limits=ephemeral-storage=50Gi;requests=cpu=10;limits=cpu=10;requests=memory=27Gi;limits=memory=27Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.muRAND_cts"
  #   name: "test.muRAND_cts.s3000"
  #   parameters:
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/murand_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     compileArgs: "-DMUSA_ARCHS=21"
  #     podNodeSelector: "In=GPU_TYPE=s3000"
  #     podResources: "requests=ephemeral-storage=50Gi;limits=ephemeral-storage=50Gi;requests=cpu=10;limits=cpu=10;requests=memory=27Gi;limits=memory=27Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mtcc"
    name: "test.mtcc.s5000"
    parameters:
      testType: "daily"
      testArgs: "--device=ph1 --tag=daily --disable_asm=true"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mtcc"
    name: "test.mtcc.s4000"
    parameters:
      testType: "daily"
      testArgs: "--device=quyuan2 --tag=daily --disable_asm=true"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.mtcc"
  #   name: "test.mtcc.s3000"
  #   parameters:
  #     testType: "daily"
  #     testArgs: "--device=quyuan1 --tag=daily --disable_asm=true"
  #     containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s3000"
  #     podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "daily.musa_cts"
    name: "daily.musa_cts.mtcc.s5000"
    parameters:
      musa_cts_branch: "release_KUAE_2.0_for_PH1_M3D_mtcc"
      envExport: "export TEST_TYPE=dailyM3d"
      compileArgs: ""
      test_cmd: "pytest -v -m 'musa_mtcc'"
      gpuArch: "mp_31"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "daily.musa_cts"
    name: "daily.musa_cts.mtcc.s4000"
    parameters:
      musa_cts_branch: "release_KUAE_2.0_for_PH1_M3D_mtcc"
      envExport: "export TEST_TYPE=dailyM3d"
      compileArgs: ""
      test_cmd: "pytest -v -m 'musa_mtcc'"
      gpuArch: "mp_22"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "daily.musa_cts"
  #   name: "daily.musa_cts.mtcc.s3000"
  #   parameters:
  #     musa_cts_branch: "release_KUAE_2.0_for_PH1_M3D_mtcc"
  #     envExport: "export TEST_TYPE=dailyM3d"
  #     compileArgs: ""
  #     test_cmd: "pytest -v -m 'musa_mtcc'"
  #     gpuArch: "mp_21"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s3000"
  #     podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mudnn_cts"
    name: "test.mudnn_cts.qy2"
    parameters:
        test_type: "daily"
        test_mark: "mudnn"
        packageName: "mudnn_dev2.8.0.tar.gz"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s4000"
        podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mudnn_cts"
    name: "test.mudnn_cts.ph1"
    parameters:
        test_type: "daily"
        test_mark: "mudnn"
        packageName: "mudnn_dev2.8.0.PH1.tar.gz"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s5000"
        podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mudnn_benchmark"
    name: "test.mudnn_benchmark.ph1"
    parameters:
        test_type: "benchmark"
        test_mark: "m3d_mudnn_benchmark"
        packageName: "mudnn_rc2.9.0.PH1_profiling.tar.gz"
        runChoice: "node"
        nodeLabel: "5000_dnn_perf"
  - job: "test.mudnn_benchmark"
    name: "test.mudnn_benchmark.qy2"
    parameters:
        test_type: "benchmark"
        test_mark: "m3d_mudnn_benchmark"
        packageName: "mudnn_rc2.9.0_profiling.tar.gz"
        runChoice: "node"
        nodeLabel: "4000_mutlass_perf"
  - job: "test.mutlass"
    name: "test.mutlass.qy2"
    parameters:
        test_type: "daily"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s4000"
        podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mutlass"
    name: "test.mutlass.ph1"
    parameters:
        test_type: "daily"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s5000"
        podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mutlass_benchmark"
    name: "test.mutlass_benchmark.qy2"
    parameters:
        mutlass_branch: "develop"
        runChoice: "node"
        nodeLabel: "4000_mutlass_perf"
  - job: "CI_MTML_TEST"
    name: "test_mtml_ph1"
    parameters:
      mtmlBranch: "release_2.0"
      gpuType: "S5000"
      testMtml: "true"
      testGmi: "false"
      containerImage: "sh-harbor.mthreads.com/sdk/management:v11"
      podResources: "requests=cpu=6;requests=memory=8Gi;limits=cpu=10;limits=memory=8Gi;limits=mthreads.com/gpu=1;"
      podNodeSelector: "In=CPU_PLATFORM=intel;In=GPU_TYPE=s5000;"
      runChoice: "pod"
      exclude_cases: "DeviceGetPciInfoTest.baseGetPci,VpuGetDecoderSessionMetricsTest.getSessionMetricsWithFfmpeg,
                      VpuGetUtilizationTest.withFfmpegDecoding,DeviceGetPowerUsageTest.*,MemoryGetVendorTest.*,
                      GpuGetMaxClockTest.baseGetMaxClock,DeviceGetSerialNumberTest*,DeviceResetTest*,DeviceGetNameTest.validData,
                      DeviceInitByPciSbdfTest*"
  - job: "CI_msys_cli_test"
    name: "test_msys_ph1"
    parameters:
      msysBranch: "release-1.2"
      gpuType: "S5000"
      testType: "compute"
      benchmark: "--compute"
      msys_cases: '-k "not graphics"'
      containerImage: "sh-harbor.mthreads.com/qa/musa_test-ubuntu-22-04:v2"
      podResources: "requests=cpu=6;requests=memory=8Gi;limits=cpu=10;limits=memory=8Gi;limits=mthreads.com/gpu=1;"
      podNodeSelector: "In=CPU_PLATFORM=intel;In=GPU_TYPE=s5000;"
      runChoice: "pod"
  # - job: "test.alphacore_hunit"
  #   name: "test_alphacore_hunit_s4000"
  #   parameters:
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_CHIP=qy1"
  #     podResources: "requests=cpu=10;requests=memory=20Gi;limits=cpu=10;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.alphacore_UTest"
  #   name: "test_alphacore_UTest_s4000"
  #   parameters:
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_CHIP=qy1"
  #     podResources: "requests=cpu=10;requests=memory=20Gi;limits=cpu=10;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "daily.musa_cts"
    name: "musa_model_test_s5000"
    parameters:
      musa_cts_branch: "release_KUAE_2.0_for_PH1_M3D"
      envExport: "export TEST_TYPE=dailyM3d"
      compileArgs: "-DENABLE_MODEL_TEST=on"
      test_cmd: "pytest -v -m 'MODEL_TEST'"
      gpuArch: "mp_31"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "daily.musa_cts"
  #   name: "master_toolkit_compatible_s5000"
  #   parameters:
  #     musa_cts_branch: "release_KUAE_2.0_for_PH1_M3D"
  #     envExport: "export TEST_TYPE=dailyM3d"
  #     test_cmd: "pytest -v -m 'MUSA_API_TEST or MUSA_FUNCTIONAL_TEST'"
  #     musaToolkitsPackageUrl: "https://oss.mthreads.com/release-ci/computeQA/cuda_compatible/newest/master/musa_toolkits_install_full.tar.gz"
  #     gpuArch: "mp_31"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s5000"
  #     podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.musa_perf_daily"
    name: "musa_perf_KUAE2.0_s4000"
    parameters:
      runChoice: "pod"
      testMark: "musa_benchmarks_perf_m3d"
      ddkBranch: "release_KUAE_2.0_for_PH1_M3D"
      musa_benchmark_cts_branch: "m3d_master"
      testType: "daily"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=cpu=10;requests=memory=20Gi;requests=mthreads.com/gpu=2;limits=cpu=10;limits=memory=62Gi;limits=mthreads.com/gpu=2;"
tests_exception:
  - job: "daily.musa_cts"
    name: "musa_exception_test_s5000"
    parameters:
      musa_cts_branch: "release_KUAE_2.0_for_PH1_M3D"
      envExport: "export TEST_TYPE=dailyM3d"
      test_cmd: "pytest -v . -m 'MUSA_EXCEPTION_TEST'"
      gpuArch: "mp_31"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
