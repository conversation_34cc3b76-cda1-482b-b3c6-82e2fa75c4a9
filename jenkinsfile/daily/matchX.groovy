@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

runner.start(env.runChoice) {
    def workflow = [:]
    def supportedTests = [
        'musa_cts': [
            name: 'test.muBLAS_cts',
            parameters: [exports: 'export TEST_TYPE=daily']
        ],
        'mupp_cts': [
            name: 'test.muPP_cts',
            parameters: []
        ]
    ]
    supportedTests.each { repo, job ->
        if (env["${repo}_branch"]) {
            def parameters = [linuxDdkPackageUrl: env.linuxDdkPackageUrl] + job.parameters
            workflow[repo] = [job: job.name, parameters: parameters]
        }
    }
    runPipeline(workflow)
}
