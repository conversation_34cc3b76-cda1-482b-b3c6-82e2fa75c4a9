@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()
env.repo = 'linux-ddk'
linuxDdkBranch = env.useDatetag == 'true' ? constants.genDateTag(env.dateTag) + '_' + env.linuxDdkBranch : env.linuxDdkBranch

def build(config) {
    def buildTasks = [:]
    def builds = config.builds

    for (buildConfig in builds) {
        def _buildConfig = buildConfig
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            repo: env.repo,
            branch: linuxDdkBranch,
            ddkBranch: linuxDdkBranch,
            commitId: env.linuxDdkCommitId,
            ddkCommitId: env.linuxDdkCommitId,
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        buildTasks["${_buildConfig.name ?: _buildConfig.job}"] = {
            runPipeline.runJob([
                job: "${_buildConfig.job}",
                parameters: parameters
            ])
        }
    }
    parallel buildTasks
}

// use POD instead
runner.start(env.runChoice) {
    def config = null
    env.linuxDdkCommitId = constants.formatCommitID(gitLib.getGitlabRepocommitByApi(env.repo, linuxDdkBranch))
    currentBuild.description += "linux-ddk branch: [branch:${linuxDdkBranch},commitID:${env.linuxDdkCommitId}]<br>"
    // get config from daily/repo.yaml
    config = commonLib.loadConfig('daily/linux-ddk.yaml', [
        date: constants.genDateTag(env.dateTag)
    ])
    def workflow = [:]
    workflow['build'] = [ closure: { build(config) }, setGitlabStatus: true ]

    def tests = config.tests ?: []
    for (testConfig in tests) {
        def _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def name = _testConfig.name ?: _testConfig.job
        // def linuxDdkPackageUrl = constants.genPackageUrl(env.repo, env.linuxDdkBranch, env.linuxDdkCommitId, 'ddk2.0.deb')
        def linuxDdkPackageUrl = constants.genPackageUrl('linux-ddk', linuxDdkBranch, env.linuxDdkCommitId, 'ddk2.0.deb')
        def parameters = [
            musaRuntimePackageUrl : defaultParameters.musaRuntimePackageName ? constants.genPackageUrl(repo, env.linuxDdkBranch, env.linuxDdkCommitId, defaultParameters.musaRuntimePackageName) : '',
            linuxDdkPackageUrl: linuxDdkPackageUrl,
            triggerInfo: env.triggerInfo,
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        workflow["${name}"] = [
            job: _testConfig.job,
            parameters: parameters,
            setGitlabStatus: true,
            async: true
        ]
    }

    runPipeline(workflow)
}
