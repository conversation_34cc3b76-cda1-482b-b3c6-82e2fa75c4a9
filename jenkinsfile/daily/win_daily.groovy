@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

finalWinBranch = env.useDatetag == 'true' ? constants.genDateTag(env.dateTag) + '_' + env.winBranch : env.winBranch

def toStringParams(params) {
    def result = [:]
    params.each { k, v ->
        result[k] = v instanceof String ? v : groovy.json.JsonOutput.toJson(v)
    }
    return result
}

def findDepName(builds, dependsOn) {
    def matches = builds.findAll {
        (it.job == dependsOn || it.name == dependsOn) && it.job?.endsWith('.win')
    }

    def daily = matches.find { it.trigger?.contains('DAILY') }
    if (daily) { return daily.name ?: dependsOn }

    return matches ? (matches[0].name ?: dependsOn) : dependsOn
}

def isDependencySuccess(depName, futures, started) {
    return !depName || (started[depName] && (!futures[depName] || (futures[depName]?.result?.toString() == 'SUCCESS')))
}

def getWddmDriverInfo(Map config) {
    def latestWddmDriverUrl = config.latestWddmDriver
    if (!latestWddmDriverUrl) {
        echo 'No latestWddmDriver in config, skip getWddmDriverInfo'
        return [:]
    }

    def wddmPkgBaseUrl = sh(script: "curl --insecure ${latestWddmDriverUrl}", returnStdout: true).trim()
    def tagName = constants.genDateTag(env.dateTag) + '_' + env.wddmBranch
    echo "getWddmDriverInfo: tagName=${tagName}, useDatetag=${env.useDatetag}, wddmBranch=${env.wddmBranch}, env.wddmCommitId=${env.wddmCommitId}"
    def wddmCommitId = env.useDatetag == 'true' ? new git().getTagCommit('wddm', tagName) : env.wddmCommitId
    echo "Found WDDM driver - URL: ${wddmPkgBaseUrl}, commit: ${wddmCommitId}"

    return [
        url: wddmPkgBaseUrl,
        commitId: wddmCommitId
    ]
}

def build(config) {
    def builds = config.builds
    def buildMap = [:]
    builds.each { buildConfig ->
        def name = buildConfig.name ?: buildConfig.job
        buildMap[name] = buildConfig
    }

    def jobFutures = [:]
    def buildTasks = [:]
    buildMap.each { name, buildConfig ->
        if (!buildConfig.trigger?.contains('DAILY')) { return }

        jobFutures[name] = null
        def params = [
            repo: env.repo,
            branch: finalWinBranch,
            commitId: env.winCommitId,
            triggerInfo: env.triggerInfo,
            testLabel: name
        ]

        if (env.repo != 'wddm') {
            try {
                wddmInfo = getWddmDriverInfo(config)
                if (wddmInfo) {
                    params.wddmDriverUrl = wddmInfo.url
                    params.wddmCommitId = wddmInfo.commitId
                }
            } catch (e) {
                echo "Warning: Failed to get WDDM driver info: ${e.message}"
            }
        }

        params.putAll(toStringParams(buildConfig.parameters ?: [:]))
        if (buildConfig.driverType) { params.driverType = buildConfig.driverType }
        if (buildConfig.outputDir) { params.outputDir  = buildConfig.outputDir }

        buildTasks[name] = {
            if (buildConfig.dependsOn) {
                def depName = findDepName(builds, buildConfig.dependsOn)
                if (depName && jobFutures[depName]) {
                    def depResult = jobFutures[depName]
                    if (depResult?.result && depResult.result.toString() != 'SUCCESS') {
                        error("[win-mr] Dependency job ${depName} failed, aborting ${name}")
                    }
                }
            }
            echo "[win-mr] running job: ${name}"
            jobFutures[name] = runPipeline.runJob([
                job: buildConfig.job,
                parameters: params
            ])
        }
    }

    def started = [:], futures = [:]

    def canRunJob = { name ->
        def buildConfig = buildMap[name]
        def depName = buildConfig?.dependsOn ? findDepName(builds, buildConfig.dependsOn) : null
        if (!depName) { return true }
        if (!buildTasks.containsKey(depName)) { return true } // dependency not defined, skip
        return started[depName] && (!futures[depName] || (futures[depName]?.result?.toString() == 'SUCCESS'))
    }

    while (started.size() < buildTasks.size()) {
        def progress = false
        def parallelTasks = [:]
        buildTasks.each { name, task ->
            if (!started[name] && canRunJob(name)) {
                parallelTasks[name] = {
                    started[name] = true
                    futures[name] = task()
                }
                progress = true
            }
        }
        if (parallelTasks) {
            parallel parallelTasks
        }
        if (!progress) {
            buildTasks.each { name, _ ->
                if (!started[name]) {
                    def buildConfig = buildMap[name]
                    def depName = buildConfig?.dependsOn ? findDepName(builds, buildConfig.dependsOn) : null
                    def depDefined = depName ? buildTasks.containsKey(depName) : true
                    def extraMsg = (!depDefined && depName) ? ' [dependency not defined, automatically skipped]' : ''
                    echo "[win-mr] Deadlock: '${name}' waiting for '${depName}', started[depName]=${started[depName]}, futures[depName]=${futures[depName]}${extraMsg}"
                }
            }
            error('[win-mr] Deadlock detected in job dependency graph!')
        }
    }
}

def updategfxc() {
    gitLib.fetchCode(env.libgfxcPakcageRepo, env.libgfxcPakcagebBranch, '', [preBuildMerge: false, lfs: true, shallow: false, updateBuildDescription: true])
    def musaCompilerSharedPackageName = 'musa_compiler_shared_win.tar.gz'
    def musaCompilerSharedUrl = constants.genPackageUrl(env.repo, finalWinBranch, env.winCommitId, musaCompilerSharedPackageName)

    dir(env.WORKSPACE) {
        new common().retryByRandomTime({
            sh "wget -q --no-check-certificate -O ${musaCompilerSharedPackageName} ${musaCompilerSharedUrl}"
            sh "ls -lh ${musaCompilerSharedPackageName} || true"
            sh "tar -xzf ${musaCompilerSharedPackageName} -C ${env.libgfxcPakcageRepo}"
        }, 20)
    }

    credentials.runWithCredential('SSH_GITLAB') {
        dir(env.libgfxcPakcageRepo) {
            sh """
                git status
                git lfs track "*.tar.gz" || :
                git add . || :
                git commit -m "daily build mtcc ${env.winCommitId} wddm ${wddmInfo?.commitId ?: ''}" || :
            """
            new common().retryByRandomTime({
                sh """
                git lfs push origin ${env.libgfxcPakcagebBranch} || :
                git push  origin ${env.libgfxcPakcagebBranch} || :
                """
            }, 20)
        }
    }
}

def updateLatestTxt() {
    constants.updateLatestTxt(env.repo, finalWinBranch, env.winCommitId)
}

runner.start(env.runChoice) {
    def directstreamPackageName = 'directstreamTest.tar.gz'
    def m3dPackageName = 'm3dTest.tar.gz'
    def ffmpegPackageName = 'windows_ffmpeg.tar.gz'
    def sdkgoogletestageName = 'sdkGoogletest'

    def config = null
    env.winCommitId = gitLib.fetchCode(
        env.repo,
        finalWinBranch,
        env.winCommitId,
        [
            disableSubmodules: true
        ]
    )

    dir(env.repo) {
        config = commonLib.loadPipelineConfig('.ciConfig.yaml', "${env.repo}/${finalWinBranch}.yaml")
    }

    def workflow = [:]
    workflow['build'] = [
        closure: { build(config) },
        setGitlabStatus: false
    ]

    if (config.tests) {
        def usedNames = [] as Set
        def wddmInfo = null
        if (env.repo != 'wddm') {
            try {
                wddmInfo = getWddmDriverInfo(config)
            } catch (e) {
                echo "Warning: Failed to get WDDM driver info: ${e.message}"
            }
        }
        config.tests.each { testConfig ->
            if (testConfig.job?.endsWith('.win')) {
                if (!testConfig.trigger?.contains('DAILY')) { return }

                def params = [triggerInfo: env.triggerInfo]
                params.putAll(toStringParams(testConfig.parameters ?: [:]))

                if (testConfig.driverType) {
                    // Common parameters for test jobs
                    params.testName = params.testName ?: (testConfig.job?.tokenize('.')?.getAt(1) ? "${testConfig.job.tokenize('.')[1]}_test" : '')
                    params.driverType = testConfig.driverType
                    params.statusName = testConfig.name

                    // Package names
                    def driverPackageName = "${env.repo}_${testConfig.driverType}.tar.gz"

                    // Set only once per repo
                    switch (env.repo) {
                        case 'FFmpeg':
                            params.ffmpgeUrl = constants.genPackageUrl(env.repo, finalWinBranch, env.winCommitId, ffmpegPackageName)
                            break
                        case 'mt-management':
                            params.sdkGoogle_test_url = constants.genPackageUrl(env.repo, finalWinBranch, env.winCommitId, sdkgoogletestageName)
                            break
                        default:
                            params.driverUrl = constants.genPackageUrl(env.repo, finalWinBranch, env.winCommitId, driverPackageName)
                    }

                    // directstream_test_url
                    if (env.repo == 'DirectStream') {
                        params.directstream_test_url = constants.genPackageUrl(env.repo, finalWinBranch, env.winCommitId, directstreamPackageName)
                    } else {
                        def directstreamLatestUrl = config.directstream_latest ?: 'https://oss.mthreads.com/sw-build/DirectStream/develop/latest.txt'
                        def directstreamBase = sh(script: "curl --insecure ${directstreamLatestUrl}", returnStdout: true).trim()
                        params.directstream_test_url = "${directstreamBase}_${directstreamPackageName}"
                    }

                    // m3d_test_url
                    if (['wddm', 'm3d'].contains(env.repo?.toLowerCase())) {
                        params.m3d_test_url = constants.genPackageUrl(env.repo, finalWinBranch, env.winCommitId, m3dPackageName)
                    } else if (wddmInfo) {
                        params.m3d_test_url = "${wddmInfo.url}_${m3dPackageName}"
                    }
                }

                // Add global test configs if present
                if (config.testConfigs) {
                    config.testConfigs.each { k, v ->
                        params[k] = (v instanceof Map || v instanceof List) ? groovy.json.JsonOutput.toJson(v) : v
                    }
                }

                // Check for duplicate testConfig.name
                if (usedNames.contains(testConfig.name)) {
                    error "[win-mr] Duplicate testConfig.name found: ${testConfig.name}"
                }
                usedNames << testConfig.name
                echo "[win-mr] Triggering test job: ${testConfig.name} for driverType: ${testConfig.driverType}"

                workflow["${testConfig.name}"] = [
                    job: testConfig.job,
                    parameters: params,
                    setGitlabStatus: true,
                    async: true
                ]
            }
        }
    }

    if (env.repo == 'mtcc') {
        workflow['updategfxc'] = [
            closure: { updategfxc() },
            setGitlabStatus: false
        ]
    }

    workflow['updateLatestTxt'] = [
        closure: { updateLatestTxt() },
        setGitlabStatus: false
    ]

    runPipeline(workflow)
}
