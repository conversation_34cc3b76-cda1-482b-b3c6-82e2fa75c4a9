package org.swqa.tools

def genJnlpContainerMap(Map containerConfig) {
    def jnlpContainerMap = [
        name: 'jnlp',
        image: constants.IMAGE.JNLP,
        imagePullPolicy: 'Always',
        env: [
            [
                name: 'K8S_NODE_NAME',
                valueFrom: [
                    fieldRef: [fieldPath: 'spec.nodeName']
                ]
            ]
        ],
        resources: [
            requests: [
                cpu: '1',
                memory: '4Gi',
                'ephemeral-storage': '100Gi'
            ],
            limits: [
                cpu: '2',
                memory: '4Gi',
                'ephemeral-storage': '100Gi'
            ]
        ],
    ]
    if (!containerConfig || !containerConfig.image) {
        // jnlp will be the worker container in this situation
        jnlpContainerMap.ports = [
            [
                name: 'http',
                containerPort: '22',
                hostPort: containerConfig.sshPort,
                protocol: 'TCP',
            ],
        ]
    }
    if (containerConfig && containerConfig.vmImage) {
        jnlpContainerMap.env.add([name: 'VM_IMAGE', value: containerConfig.vmImage])
        jnlpContainerMap.env.add([name: 'VM_GPU_REQUEST', value: containerConfig.vmGpuRequest])
        jnlpContainerMap.env.add([name: 'VM_CPU_REQUEST', value: containerConfig.vmCpuRequest])
        jnlpContainerMap.env.add([name: 'VM_MEMORY_REQUEST', value: containerConfig.vmMemoryRequest])
        jnlpContainerMap.image = constants.IMAGE.VM
        jnlpContainerMap.lifecycle = [
            preStop: [
                exec: [
                    command: ['/bin/sh', '-c', 'teardown']
                ]
            ]
        ]
        if (containerConfig.vmGpuRequest) {
            currentBuild.displayName += "-${containerConfig.vmGpuRequest}"
            currentBuild.description += "GPU: ${containerConfig.vmGpuRequest}"
        }
    }
    return jnlpContainerMap
}

def genWorkerContainerMap(Map containerConfig) {
    def customContainerMap = [
        name: 'worker',
        image: containerConfig.image,
        imagePullPolicy: 'Always',
        env: [
            [
                name: 'K8S_NODE_NAME',
                valueFrom: [
                    fieldRef: [fieldPath: 'spec.nodeName']
                ]
            ]
        ],
        command: ['sleep'],
        args: ['99d'],
        ports: [
            [
                name: 'http',
                containerPort: '22',
                hostPort: containerConfig.sshPort,
                protocol: 'TCP',
            ],
        ],
        securityContext: [
            capabilities: [
                add: ['SYS_ADMIN']
            ]
        ]
    ]
    if (containerConfig.resources) {
        customContainerMap.resources = [:]
        containerConfig.resources.split(';').each {
            def (op, type, value) = it.split('=')
            customContainerMap.resources[op] = customContainerMap.resources[op] ? customContainerMap.resources[op] + ["${type}": value] : ["${type}": value]
        }
    }
    return customContainerMap
}

def genMTHostAlias() {
    return constants.mtServiceHosts.collect { host, ip ->
        [ip: ip, hostnames: [host]]
    }
}

def genPodYaml(String jobName, Map containerConfig=null) {
    log.debug("generating yaml with job: ${jobName}, container config: ${containerConfig}")
    podYamlMap = [
        apiVersion: 'v1',
        kind: 'Pod',
        metadata: [
            name: 'SWQA-CI',
            labels: [
                JOB_NAME: jobName
            ]
        ],
        spec: [
            dnsConfig: [:],
            dnsPolicy: 'Default',
            // Blame the unstable DNS service
            hostAliases: genMTHostAlias(),
            affinity: [
                nodeAffinity: [
                    preferredDuringSchedulingIgnoredDuringExecution: [
                        [
                            weight: 1,
                            preference: [
                                matchExpressions: [
                                    [key: 'PLATFORM', operator: 'In', values: ['cpu']]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            containers: [
                genJnlpContainerMap(containerConfig),
            ]
        ],
    ]
    if (containerConfig && containerConfig.image) {
        podYamlMap.spec.containers.add(genWorkerContainerMap(containerConfig))
        if (containerConfig.nodeSelector) {
            def nodeSelector = []
            // nodeSelector will be like In=PLATFORM=Intel;In=GPU_CHIP=qy2
            containerConfig.nodeSelector.split(';').each {
                def (operator, key, value) = it.split('=')
                List values = value.split(',')
                if (key == 'GPU_TYPE') {
                    currentBuild.displayName += "-${value}"
                    currentBuild.description += "<b> GPU_TYPE: ${value} </b><br>"
                }
                nodeSelector.add([key: key, operator: operator, values: values])
            }
            podYamlMap.spec.affinity.nodeAffinity['requiredDuringSchedulingIgnoredDuringExecution'] = [
                nodeSelectorTerms: [
                    [matchExpressions: nodeSelector]
                ]
            ]
        }
    }
    log.debug("pod yaml data: ${podYamlMap}")
    String podYaml = writeYaml data: podYamlMap, returnText: true
    return podYaml
}

def genVMPodYaml(String jobName, Map containerConfig ) {
    log.debug("generating yaml with job: ${jobName}, container config: ${containerConfig}")
    podYamlMap = [
        apiVersion: 'v1',
        kind: 'Pod',
        metadata: [
            name: 'SWQA-CI',
            labels: [
                JOB_NAME: jobName
            ]
        ],
        spec: [
            dnsConfig: [:],
            dnsPolicy: 'Default',
            hostAliases: genMTHostAlias(),
            containers: [
                genJnlpContainerMap(containerConfig),
            ]
        ],
    ]
    log.debug("vm pod yaml data: ${podYamlMap}")
    String podYaml = writeYaml data: podYamlMap, returnText: true
    return podYaml
}

// def containerConfig = [
//     name: 'mytest',
//     image: 'containerImage',
//     nodeSelector: 'IN=PLATFORM=Intel;IN=GPU_CHIP=qy2',
//     resources: 'requests=cpu=6;requests=memory=20Gi;requests=ephemeral-storage=100Gilimits=cpu=10;limits=memory=32Gi;limits=mthreads.com/gpu=2;'
// ]
// println(genPodYaml('test-job', containerConfig))
