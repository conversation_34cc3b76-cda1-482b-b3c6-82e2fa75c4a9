package org.swqa.tools

import java.security.SecureRandom

def runRelyNetwork(int retries=3, int timeoutMinutes=5, Closure command) {
    def attempt = 0
    def success = false
    timeout(time: timeoutMinutes, unit: 'MINUTES') {
        while (attempt < retries) {
            try {
                attempt++
                command.call()
                success = true
                break
            } catch (exc) {
                echo "Attempt ${attempt} failed: ${exc.message}"
                sh 'systemctl restart NetworkManager ||:'
                if (attempt == retries) {
                    error "Network issue: All ${retries} attempts failed."
                }
            }
        }
    }
    return success
}

def findMrDependency(String repo, String text) {
    text = text.replaceAll('\\\\', '')
    def pattern = /\[${repo}:(.*?)\]/
    def matcher = text =~ pattern
    return matcher.find() ? matcher.group(1).strip() : ''
}

def ccachedCmd(String ccacheDir='', String maxsize='5G') {
    cmd = ''
    if (ccacheDir) {
        apt.installPackage('ccache')
        cmd = """
            mkdir -p ${ccacheDir}
            export USE_CCACHE=1
            export CCACHE_DIR=${ccacheDir}
            export CCACHE_MAXSIZE=${maxsize}
            export CCACHE_NOHARDLINK=1
            export CCACHE_BASEDIR=/root
            export CCACHE_COMPILERCHECK=content
            export CCACHE_UMASK=0000
            ccache -s || true
        """
    }
    return cmd
}

def removeNonStringValuesOfMap(Map input) {
    return input.findAll { k, v -> v instanceof CharSequence }
}

def formatString(String template, Map binding = [:]) {
    def engine = new org.apache.commons.lang3.text.StrSubstitutor(binding)
    def s = engine.replace(template)
    engine = null
    return s
}

def validateDateTag(tag_date) {
    if (tag_date) {
        return tag_date.matches('[0-9]{8}')
    }
    return false
}

def listToMap(list, key = 'name') {
    def m = [:]
    list.each {
        m[it[key]] = it
    }
    return m
}

def genBindingFromExternalFile(str, format = 'yaml', regx = /\$\{(.*)\}/) {
    def readData = null
    switch (format) {
        case 'yaml':
            readData = readYaml text: str
            break
        case 'json':
            readData = readJSON text: str
    }

    def resultMap = [:]

    if (readData['include']) {
        // group: sw
        // repo: linux-ddk
        // filePath: .ciConfig.yaml
        // branch: develop
        // as: linux-ddk
        def externalData = [:]
        def includeInfo = readData['include']
        if (!(includeInfo instanceof List)) {
            includeInfo = [includeInfo]
        }
        includeInfo.each { info ->
            def fileType = info.filePath.split('\\.')[-1]
            def infoData = new git().getFileContentByApi(info.repo, info.branch, info.filePath, fileType, info.group)
            externalData[info['as']] = infoData
        }

        def matcher = str =~ regx
        def find = matcher.findAll()
        if (find.size()) {
            find.each {
                def keyStr = it[1].trim()
                def keyList = keyStr.split('->')
                def keyListSize = keyList.size()
                def externalDataKey = keyList[0]
                def externalDataItem = externalData[externalDataKey]

                if (externalDataItem) {
                    for (int i = 1; i < keyListSize; i++) {
                        externalDataItem = externalDataItem[keyList[i]]
                        if (i == keyListSize - 1) {
                            resultMap[it[1]] = externalDataItem
                        }
                        if (externalDataItem == null) {
                            break
                        }
                        if (externalDataItem instanceof List) {
                            externalDataItem = listToMap(externalDataItem)
                        }
                    }
                }
            }
        }
    }
    return resultMap
}

def loadConfig(String name, Map binding=[:]) {
    def format = name.split('\\.')[-1]
    def text = null
    def externalBinding = [:]
    try {
        text = libraryResource("conf/${name}")
        externalBinding = genBindingFromExternalFile(text, format)
    } catch (exc) {
        if (exc.message.contains('No such library resource')) {
            return null
        }
    }
    def configText = formatString(text, externalBinding + binding)
    switch (format) {
        case 'yaml':
            return readYaml(text: configText)
        case 'json':
            return readJSON(text: configText)
        default:
            return configText
    }
}

def loadPipelineConfig(String repoConfig, String localConfig, Map binding = [:], String defaultLocalConfig = '') {
    def config = null
    if (fileExists(repoConfig)) {
        log.info('Get CI config from repo.')
        def configText = sh(script: "cat ${repoConfig}", returnStdout: true).trim()
        if (!configText) { return [:] }
        def externalBinding = genBindingFromExternalFile(configText, 'yaml')
        def formatedText = formatString(configText, externalBinding + binding)
        config = formatedText ? readYaml(text: formatedText) : [:]
    } else {
        log.info('Get CI config from local.')
        config = loadConfig(localConfig, binding)
    }

    if (config == null && defaultLocalConfig) {
        log.info('Get CI config from default local.')
        config = loadConfig(defaultLocalConfig, binding)
    }

    if (config == null) {
        log.error("There is no ci config for this branch: ${env.gitlabSourceBranch}.")
        throw new Exception("There is no ci config for this branch: ${env.gitlabSourceBranch}.")
    }

    filterUnsuccessJobs(config)
    if (env.gitlabTargetBranch) {
        filterJobsByChangedFiles(config)
    }

    println '-----config-----'
    println config

    return config
}

def isOverTime(time1, time2, days) {
    def overTime = false
    def duration = java.time.Duration.between(time1, time2)
    def seconds = duration.toSeconds()
    if (seconds > 24 * 60 * 60 * days) {
        overTime = true
    }

    return overTime
}

def filterUnsuccessJobs(config, buildLabel = '', repoName = env.gitlabSourceRepoName, commitId = env.gitlabMergeRequestLastCommit, ref = env.gitlabSourceBranch) {
    if (env.gitlabSourceBranch == null) { return }
    if (env.gitlabActionType == 'PUSH') { return }
    // Check for cirenew trigger phrase
    if (env.gitlabActionType == 'NOTE' && env.gitlabTriggerPhrase =~ /(?i)cirenew/) {
        return
    }

    // Check for cirerun[status1,status2,...] or cirerun [status1,status2,...] trigger phrase
    def cirerunStatusPattern = /(?i)cirerun\s*\[([^\]]+)\]/
    def cirerunStatusMatcher = env.gitlabTriggerPhrase =~ cirerunStatusPattern

    // Generate an array from the match for later use
    def cirerunStatusArray = []
    if (cirerunStatusMatcher) {
        def statusesStr = cirerunStatusMatcher[0][1]
        cirerunStatusArray = statusesStr.split(',').collect { it.trim().toLowerCase() }
    }

    cirerunStatusMatcher = null

    println "cirerunStatusArray: ${cirerunStatusArray}"

    if (cirerunStatusArray && cirerunStatusArray.contains('all')) {
        return
    }

    // Create condition based on cirerunStatusArray
    def shouldRerun = { status ->
        if (cirerunStatusArray) {
            return cirerunStatusArray.contains(status)
        }
        return status != 'success'
    }

    def zonedDateTimeNow = java.time.ZonedDateTime.now()
    // default: 7 days
    def statusLifetime = 7
    if (config.statusLifetime) {
        try {
            statusLifetime = config.statusLifetime.toDouble()
        } catch (e) {
            println e
        }
    }

    def statuses = new git().getGitlabCommitAllStatuses(repoName, commitId, ref)
    def statusesMap = [:]

    statuses.each {
        statusesMap[it.name] = [
            'status': it.status,
            'finished_at': it.finished_at
        ]
    }

    buildLabel = buildLabel ?: env.buildLabel
    config = config ?: [:]
    def _builds = config.builds ?: []
    def _tests = config.tests ?: []

    if ((buildLabel && shouldRerun(statusesMap[buildLabel]?.status)) || !buildLabel) {
        config.builds = _builds.findAll {
            def label = it.parameters?.testLabel ?: it.name
            def statusData = statusesMap[label] ?: [:]
            if (shouldRerun(statusData.status)) {
                return true
            }
            def overTime = true
            try {
                def zonedDateTime = java.time.ZonedDateTime.parse(statusData['finished_at'])
                overTime = isOverTime(zonedDateTime, zonedDateTimeNow, statusLifetime)
            } catch (e) {
                println e
            }
            println("${label} overTime: ${overTime}")
            return overTime
        }
    } else {
        config.builds = []
    }

    config.tests = _tests.findAll {
        def label = it.parameters?.testLabel ?: it.name

        def statusData = statusesMap[label] ?: [:]
        if (shouldRerun(statusData.status)) {
            return true
        }
        def overTime = true
        try {
            def zonedDateTime = java.time.ZonedDateTime.parse(statusData['finished_at'])
            overTime = isOverTime(zonedDateTime, zonedDateTimeNow, statusLifetime)
        } catch (e) {
            println e
        }
        println("${label} overTime: ${overTime}")
        return overTime
    }
}

def filterJobsByChangedFiles(config) {
    try {
        config = config ?: [:]
        def _builds = config.builds ?: []
        def _tests = config.tests ?: []

        def changedFiles = sh(script: "git diff origin/${env.gitlabTargetBranch} --name-only", returnStdout: true).trim()
        // Prepare list of changed files (split by newlines and remove empty entries)
        def changedFilesList = changedFiles.split('\n').findAll { it != '' }
        println "changedFilesList: ${changedFilesList}"

        def shouldKeep = { job ->
            return filterWithList(job.whitelist ?: job.blacklist, changedFilesList, job.whitelist ? 'whitelist' : 'blacklist')
        }

        config.builds = _builds.findAll(shouldKeep)
        config.tests = _tests.findAll(shouldKeep)
    } catch (e) {
        log.error("Filtered by blacklist failed: ${e.message}")
    }
}

@NonCPS
def filterWithList(filterList, items, mode = 'blacklist') {
    boolean isBlacklist = mode == 'blacklist'

    // Handle cases with no filter patterns
    if (!filterList) {
        return isBlacklist // No rules: blacklist allows all, whitelist denies all
    }

    // Clean invalid patterns (empty strings)
    def filteredList = filterList.findAll { it.trim() != '' }
    if (filteredList.isEmpty()) {
        return isBlacklist // No valid rules: maintain default behavior
    }

    // Prepare data structures for efficient matching
    def exactMatches = filteredList as Set // For O(1) exact match checks
    filteredList.sort { -it.length() } // Sort by descending length for prefix matching efficiency

    boolean hasAnyMatch = false // Whether any item passed the filter
    boolean allMatch = true // Whether all items passed the filter (only relevant for blacklist)

    items.each { item ->
        boolean matched = exactMatches.contains(item) // First check for exact matches

        // Then check for prefix matches (longest patterns first)
        matched = matched || filteredList.any { pattern ->
            pattern.length() < item.length() && item.startsWith(pattern)
        }

        if (matched) {
            hasAnyMatch = true
        } else {
            allMatch = false // At least one item failed the filter
        }
    }

    // Return result based on mode:
    // - Blacklist: allow if ALL items are NOT matched (return !allMatch)
    // - Whitelist: allow if ANY item is matched (return hasAnyMatch)
    return isBlacklist ? !allMatch : hasAnyMatch
}

def loadScript(String name, String path='', Boolean excutable=true) {
    def text = libraryResource("scripts/${path}/${name}")
    writeFile(file: name, text: text)
    if (excutable) {
        sh "chmod 755 ${name}"
    }
}

def runScript(String name, String path='') {
    loadScript(name, path)
    sh "./${name}"
}

def getNodeIP() {
    def ip = ''
    catchError(stageResult: null, buildResult: null) {
        def checkNodeisUnix = isUnix()
        def ipReg = checkNodeisUnix ? /src\s+(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})/ : /(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})/
        def getIPCmd = checkNodeisUnix ? 'ip -o route get to *************' : 'ipconfig | findstr IPv4'
        def output = checkNodeisUnix ? sh(script: getIPCmd, returnStdout: true).trim() : bat(script: getIPCmd, returnStdout: true).trim()
        // ************** - github.mthreads.com
        // ************** via ************** dev bond0 src ************ uid 1001 \    cache
        // IPv4 地址 . . . . . . . . . . . . : ***************
        def matcher = (output =~ ipReg)
        if (matcher.find()) {
            ip = matcher.group(1)
        }
    }
    return ip
}

def restartLinuxNode() {
    sh 'nohup bash -c "sleep 1 && reboot" > /dev/null 2>&1 &'
}

def restartWinNode() {
    bat 'shutdown -r -t 5'
}

def checkNodeisOffline(String nodeName) {
    def node = jenkins.model.Jenkins.instance.getNode(nodeName)
    return node.getComputer().isOffline()
}

// def checkNodeisOffline(String nodeName) {
//     def cmd = "http://sh-jenkins.mthreads.com/computer/${nodeName}/api/json?pretty=true --user swqa-gfx-ci:11c2674c4bf21898c34c89c72767e7aa15"
//     def nodeInfo = sh(script: "curl --insecure ${cmd}", returnStdout: true)
//     def response = new groovy.json.JsonSlurper().parseText(nodeInfo)
//     String nodeOfflineStatus = response.offline
//     return nodeOfflineStatus.toBoolean()
// }

def toggleNodeOffline(String nodeName, boolean offline, String message) {
    def encodedMessage = URLEncoder.encode(message, 'UTF-8')
    def action = offline ? "toggleOffline?offlineMessage=${encodedMessage}" : 'toggleOffline'
    sh """
        curl --insecure -X POST "http://swci-jenkins.mthreads.com/computer/${nodeName}/${action}" \
            -u ya.li:115727448a1a218ff0ae41d2100e0bec95
    """
}

def setNodeStatus(String nodeName, String targetStatus, String message="offline by pipeline ${env.JOB_NAME}#${env.BUILD_ID}") {
    def supportedStatus = ['online', 'offline']

    if (!supportedStatus.contains(targetStatus)) {
        throw new IllegalArgumentException("Unsupported status: ${targetStatus}. Supported statuses are: ${supportedStatus.join(', ')}")
    }

    node('Linux_jump') {
        def targetOffline = targetStatus == 'offline'

        def isNodeOffline = checkNodeisOffline(nodeName)
        log.info("checkNodeisOffline for ${nodeName}: ${isNodeOffline}")

        if (isNodeOffline != targetOffline) {
            log.info("setting node ${nodeName} to ${targetStatus} now...")
            toggleNodeOffline(nodeName, targetOffline, message)
        }
        log.info("node ${nodeName} current is ${targetStatus} already")
    }
}

def waitUntilNodeOnline(String nodeName, int maxWaitTimeInSeconds) {
    log.debug("Waiting node ${nodeName} offline...")
    timeout(time: 180, unit: 'SECONDS') {
        waitUntil(initialRecurrencePeriod: 3000, quiet: true) {
            return checkNodeisOffline(nodeName)
        }
    }
    log.debug("Node ${nodeName} is offline.")
    log.debug("Waiting node ${nodeName} online...")
    timeout(time: maxWaitTimeInSeconds, unit: 'SECONDS') {
        // Sets the initial wait period, in milliseconds, between retries. Defaults to 250ms.
        // If true, the step does not log a message each time the condition is checked. Defaults to false.
        waitUntil(quiet: true) {
            return (checkNodeisOffline(nodeName) == false)
        }
    }
    log.debug("Node ${nodeName} is online.")
}

def disconnectNode(String nodeName) {
    sh """
        curl --insecure -X POST "http://swci-jenkins.mthreads.com/computer/${nodeName}/doDisconnect?offlineMessage=Auto" \
            --user swqa-gfx-ci:115d022ce3f2bedbeb7a954091f0fbdf8a
    """
    print 'Switch to disconnect status'
}

def restartNodeByPdu(String nodeName, String ip, String method = 'api') {
    if (!ip) { log.debug('no ip, skip pdu reboot.') }
    if (method == 'script') {
        def pduScpts = 'pdu.py'
        loadScript('pdu_slots_mapping_for_duts.json', 'pdu', false)
        loadScript('pdusnmp.py', 'pdu')
        loadScript(pduScpts, 'pdu')

        sh "python3 ${pduScpts} -o off -t ${ip}"
        sleep(15)
        disconnectNode(nodeName)
        sh "python3 ${pduScpts} -o on -t ${ip}"
    } else if (method == 'api') {
        sh "curl -X POST http://**************:8000/api/v1/hosts/${ip}/pdu_restart"
    }
}

def reboot(String nodeName, int _timeout=10 * 60) {
    if (env.runChoice && env.runChoice != 'node') {
        return
    }
    try {
        isUnix() ? restartLinuxNode() : restartWinNode()
    } catch (exc) {
        catchError(buildResult: null, stageResult: null) {
            sh 'echo b > /proc/sysrq-trigger'
        }
    }
    node('Linux_jump') {
        try {
            waitUntilNodeOnline(nodeName, _timeout)
        } catch (exc) {
            catchError(buildResult: null, stageResult: null) {
                restartNodeByPdu(nodeName, env.nodeIP)
            }
            waitUntilNodeOnline(nodeName, _timeout)
        }
    }
}

def tearDown() {
    timeout(time: 10, unit: 'MINUTES') {
        if (fileExists('ddk_umd')) {
            ddk.uninstallUmd()
        }
        if (fileExists('mt-ddk/linux-ddk/build')) {
            ddk.uninstallLinuxDdk()
        }
        sh '''
            rm -rf /etc/X11/xorg.conf ||:
            dpkg -P mtgpu ||:
            dpkg -P musa ||:
            find /lib/modules/$(uname -r) -name 'mtgpu.ko' -exec rm -f {} \\; ||:
            find /var/lib -name 'mtgpu.ko' -exec rm -f {} \\; ||:
            find /usr/lib -name 'mtgpu.ko' -exec rm -f {} \\; ||:
            update-initramfs -u -k $(uname -r) ||:
            lsinitramfs /boot/initrd.img-$(uname -r)|grep mt ||:
        '''
    }
}

def recoverEnv() {
    // def recoverEnv(String conf='mtgpu.conf') {
    // sh "cd /etc/modprobe.d && rm -rf mtgpu.conf && wget -q --no-check-certificate https://oss.mthreads.com/product-release/cts/${conf} -O mtgpu.conf"
    sh(libraryResource('scripts/linux/recovery.sh'))
    }

def findProcessID(String processName) {
    return utils.runCommandWithStdout("pgrep -o ${processName} ||:")
}

def killProcess(String processName) {
    try {
        timeout(time: 30, unit: 'SECONDS') {
            waitUntil(initialRecurrencePeriod: 3000, quiet: true) {
                def processId = findProcessID(processName)
                if (processId) {
                    sh "kill -9 ${processId} && sleep 1"
                }
                return (findProcessID(processName).isEmpty())
            }
        }
        println("${processName} process successfully killed.")
        return true
    } catch (exc) {
        println("Error occurred: ${exc}")
        return false
    }
}

def startXorg(String xorgBin='/usr/lib/xorg/Xorg', String exportEnv='') {
    killProcess('Xorg')
    apt.installPackage('xorg')
    // Only allow TSAN_OPTIONS export to avoid introducing unstable or unsafe env vars
    // Other exports like LD_LIBRARY_PATH before xorg may cause segmentation faults
    exportEnv = exportEnv?.contains('TSAN_OPTIONS') ? exportEnv : ''
    // Use subshell to scope export to avoid environment pollution
    // This prevents lingering env vars like LD_PRELOAD that can break system tools (e.g., pgrep, ps)
    sh """
        dmesg -T
        if ! pgrep -f ${xorgBin}; then
            (
                ${exportEnv}
                ${xorgBin} -maxclients 2048 &
            )
        fi
        sleep 5
        ps -ef | grep -v grep | grep Xorg
    """
}

def checkGlxinfo() {
    def exportEnv = env.exportEnv?.contains('TSAN_OPTIONS') ? env.exportEnv.trim().replaceAll(/;+\s*$/, '') : ''
    def glxinfoCmd = exportEnv ? "${exportEnv}; export DISPLAY=:0.0; timeout 30 glxinfo -B" : 'export DISPLAY=:0.0; timeout 30 glxinfo -B'
    def glxinfo = utils.runCommandWithStdout(glxinfoCmd)
    println(glxinfo)
    def matcher = (glxinfo =~ /Vendor:\s+([^\(]+)/)
    if (matcher.find()) {
        def vendor = matcher[0][1].trim()
        println "Detected Vendor: ${vendor}"
        if (vendor != 'MOORE THREADS') {
            error("GPU status abnormal: Expected Vendor 'MOORE THREADS', but found '${vendor}'. Please check.")
        }
    } else {
        error 'Vendor information not found in glxinfo output. Please verify the command output.'
    }
}

def publishHTML(String reportDir, String reportFiles, String reportName) {
    publishHTML(target:[
        allowMissing:true,
        alwaysLinkToLastBuild:false,
        keepAll:true,
        reportDir: reportDir,
        reportFiles: reportFiles,
        reportName: reportName
    ])
}

def runCoverage(String cvgReport) {
    sh """
        lcov -d . -c -o ${cvgReport} --branch-coverage --ignore-error=mismatch --ignore-error=source
    """
// lcov --remove ${cvgReport} '*/include/*' '*/3rdparty/*' '*/test/*' '*/mt-sdk/*' '*/cml/*' '*/fol/*' '*/shared/*' '*/pfm/*' -o ${cvgReport} --branch-coverage
}

def updateAptSource() {
    sh '''
        cd /tmp
        wget -q https://oss.mthreads.com/sw-infra-devops/miyongpeng/tools/ca-certificates_20211016ubuntu0.20.04.1_all.deb ||:
        dpkg -i ca-certificates_20211016ubuntu0.20.04.1_all.deb ||:

        cat > /etc/apt/sources.list << EOF
deb https://nexus.shg1.mthreads.com/repository/apt-proxy/ jammy main restricted universe multiverse
deb https://nexus.shg1.mthreads.com/repository/apt-proxy/ jammy-security main restricted universe multiverse
deb https://nexus.shg1.mthreads.com/repository/apt-proxy/ jammy-updates main restricted universe multiverse
EOF
        apt update ||:
    '''
}

def copyFilesToJumpNode(String targetFiles, String destPath) {
    updateAptSource()
    sh 'apt install -y sshpass || true'
    sh "sshpass -p 123456 scp -o StrictHostKeyChecking=no -r ${targetFiles} root@**************:/home/<USER>/workspace/${destPath}"
}

def checkCvgStdout(String summay, String criteria) {
    def pattern = /.*lines\D*(?<lines>\d+\.?\d*)%.*\s.*functions\D*(?<functions>\d+\.?\d*)%.*\s.*branches\D*(?<branches>\d+\.?\d*)%.*\s/
    def matcher = summay =~ pattern

    if (matcher.find()) {
        return assertValue(matcher, criteria)
    }

    return [1, 'Could not find coverage rate in summary for lines, functions, and branches']
}

def allureplugin(String resultPath, String allureReportPath = 'allure-report') {
    allure([includeProperties: false,
        jdk: '',
        properties: [],
        reportBuildPolicy: 'ALWAYS',
        report: allureReportPath,
        results: resultPath.split(',').collect { [path: it.trim()] }
    ])
    def allureReportUrl = "${env.BUILD_URL}allure"
    currentBuild.description += "report: ${allureReportUrl}<br>"
    if (currentBuild.result == 'UNSTABLE') { error "Test failed, please check: ${allureReportUrl}" }
}

def allureReport(String resultPath, String allureReportPath = 'allure-report') {
    allure([includeProperties: false,
        jdk: '',
        properties: [],
        reportBuildPolicy: 'ALWAYS',
        report: allureReportPath,
        results: resultPath.split(',').collect { [path: it.trim()] }
    ])
}

def publishHTML(String reportDir, String reportName) {
    publishHTML([
        allowMissing: false,
        alwaysLinkToLastBuild: true,
        keepAll: true,
        reportDir: reportDir,
        reportFiles: 'index.html',
        reportName: reportName
    ])
}

def allure(String resultPath, String allureReportName='allureReport', int timeoutMinutes=10) {
    timeout(time: timeoutMinutes, unit: 'MINUTES') {
        constants.downloadAndUnzipPackage('https://oss.mthreads.com/sw-build/allure/allure.tar.gz', env.WORKSPACE)
        def allureBinaryPath = "${env.WORKSPACE}/allure/bin/allure"
        sh "${allureBinaryPath} generate ${resultPath} --clean -o ${env.WORKSPACE}/${allureReportName} --config charset=utf-8"
        publishHTML([
            allowMissing: false,
            alwaysLinkToLastBuild: true,
            keepAll: true,
            reportDir: "${env.WORKSPACE}/${allureReportName}",
            reportFiles: 'index.html',
            reportName: allureReportName,
            includes: '**/*'
        ])
        scriptCmd = "grep -rl failed ${env.WORKSPACE}/${allureReportName}/data/*.json"
        def exitValue = sh(script: scriptCmd, returnStatus: true) == 0
        if (exitValue) {
            error "Test failed, please check: http://swci-jenkins.mthreads.com/job/${env.JOB_NAME}/${env.build_ID}/${URLEncoder.encode(allureReportName, 'UTF-8')}/"
        }
    }
}

def downloadTar(String tarUrl) {
    def tarName = tarUrl.split('/')[-1]
    def unpackCmd = tarName.endsWith('.tar') ? 'tar -xf' : 'tar -xzf'
    sh """
        wget -q --no-check-certificate ${tarUrl}
        ${unpackCmd} ${tarName}
    """
}

def parseMultilineString(String multilineString, String sep=':') {
    if (sep) {
        return multilineString.split().collectEntries { line ->
            def (key, value) = line.split(sep)
            [(key): value]
        }
    }
    return multilineString.split().findAll { it }
}

def parseExportVars(String multilineString) {
    // Return empty list if input is null or blank
    if (!multilineString?.trim()) { return [] }

    List<String> parts = []
    List<String> exports = []

    try {
        def exportMap = readJSON text: multilineString
        parts = exportMap.collectMany { k, v ->
            if (v instanceof List) {
                v.collect { item -> "${k?.trim()}=${item?.toString()?.trim()}" }
            } else if (v?.toString()?.trim()) {
                ["${k?.trim()}=${v.toString().trim()}"]
            } else {
                []
            }
        }
    } catch (ignored) {
        parts = multilineString.split(/[\r\n;]+/).findAll { it.trim() && it.contains('=') }
    }

    // Validate variable names: must match [_A-Za-z][_A-Za-z0-9]*
    def validKeyRegex = /^[_A-Za-z][_A-Za-z0-9]*$/
    // Filter valid entries
    exports = parts.collectEntries { line ->
        def stripped = line.replaceFirst(/^export\s+/, '')
        def (key, value) = stripped.split('=', 2)
        key = key.trim()
        value = value.trim()
        (key ==~ validKeyRegex && value) ? [(key): value] : [:]
    }.collect { k, v -> "export ${k}=${v}" }
    return exports
    }

def getUrlContent(String url) {
    return sh(script: "curl --insecure ${url}", returnStdout: true).trim()
}

def checkCtsDeps() {
    try {
        runRelyNetwork {
            sh '''
                if ! dpkg -l | grep libvulkan1; then
                    echo "install libvulkan1..."
                    apt update
                    apt install -y libvulkan1
                    echo "libvulkan1 is installed..."
                else
                    echo "libvulkan1 is installed..."
                fi
            '''
        }
        // if (!fileExists('/root/cts_vk_vulkan/deqp-vk')) {
        oss.install()
        sh '''
            rm -rf /root/cts_vk_vulkan /root/cts/vk_vulkan && mkdir -p /root/cts/vk_vulkan && cd /root/cts/vk_vulkan
            mc cp -r oss/product-release/cts/vk/vulkan-cts-*******-intel-ubuntu.tar.gz . && tar -zxvf *.tar.gz
            ln -s /root/cts/vk_vulkan/vulkan-cts-*******-intel-ubuntu /root/cts_vk_vulkan
            wget -q --no-check-certificate https://oss.mthreads.com/dependency/ogl/ci/sanitizers/libtsan.so.2.0.0 -O /usr/lib/x86_64-linux-gnu/libtsan.so.2.0.0
        '''
    // }
    } catch (exc) {
        println(exc)
    }
}

def delete_oem_inf() {
    bat '''
        C:\\Windows\\System32\\pnputil.exe /enum-drivers>C:\\Test\\inf.log
    '''
    pnp_test_result = bat(script: 'cat C:\\Test\\inf.log', returnStdout: true).trim()
    pnp_test_result = pnp_test_result.replaceAll(/\s/, '')
    //echo "${pnp_test_result}"
    regs = /:oem\d+.inf[^\x00-\xff]{4}:(mt[-]+\w*[-]*\w*[-]*\w*|mtgpu_gl_hw|wddm_driver).inf[^\x00-\xff]{6}:MooreThreads/
    //echo "${regs}"
    if (pnp_test_result =~ 'OriginalName') {
        regs = /:oem\d+.inf\w{12}:(mt[-]+\w*[-]*\w*[-]*\w*|mtgpu_gl_hw|wddm_driver).inf\w{12}:MooreThreads/
    }
    def oem_list = []
    try {
        def oem_result = pnp_test_result =~ regs
        while (oem_result.find()) {
            //echo "${oem_result.group()}"
            def oem_test = (oem_result.group() =~ /oem\d+.inf/)[0]
            def oem_inf = oem_test.toString()
            oem_list.add(oem_inf)
            echo "${oem_inf}"
        }
        oem_result = null
        oem_test = null

        if (!oem_list.isEmpty()) {
            oem_list.each { oem_file ->
                echo "delete ${oem_file}"
                bat """
                    C:\\Windows\\System32\\pnputil.exe /delete-driver ${oem_file} /uninstall
                    sleep 10
                """
            }
        }
        else {
            echo 'there is no oem inf'
        }
    }
    catch (e) {
        print(e)
    }
}

def initEnv(node_name, node_ip) {
    lock("initEnv_${env.BUILD_NUMBER}") {
        def nodeIdentifier = node_name.split('_')[-1]
        stage("initEnv ${nodeIdentifier}") {
            try {
                timeout(5) {
                    restartWinNode()
                    node('Linux_jump') { waitUntilNodeOnline(node_name, 240) }
                }
            } catch (e) {
                node('Linux_jump') {
                    restartNodeByPdu(node_name, node_ip)
                    waitUntilNodeOnline(node_name, 300)
                }
            }
        }
    }
}

boolean isCollectionOrArray(object) {
    return [Collection, Object[]].any { it.isAssignableFrom(object.getClass()) }
}

boolean isLinkedHashMap(object) {
    return [LinkedHashMap].any { it.isAssignableFrom(object.getClass()) }
}

def mergeMap(List<Map> maps) {
    Map map = [:]
    maps.each { m ->
        m.each { key, value ->
            if (isCollectionOrArray(value)) {
                if (!map.containsKey(key)) { map[key] = [] }
                map[key] += value
            } else if (isLinkedHashMap(value)) {
                if (!map.containsKey(key)) { map[key] = [:] }
                map[key] = mergeMap([map[key], value])
            } else {
                map[key] = value
            }
        }
    }
    return map
}

def fetchCommitFromTag(String daily_tag) {
    def tags_info_url = "https://oss.mthreads.com/release-ci/repo_tags/${daily_tag}.txt"

    try {
        def tags_info_string = sh(script: "curl --insecure ${tags_info_url}", returnStdout: true).trim()

        def tags_info_json = readJSON text: tags_info_string, returnPojo: true

        return tags_info_json
    } catch (e) {
        error "Failed to retrieve or parse commit information: ${e.message}"
    }
}

def getNodeLabels(String nodename) {
    def node = Jenkins.instance.getNode(nodename)
    def labels = node.getLabelString()
    echo "Node(${nodename}) label: ${labels}"
    return labels
}

def setNodeLabels(String nodename, String labels) {
    def node = Jenkins.instance.getNode(nodename)
    /* groovylint-disable-next-line UnnecessarySetter */
    node.setLabelString(labels)
    def checkLabels = node.getLabelString()
    echo "Successfully set the nodelabels of node(${nodename}) to ${checkLabels}."
}

def findLatestOssPkg(String ossPath, String pkgFilter) {
    oss.install()
    return sh(
        script: """
            mc find ${ossPath} --name ${pkgFilter} --exec "mc stat --json {}" \
            | awk -F'\\"' '
                {
                    for (i=1; i<=NF; i++) {
                        if (\$i == "lastModified") lm=\$(i+2)
                        if (\$i == "name") name=\$(i+2)
                    }
                    if (lm && name) print lm, name
                }' \
            | sort -r \
            | awk '{print \$2}' \
            | head -n 1
        """,
        returnStdout: true
    ).trim()
}

def retryByRandomTime(closure, _timeout=120, retries=3) {
    def attempt = 0
    def success = false
    def lastError = null
    def random = new SecureRandom()
    def maxDelaySeconds = 20

    while (attempt < retries && !success) {
        attempt++
        try {
            timeout(time: _timeout, unit: 'MINUTES') {
                closure()
            }
            success = true
        } catch (exc) {
            lastError = exc
            utils.showErrorMessage("Attempt ${attempt} failed: ${exc.message}")
            if (attempt < retries) {
                def delay = random.nextInt(maxDelaySeconds) + 1 // [1, maxDelaySeconds]
                echo "Waiting ${delay}s before retrying..."
                sleep(time: delay, unit: 'SECONDS')
            }
        }
    }

    if (!success) {
        error("All ${retries} attempts failed. Last error: ${lastError}")
    }
}

def getDmesgErrors() {
    def output = sh(script: 'sudo dmesg -l err | grep mtgpu || true', returnStdout: true).trim()
    if (output) {
        utils.showErrorMessage("❌ Kernel error messages detected in dmesg: ${output}")
    } else {
        println '✅ No kernel error messages found in dmesg.'
    }
    return output ?: null
}

def removeWinJobs(config) {
    config = config ?: [:]
    if (config.builds) {
        config.builds = config.builds.findAll { !(it.job?.endsWith('.win')) }
    }
    if (config.tests) {
        config.tests = config.tests.findAll { !(it.job?.endsWith('.win')) }
    }

    return config
}
