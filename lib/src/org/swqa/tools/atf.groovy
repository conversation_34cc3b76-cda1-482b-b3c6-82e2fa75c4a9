package org.swqa.tools

import groovy.transform.Field
import groovy.json.JsonSlurperClassic
import groovy.json.JsonOutput

@Field String ATF_USERNAME = 'graphic_qa'
@Field String ATF_PASSWORD = '123456'
@Field String ATF_TOKEN = 'M2Q0ZjhiN2Y='  // 佳益个人token
@Field String DB_HOST = '**************'
@Field String DB_PORT = '3390'
@Field String DB_USERNAME = 'qa_data_analysis_dev'
@Field String DB_PASSWORD = 'yJpiPudACm5io25N'
@Field String DB_NAME = 'qa_data_analysis_dev'
@Field String dbCmdPrefix = "mysql -h ${DB_HOST} -u ${DB_USERNAME} -p${DB_PASSWORD} -P ${DB_PORT} -D ${DB_NAME}"
@Field String API_KEY = 'M2Q0ZjhiN2Y='

def makeRequestByCurl(curlCmd) {
    retry(3) {
        def responseText = sh(script: curlCmd, returnStdout: true).trim()
        try {
            log.info("response: ${responseText}")
            return new JsonSlurperClassic().parseText(responseText)
        } catch (exc) {
            return responseText
        }
    }
}

def getAtfJobResult(jobId) {
    // Acquire token first, token will be expired in 5 mins, and use token will not refresh the expire timeout
    // So for safety, acquire a new token
    cmd = """
        curl https://atf-v4-prod.shg1.mthreads.com/test-job/${jobId} \
        --header 'X-API-KEY: ${API_KEY}' \
        --header 'Content-Type: application/json'
    """
    return makeRequestByCurl(cmd)
}

def waitJobStatus(jobId, status, interval=10, _timeout=60) {
    timeout(time: _timeout, unit: 'SECONDS') {
        waitUntil(initialRecurrencePeriod: interval * 1000, quiet: true) {
            def atfJobData = getAtfJobResult(jobId)
            log.info("ATF job ${jobId} current status: ${atfJobData.status}")
            return atfJobData.status in status
        }
    }
    log.info("ATF job ${jobId} status is ${status} now")
}

def startExecution(data) {
    cmd = """
        curl --location 'https://atf-v4-prod.shg1.mthreads.com/execution/build' \
        -X 'POST' \
        --header 'accept: application/json, text/plain, */*' \
        --header 'content-type: application/json' \
        --header 'X-API-KEY: ${ATF_TOKEN}' \
        --data '${data}'
    """
    return makeRequestByCurl(cmd)
}

def reportCtsResultToDb(jobId, category, item) {
    // required: category, item
    def tableName = 'daily_summary'
    def reportDate = new Date().format('yyyy-MM-dd')
    def project = 'SUDI'
    def driver = 'DDK'
    def platform = 'CTS'

    def atfJobData = getAtfJobResult(jobId)
    def columns = ['report_dt', 'project', 'driver', 'platform', 'category', 'item', 'result', 'value']
    def values = [
        "'${reportDate}', '${project}', '${driver}', '${platform}', '${category}', '${item}', 'pass', ${atfJobData.stats.pass ?: null}",
        "'${reportDate}', '${project}', '${driver}', '${platform}', '${category}', '${item}', 'fail', ${atfJobData.stats.fail ?: null}",
        "'${reportDate}', '${project}', '${driver}', '${platform}', '${category}', '${item}', 'not-support', ${atfJobData.stats.notSupported ?: null}",
    ]
    sql = """
        ${dbCmdPrefix} -e "INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${values.join('), (')});"
    """
    def out = sh(script: sql, returnStdout: true).trim()
    log.info(out)
}

def killJob(jobId, wait=true) {
    def cmd = """
    curl --location --request POST 'https://atf-v4-prod.shg1.mthreads.com/test-job/kill/${jobId}' \
    --header 'accept: application/json, text/plain, */*' \
    --header 'Connection: keep-alive' \
    --header 'X-API-KEY: ${ATF_TOKEN}'
    """
    log.info("Kill ATF test job: ${jobId}")
    def killedJobId = makeRequestByCurl(cmd)
    wait && waitJobStatus(jobId, ['STATUS_KILLED', 'STATUS_ARCHIVED'])
    return killedJobId
}

def mergeJob(jobId) {
    killJob(jobId, true)
    def cmd = """
    curl --location --request POST 'https://atf-v4-prod.shg1.mthreads.com/test-job/merge/${jobId}' \
        --header 'X-API-KEY: ${API_KEY}' \
    """
    log.info("Merge job: ${jobId}")
    return makeRequestByCurl(cmd)
}

def createTestJob(String testPlanId, String jobName, Boolean wait=true) {
    jobName = URLEncoder.encode(jobName, 'UTF-8')
    cmd = """
        curl --insecure 'https://atf-v4-prod.shg1.mthreads.com/test-job?testPlanId=${testPlanId}&title=${jobName}' \
            --header 'X-API-KEY: ${API_KEY}' \
            --data '{}' \
    """
    log.info("Creating new ATF test job with test plan: ${testPlanId}")
    def jobId = makeRequestByCurl(cmd)
    wait && waitJobStatus(jobId, ['STATUS_RUNNABLE', 'STATUS_RUNNING'])
    return jobId
}

def createRetryJob(jobId, results=null) {
    killJob(jobId, true)
    def RETRY_RESULTS = ['RESULT_UNKNOWN', 'RESULT_NOT_SUPPORTED', 'RESULT_FAIL', 'RESULT_TIMEOUT', 'RESULT_CRASH', 'RESULT_INIT']
    def retryResults = results ?: RETRY_RESULTS
    def data = JsonOutput.toJson([
        'title': '',
        'results': retryResults
    ])
    cmd = """
        curl 'https://atf-v4-prod.shg1.mthreads.com/test-job/${jobId}/rerun' \
            -X 'POST' \
            -H 'Content-Type: application/json' \
            -H 'X-API-KEY: ${API_KEY}' \
            --data '${data}'
    """
    def response = makeRequestByCurl(cmd)
    // assert response.id.isNumber()
    return response.id
}

def getTestReport(jobId) {
    def responseText = ''
    def cmd = """
        curl --location 'https://atf-v4-prod.shg1.mthreads.com/result/export' \
        --header 'accept: application/json, text/plain, */*' \
        --header 'content-type: application/json' \
        --header 'X-API-KEY: ${ATF_TOKEN}' \
        --data '{"jobId":"${jobId}"}'
    """
    retry(3) {
        responseText = sh(script: cmd, returnStdout: true).trim()
        log.info("response: ${responseText}")
    }
    return responseText
}
