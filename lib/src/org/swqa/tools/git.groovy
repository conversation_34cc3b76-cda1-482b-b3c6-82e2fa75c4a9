package org.swqa.tools

def getCurrentCommitID(String repo) {
    def commitID
    dir(repo) {
        commitID = sh(script: 'git rev-parse HEAD', returnStdout: true).split('HEAD')[-1].trim()
    }
    return commitID[0..8]
}

def fetchCode(
    String repoName,
    String branch,
    String commit=null,
    Map options=null
) {
    def defaultOptions = [preBuildMerge: true, disableSubmodules: false, shallow: false, submoduleShallow: true, updateBuildDescription: false, noTags: false]
    options = options ?: [:]
    defaultOptions.each { key, value ->
        options[key] = options.containsKey(key) ? options[key] : value
    }
    if (env.gitlabActionType?.equalsIgnoreCase('push')) {
        options.preBuildMerge = false
    }
    def _extensions = [
        [$class: 'CloneOption', timeout: 30, shallow: options.shallow, noTags: options.noTags],
        [$class: 'CheckoutOption', timeout: 30],
        [$class: 'UserIdentity', email: '<EMAIL>', name: 'git-robot'],
        [$class: 'SubmoduleOption', disableSubmodules: options.disableSubmodules, parentCredentials: true, recursiveSubmodules: true, shallow: options.submoduleShallow, timeout: 60]
    ]
    if (!utils.isEmpty(env.gitlabTargetBranch) && repoName.toLowerCase() == env.gitlabSourceRepoName?.toLowerCase() && options.preBuildMerge) {
        _extensions.add([$class: 'PreBuildMerge', options: [fastForwardMode: 'FF', mergeRemote: 'origin', mergeStrategy: 'DEFAULT', mergeTarget: "${env.gitlabTargetBranch}"]])
    }
    if (options.lfs) {
        _extensions.add([$class: 'GitLFSPull'])
    }
    dir(repoName) {
        deleteDir()
        def group = options.gitlabGroup ?: constants.GITLAB.SW_GROUP
        def baseUrl = options.gitlabBaseUrl ?: constants.GITLAB.SH_CODE_URL
        checkout([
            $class: 'GitSCM',
            branches: [[name: commit ?: branch]],
            userRemoteConfigs: [
                [
                    credentialsId: 'sh-code-ssh',
                    url: "${baseUrl}:${group}/${repoName}.git"
                ]
            ],
            extensions: _extensions
        ])

        if (isUnix()) {
            sh """
                git config --global --add safe.directory '*' || :
                git config --unset core.hooksPath || :
                git checkout -b ${branch} || :
            """
        } else {
            bat "git checkout -b ${branch} || echo Command failed"
        // bat 'git config --unset core.hooksPath || echo Command failed'
        }
    }
    String commitId = commit ?: getCurrentCommitID(repoName)
    log.info("repo: ${repoName} | branch: ${branch} | commit: ${commitId}")
    if (options.updateBuildDescription) { currentBuild.description += "${repoName} | ${branch} | ${commitId}<br>" }
    return constants.formatCommitID(commitId)
}

def updateSubmodule(String repoPath='.', Integer depth = null, String submodule_name = '', String disable_submodule_update = '') {
    def gitCmd = 'git'
    if (disable_submodule_update) {
        def submodules = disable_submodule_update.split(',')
        submodules.each { submodule ->
            gitCmd += " -c submodule.\"${submodule}\".update=none"
        }
    }
    gitCmd += ' submodule update --init --recursive'
    if (depth != null) {
        gitCmd += " --depth ${depth}"
    }

    if (submodule_name && !submodule_name.isEmpty()) {
        gitCmd += " ${submodule_name}"
    }

    credentials.runWithCredential('SSH_GITLAB') {
        dir(repoPath) {
            if (isUnix()) {
                echo 'Running on Unix-like system'
                sh """
                    git clean -dffx
                    ${gitCmd}
                """
            } else {
                echo 'Running on Windows system'
                bat """
                    git config --global core.longpaths true
                    git clean -dffx
                    ${gitCmd} || exit 1
                """
            }
        }
    }
}

def isNewer(commitId, currentLatestCommitId) {
    Number ret = sh(script: "git merge-base --is-ancestor ${commitId} ${currentLatestCommitId}", returnStatus: true)
    // commitId older than currentLatestCommitId, ret will be 0
    // otherwise (commitId is newer, or one of them is not valid) ret will be 1
    return ret == 1
}

def setTag(String tag) {
    credentials.runWithCredential('SSH_GITLAB') {
        sh """
            git tag -d ${tag} || :
            git tag -a ${tag} -m "auto tag"
            git push origin ${tag} -f
        """
    }
}

def deleteTag(String repo, String tag) {
    credentials.runWithCredential('SSH_GITLAB') {
        dir(repo) {
            sh """
                git tag --list
                git tag -d ${tag} || :
                git push origin :${tag} || :
            """
        }
    }
}

def getGitlabMRHeadPipelineStatus(
    String repoName = env.gitlabSourceRepoName,
    String mrIid = env.gitlabMergeRequestIid
) {
    projectId = getGitlabProjectId(repoName)
    def response = utils.gitlabHttpRequest([
        url: "${SH_CODE_API_URL}/projects/${projectId}/merge_requests/${mrIid}"
    ])
    def jsonData = readJSON text: response.content
    return jsonData.head_pipeline?.status ?: ''
}

def isMRconflict(String repoName = env.gitlabSourceRepoName, String mrIid = env.gitlabMergeRequestIid) {
    def projectId = getGitlabProjectId(repoName)
    def response = utils.gitlabHttpRequest([
        url: "${SH_CODE_API_URL}/projects/${projectId}/merge_requests/${mrIid}"
    ])
    def jsonData = readJSON text: response.content
    return jsonData.has_conflicts == true
}

def getGitlabRepocommitByApi(String repoName, String branch) {
    def projectId = getGitlabProjectId(repoName)
    def response = utils.gitlabHttpRequest([
        url: "${SH_CODE_API_URL}/projects/${projectId}/repository/commits/${branch}"
    ])
    def jsonData = readJSON text: response.content
    return constants.formatCommitID(jsonData.id)
}

def getSubmoduleCommit(String submodulePath) {
    try {
        credentials.runWithCredential('SSH_GITLAB') {
            if (submodulePath.contains('/')) {
                def parentPath = submodulePath.tokenize('/')[0]
                if (!fileExists("${parentPath}/.git")) {
                    echo "Submodule '${parentPath}' not initialized, initializing with depth 1..."
                    utils.runCommand("git submodule update --init --depth 1 ${parentPath}")
                }
                dir(parentPath) {
                    def subPath = submodulePath.substring(submodulePath.indexOf('/') + 1)
                    return getSubmoduleCommit(subPath)
                }
            } else {
                def commit = utils.runCommandWithStdout("git ls-files -s -- \"${submodulePath}\" | awk '{print \$2}' | cut -c 1-9")
                return commit
            }
        }
    } catch (exc) {
        error("Failed to get submodule commit for ${submodulePath}: ${exc.message}")
    }
}

def createGitlabTagByApi(String repoName, String tagName, String ref) {
    def projectId = getGitlabProjectId(repoName)
    def response = utils.gitlabHttpRequest([
        url: "${SH_CODE_API_URL}/projects/${projectId}/repository/tags?tag_name=${tagName}&ref=${ref}",
        httpMode: 'POST'
    ])
    log.debug(response.content)
    def data = readJSON text: response.content
    return constants.formatCommitID(data.target)
}

def createGitlabBranchByApi(String repoName, String branchName, String ref) {
    def projectId = getGitlabProjectId(repoName)
    def response = utils.gitlabHttpRequest([
        url: "${SH_CODE_API_URL}/projects/${projectId}/repository/branches?branch=${branchName}&ref=${ref}",
        httpMode: 'POST'
    ])
    log.debug(response.content)
    def data = readJSON text: response.content
    return data.commit?.id
}

def isMrRebaseWithoutTriggerPipeline(
    String repoName = env.gitlabSourceRepoName,
    String mrIid = env.gitlabMergeRequestIid
) {
    //def status = getGitlabMRHeadPipelineStatus(repoName, mrIid)
    //println "isMrRebaseWithoutTriggerPipeline: status is ${status}"

    return getGitlabMRHeadPipelineStatus(repoName, mrIid) == 'skipped'
}

def getGitlabProjectId(String repoName) {
    def SH_CODE_SW_PATH = 'sw'
    def REPO_FULL_PATH = "${SH_CODE_SW_PATH}/${repoName}"

    return URLEncoder.encode(REPO_FULL_PATH, 'UTF-8')

    // credentials.runWithCredential('API_GITLAB') {
    //     def command = """
    //         curl -s -X GET \
    //         --header "PRIVATE-TOKEN:${API_TOKEN_GITLAB}" \
    //         "${SH_CODE_GRAPHQL_API_URL}" \
    //         -H "Content-type:application/json" \
    //         -d '{"query":"{project(fullPath:\\"${REPO_FULL_PATH}\\"){id}}"}' | grep -o '"id":"[^"]*' | sed 's/"id":"//' | cut -d'/' -f5
    //     """

    //     def id = sh(script: command, returnStdout: true).trim()

    //     println "projectId: ${id}"

//     return id
// }
}

def getGitlabCommitStatus(String repoName, String commitId, String name) {
    withCredentials([string(credentialsId: SH_CODE_API_TOKEN_NAME, variable: 'API_TOKEN')]) {
        projectId = getGitlabProjectId(repoName)
        def encodedName = URLEncoder.encode(name, 'UTF-8')
        def encodedRef = URLEncoder.encode(env.gitlabSourceBranch, 'UTF-8')
        println 'Encoded name: ' + encodedName

        def response = utils.gitlabHttpRequest([
            url: "${SH_CODE_API_URL}/projects/${projectId}/repository/commits/${commitId}/statuses?name=${encodedName}&ref=${encodedRef}"
        ])
        def jsonData = readJSON text: response.content
        return jsonData.size() > 0 ? jsonData[0].status : ''
    }
}

def getGitlabCommitAllStatuses(String repoName, String commitId, String ref) {
    withCredentials([string(credentialsId: SH_CODE_API_TOKEN_NAME, variable: 'API_TOKEN')]) {
        def allData = []
        def page = 1
        def perPage = 100
        def lastPageNum = perPage

        def projectId = getGitlabProjectId(repoName)
        def encodedRef = URLEncoder.encode(ref, 'UTF-8')
        def baseApiUrl = "https://sh-code.mthreads.com/api/v4/projects/${projectId}/repository/commits/${commitId}/statuses?per_page=${perPage}&ref=${encodedRef}"

        while (lastPageNum == perPage) {
            def response = utils.gitlabHttpRequest([
                url: "${baseApiUrl}&page=${page}"
            ])
            def jsonData = readJSON text: response.content
            allData = allData + jsonData
            lastPageNum = jsonData.size()
            page = page + 1
        }

        return allData
    }
}

def setGitLabStatusRetryByApi(
    String name,
    String state,
    String repoName = env.gitlabTargetRepoName,
    String commitId = env.gitlabMergeRequestLastCommit,
    String link = env.RUN_DISPLAY_URL) {
    def maxRetries = 3
    def interval = 10
    def retries = 0
    // def httpStatus = 0
    // def successHttpStatus = [200, 201, 204, 301, 302]
    while (retries < maxRetries) {
        setGitLabStatusByApi(name, state, repoName, commitId, link)
        // get current status
        def mrStatus = getGitlabCommitStatus(repoName, commitId, name)
        if (mrStatus == state) {
            log.debug("setGitLabStatus: [${name} -> ${state}] successful!")
            break
        } else {
            log.debug("setGitLabStatus failed, retry after ${interval} seconds")
            Thread.sleep(1000 * interval)
            retries++
        }
    // httpStatus = response.substring(Math.max(0, response.length() - 3)) as Integer  // Extract the last three characters of the returned result as the HTTP status code.
    // if (successHttpStatus.contains(httpStatus)) {
    //     println "API Successful!"
    //     break
    // } else {
    //     println "API Failed，HTTP Code: $httpStatus"
    //     retries ++
    //     println "Retrying for the ${retries} time..."
    // }
    }

    if (retries > maxRetries) {
        println "setGitLabStatus: [${name} -> ${state}] Failed!"
        return false
    }
    return true
    }

def setGitLabStatusByApi(
    String name,
    String state,
    String repoName=env.gitlabTargetRepoName,
    String commitId=env.gitlabMergeRequestLastCommit,
    String link = env.RUN_DISPLAY_URL
) {
    def projectId = getGitlabProjectId(repoName)
    def encodedName = URLEncoder.encode(name, 'UTF-8')
    def encodedRef = URLEncoder.encode(env.gitlabSourceBranch, 'UTF-8')
    def response = utils.gitlabHttpRequest([
        url: "${SH_CODE_API_URL}/projects/${projectId}/statuses/${commitId}?name=${encodedName}&state=${state}&target_url=${link}&ref=${encodedRef}",
        httpMode: 'POST'
    ])
    log.debug("setting gitlab status ${encodedName} -> ${state}")
    return response.content
}

def setGitlabStatus(String name, String state='pending', String link = '') {
    if (currentBuild.description?.contains("Skip CI for MR ${env.gitlabMergeRequestIid} via dashboard tool")) {
        log.debug('Skip CI marker detected, skipping subsequent steps')
        return
    }
    if (utils.isEmpty(env.gitlabTargetRepoName) || utils.isEmpty(env.gitlabMergeRequestLastCommit)) {
        log.debug('No required gitlab paramter found (repo name & last commit), skip setting gitlab status')
        return
    }

    // valid state: pending, running, success, failed
    List skipStates = ['failed', 'success']
    if (skipStates.contains(state)) {
        def currentState = getGitlabCommitStatus(env.gitlabTargetRepoName, env.gitlabMergeRequestLastCommit, name)
        if (state == currentState) {
            log.debug("Skip set state for the same status: ${currentState}")
            return
        }
    }
    log.debug("setGitLabStatus: ${name} -> ${state}")
    if (env.debug == 'true') {
        log.debug('we are in debug mode, will not update gitlab status actually.')
        return
    }
    // if (env.BUILD_USER == 'SCM Change') {
    //     setGitlabCommitStatus name: name, state: state
    // } else {
    //     setGitLabStatusRetryByApi(name, state)
    // }
    setGitLabStatusRetryByApi(name, state, env.gitlabTargetRepoName, env.gitlabMergeRequestLastCommit, link ?: env.RUN_DISPLAY_URL)
}

def setGitlabStatus(List names, String state='pending', List links = []) {
    names.eachWithIndex { name, i ->
        setGitlabStatus(name, state, links[i] ?: '')
    }
}

def getGitlabMRStatus(
    String mrIid = env.gitlabMergeRequestIid,
    String repoName = env.gitlabSourceRepoName
) {
    withCredentials([string(credentialsId: SH_CODE_API_TOKEN_NAME, variable: 'API_TOKEN')]) {
        def projectId = getGitlabProjectId(repoName)
        def apiUrl = "https://sh-code.mthreads.com/api/v4/projects/${projectId}/merge_requests/${mrIid}"
        def response = utils.gitlabHttpRequest([
            url: apiUrl
        ])
        def jsonData = readJSON text: response.content

        return jsonData['state']
    }
}

def getFileContentByApi(String repo, String branch, String filePath, String fileType = 'plain', String group = 'sw') {
    def id = URLEncoder.encode("${group}/${repo}", 'UTF-8')
    def fileName = URLEncoder.encode(filePath, 'UTF-8')
    def apiUrl = "https://sh-code.mthreads.com/api/v4/projects/${id}/repository/files/${fileName}?ref=${branch}"
    def response = utils.gitlabHttpRequest([
        url: apiUrl
    ])
    def fileInfoJson = readJSON text: response.content
    def fileBlob = new String(fileInfoJson.content.decodeBase64(), 'UTF-8')

    switch (fileType) {
        case 'yaml':
            def yamlContent = readYaml text: fileBlob
            return yamlContent
        case 'json':
            def jsonData = readJSON text: fileBlob
            return jsonData
        default:
            return fileBlob
    }
}

def installGitLfs() {
    apt.installPackage('git-lfs')
    sh 'git lfs install'
}

// cancel auto merge
def cancelAutomerge() {
    def whitelist = ['gr-kmd', 'linux-ddk', 'mt-vgpu']
    if (whitelist.contains(env.gitlabSourceRepoName)) { return }
    try {
        def projectId = getGitlabProjectId(env.gitlabSourceRepoName)
        def apiUrl = "https://sh-code.mthreads.com/api/v4/projects/${projectId}/merge_requests/${env.gitlabMergeRequestIid}/cancel_merge_when_pipeline_succeeds"
        utils.gitlabHttpRequest([
            url: apiUrl,
            httpMode: 'POST'
        ])
    } catch (exc) {
        println exc
    }
}

def addCommentForMR(note) {
    try {
        def projectId = getGitlabProjectId(env.gitlabSourceRepoName)
        def apiUrl = "https://sh-code.mthreads.com/api/v4/projects/${projectId}/merge_requests/${env.gitlabMergeRequestIid}/notes"
        def payload = [body: note]
        def jsonPayload = new groovy.json.JsonBuilder(payload).toString()
        utils.gitlabHttpRequest([
            url: apiUrl,
            httpMode: 'POST',
            customHeaders: [[name: 'Content-Type', value: 'application/json']],
            requestBody: jsonPayload
        ])
    } catch (exc) {
        println "Failed to add MR comment: ${exc}"
    }
}

def triggeredByMR() {
    // no gitlabMergeRequestState in namespace when pipeline is triggered by comment
    boolean isMRTriggered = env.gitlabMergeRequestState?.toLowerCase() == 'opened'
    def triggerRegex = /cirerun|cirenew|runtest/
    boolean isCommentTriggered = env.gitlabTriggerPhrase?.toLowerCase() =~ triggerRegex
    return isMRTriggered || isCommentTriggered
}

def getTagCommit(String repo, String tag) {
    try {
        def projectId = getGitlabProjectId(repo)
        def gitlabUrl = "https://sh-code.mthreads.com/api/v4/projects/${projectId}/repository/tags/${tag}"

        withCredentials([string(credentialsId: SH_CODE_API_TOKEN_NAME, variable: 'GITLAB_API_TOKEN')]) {
            def response = sh(script: "curl --insecure -H 'PRIVATE-TOKEN: ${GITLAB_API_TOKEN}' '${gitlabUrl}' 2>/dev/null || echo '{}'", returnStdout: true).trim()
            def tagInfo = readJSON(text: response)

            if (tagInfo?.commit?.id) {
                return tagInfo.commit.id
            }

            echo "No commit ID found in tag response for repo '${repo}' and tag '${tag}'"
            throw new Exception("Tag '${tag}' not found in repo '${repo}'")
        }
    } catch (e) {
        echo "Failed to get tag commit via GitLab API: ${e.message}"
        throw e
    }
}
