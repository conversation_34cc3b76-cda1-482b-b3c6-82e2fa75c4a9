package org.swqa.tools

def cancelPreviousBuild(String jobName) {
    if (!env.gitlabMergeRequestIid) {
        log.warning('no merge request id, skip.')
        return
    }
    def job = Jenkins.instance.getItemByFullName(jobName)
    if (job) {
        job.builds.each { build ->
            try {
                def triggerInfo = build.getEnvVars().get('triggerInfo')
                if (build.isBuilding() && triggerInfo =~ "gitlabMergeRequestIid:${env.gitlabMergeRequestIid}" && triggerInfo =~ "gitlabSourceRepoName:${env.gitlabSourceRepoName}") {
                    log.info("Cancel ${jobName}#${build.number}")
                    build.description = "New build started, cancel ${jobName}#${build.number}<br>" + build.description
                    build.doStop()
                }
            } catch (exc) {
                log.error("Cancel ${jobName}#${build.number} error: ${exc}")
            }
        }
    }
}

def addTriggerDesc() {
    def cause = currentBuild.getBuildCauses()
    def triggerInfo = 'Triggered by other'
    if (!cause.isEmpty()) {
        def info = cause[0].shortDescription
        triggerInfo = info.replace('Started', 'Triggered')
    }
    currentBuild.description = currentBuild.description ?: "${triggerInfo}<br>"
}

@NonCPS
def stopPreviousBuilds(currentJob, buildNumber, currentPrId) {
    echo "Starting to check previous builds for job: '${currentJob.name}' with PR ID: '${currentPrId}'"

    for (def build : currentJob.builds) {
        if (build != null && build.isBuilding() && build.number.toInteger() < buildNumber) {
            echo "Checking Build #${build.number}"

            def buildPrIdMatch = build.getEnvVars().get('triggerInfo') =~ "gitlabMergeRequestIid:${currentPrId}"
            if (buildPrIdMatch) {
                echo "Stopping Build #${build.number} as it matches the current PR ID: '${currentPrId}'"
                build.doStop()
                echo "Waiting 5 seconds after stopping build #${build.number}"
                sleep(5)
            } else {
                echo "Build #${build.number} does not match the current PR ID: '${currentPrId}'. Skipping."
            }
        } else if (build != null) {
            echo "Build #${build.number} is not running or is newer than current build #${buildNumber}. Skipping."
        }
    }

    echo "Debug: Finished checking builds for job: '${currentJob.name}'"
}

def checkPrPreviousBuilds() {
    def jobName = env.JOB_NAME
    def buildNumber = env.BUILD_NUMBER.toInteger()
    def currentPrId = env.gitlabMergeRequestIid

    echo "Job Name is '${jobName}'"
    echo "Build Number is '${buildNumber}'"
    echo "Current PR ID is '${currentPrId}'"

    if (!currentPrId) {
        echo 'No PR information found.'
        return
    }

    try {
        def currentJob = Jenkins.instance.getItemByFullName(jobName)
        stopPreviousBuilds(currentJob, buildNumber, currentPrId)
    } catch (NoSuchElementException e) {
        echo "Caught NoSuchElementException: ${e.message}"
    } catch (e) {
        echo "Caught Exception: ${e.message}"
    }
}
