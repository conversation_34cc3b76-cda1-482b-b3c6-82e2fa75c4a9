builds:
  - job: "build.mm_fw"
    buildAfterMerged: "true"
    name: "jenkins/linux-build"
    parameters:
      containerImage: "sh-harbor.mthreads.com/build-env/fw:v6"
      cmd: "./build.sh"
      runCppcheck: "yes"
      distPath: "./refc/src/seurat.bin"
      fwPackageName: "mtvpu-02-1.0.bin"
  - job: "build.mm_fw"
    name: "jenkins/clang-tidy"
    parameters:
      containerImage: "sh-harbor.mthreads.com/build-env/fw:v6"
      cmd: "./clang-tidy.sh"
      runCppcheck: "no"
tests:
  - job: "CI_video_mtgpu_test_gitlab"
    name: "jenkins/S50_video_test"
    parameters:
      fwPackageName: "mtvpu-02-1.0.bin"
      test_nodes: "S50_video_test"
  - job: "CI_video_mtgpu_test_gitlab"
    name: "jenkins/S80_video_test"
    parameters:
      fwPackageName: "mtvpu-02-1.0.bin"
      test_nodes: "S80_video_test"
  - job: "CI_DDK_media_test_gitlab"
    name: "jenkins/S80_media-driver_test"
    parameters:
      fwPackageName: "mtvpu-02-1.0.bin"
      test_nodes: "S80_video_test"
  - job: "DDK_media_encode_test_gitlab"
    name: "jenkins/linux_media_encode_test"
    parameters:
      fwPackageName: "mtvpu-02-1.0.bin"
      nodeLabel: "ddk_video_test"
