builds:
  - job: "build.mt-management-platform"
    name: "jenkins/linux-mt-management-platform"
    parameters:
      containerImage: "sh-harbor.mthreads.com/sdk/management:v10"
      cmd: "./build.sh Debug x86_64 HOST ${currentDate} TRUE"

  - job: "build.mt-management-platform.win"
    name: "win/mt-management-platform_build"
    parameters:
      cmd: "build.bat Debug Win64 ${currentDate} TRUE DGPU NO"
