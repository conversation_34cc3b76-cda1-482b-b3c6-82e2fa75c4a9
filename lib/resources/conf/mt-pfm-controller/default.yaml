builds:
  - job: "build.mt-pfm-controller"
    name: "jenkins/linux_build"
    parameters:
      nodeLabel: "Linux_build"
      cmd: "python3 build.py --ci"
      pkgName: "linux_mt_pfm_controller.tar.gz"
  - job: "build.mt-pfm-controller"
    name: "jenkins/windows_build"
    parameters:
      nodeLabel: "Win11 && Build"
      cmd: "python build.py --ci"
      containerImage: ""
      pkgName: "windows_mt_pfm_controller.tar.gz"
tests:
  - job: "test.mt_pfm_controller"
    name: "jenkins/qy2_test"
    parameters:
      nodeLabel: "S90 && Ubuntu22.04 && x86_64"
      cmd: "python3 build.py --ci --test --gpu-arch qy2"
