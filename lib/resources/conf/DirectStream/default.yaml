builds:
  - job: "build.DirectStream"
    name: "linux/x86-DirectStream-build"
    parameters:
      cmd: 'sed -i ''s/https:\/\/github.mthreads.com\/mthreads\/************************:sw/g/'' ./DirectStream_Build_Linux.sh  && ./DirectStream_Build_Linux.sh -a all'
      packageName: "DirectStream.tar.gz"
      nodeLabel: "Linux_build"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v23"
  - job: "build.DirectStream"
    name: "linux/x86-DirectStream-clang-tidy"
    parameters:
      cmd: 'sed -i ''s/https:\/\/github.mthreads.com\/mthreads\/************************:sw/g/'' ./DirectStream_Build_Linux.sh  && ./DirectStream_Build_Linux.sh -a all --clang_tidy'
      nodeLabel: "Linux_build"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v23"
tests:
  - job: "CI_DirectStream_test_pipline"
    name: "jenkins/all test"
  - job: "CI_directstream_build_gitlab"
    name: "jenkins/linux_directstream_build"
  - job: "CI_DirectStream_vaapi-fits_test_pipline"
    name: "jenkins/vaapiTest"
  - job: "Coverity_Access_directstream_linux_test"
    name: "jenkins/linux coverity test"
  - job: "test.directStreamCodec"
    name: "jenkins/directstream decode test"
    parameters:
      nodeLabel: "S50_video_test || S80_video_test"
      testScripr: "apt install dos2unix -y && chmod +x ./dec_ci.sh && ./dec_ci.sh -d /root/DirectStream/yuv"
