builds:
  - job: "build.mt-dcgm"
    name: "jenkins/build"
    parameters:
      buildCmd: "./build.sh --release --debug --arch amd64 --clean --deb"
tests:
  - job: "test.mt_dcgm"
    name: "jenkins/test"
    parameters:
      buildCmd: "./build.sh --release --arch amd64 --clean --coverage"
      linuxDdkPackageUrl: "https://oss.mthreads.com/release-ci/computeQA/cuda_compatible/newest/master/musa_Ubuntu_amd64.deb"
      musaToolkitsPackageUrl: "https://oss.mthreads.com/release-ci/computeQA/cuda_compatible/newest/master/musa_toolkits_install_full.tar.gz"
