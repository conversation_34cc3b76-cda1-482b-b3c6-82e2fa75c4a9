builds:
  - job: "build.linux-ddk"
    name: "linux-ddk build"
    parameters:
      containerImage: "sh-harbor.mthreads.com/qa/linux-ddk:v6"
      pkgName: "ddk2.0"
      cmd: "./ddk_build.sh -a 0 -r 1"
      # add ddk info when triggered by submodule
      ddkBranch: "mt-ddk-2.0"
      submoduleConfig: '{"gr-umd": "head", "libdrm-mt": "head", "m3d": "head", "shared_include": "head"}'
      buildChoice: "tar.gz"
tests:
  - job: "test.m3d_musa_cts"
    name: "m3d musa cts test"
    parameters:
      branch: "m3d_master"
      testType: "smokeM3d"
      testLabel: "m3d musa cts test"
