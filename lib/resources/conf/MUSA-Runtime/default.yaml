buildImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v26"
builds:
  - pkgName: "MUSA_Runtime"
    cmd: "./umd_build.sh -w nulldrmws -p mtgpu_linux -r 1.0.0.0 -d 0 -o hw -b debug -j9"
    parameters:
      umdPackageUrl: "https://oss.mthreads.com/release-ci/computeQA/ddk/stable/umd_x86-mtgpu_linux-xorg-release-pdump_off.tar"
  - pkgName: "MUSA_Runtime_Debug_Pdump"
    cmd: "./umd_build.sh -w nulldrmws -p mtgpu_linux -r 1.0.0.0 -d 0 -o hw -b debug -j9"
    parameters:
      umdPackageUrl: "https://oss.mthreads.com/release-ci/computeQA/ddk/stable/umd_x86-mtgpu_linux-xorg-debug-pdump_on.tar"
tests:
  - job: "test.graphic_cts"
    name: "graphic cts test for gl_cts_pr"
    parameters:
      testConfig: '{"gl4.1":{"workdir":"/root/cts_gl","binary":"glcts","case":"cts/gl_cts_pr.txt"}}'
  - job: "test.graphic_cts"
    name: "graphic cts test for gl_cs_cts_pr"
    parameters:
      testConfig: '{"gl4.2+":{"workdir":"/root/cts_gl","binary":"glcts","case":"cts/gl_cs_cts_pr.txt"}}'
  - job: "test.graphic_cts"
    name: "graphic cts test for gles_cts_pr"
    parameters:
      testConfig: '{"gles":{"workdir":"/root/cts_gles","binary":"glcts","case":"cts/gles_cts_pr.txt"}}'
  - job: "test.graphic_cts"
    name: "graphic cts test for vk_cts_pr"
    parameters:
      testConfig: '{"vk":{"workdir":"/root/cts_vk","binary":"deqp-vk","case":"cts/vk_cts_pr.txt"}}'
  - job: "test.vps"
    name: "qy2 vps test"
    parameters:
      kmdPackageName: "x86_64-mtgpu_linux-xorg-release-vps.tar.gz"
      umdPackageName: "x86_64-mtgpu_linux-xorg-release-vps.tar.gz"
      model: "cmodel"
      chipType: "quyuan2"
  - job: "test.vps"
    name: "ph1 vps test"
    parameters:
      kmdPackageName: "x86_64-mtgpu_linux-xorg-release-vps.tar.gz"
      umdPackageName: "x86_64-mtgpu_linux-xorg-release-vps-ph1.tar.gz"
      model: "amodel"
      chipType: "ph1"
  - job: "test.renderdoc"
    name: "renderdoc test"
  - job: "test.piglit"
    name: "piglit test"
  - job: "test.glmark2"
    name: "x86 glmark2 test"
    parameters:
      nodeLabel: "piglit_test"
      kmdPackageName: "x86_64-mtgpu_linux-xorg-release-hw.deb"
  - job: "test.pdump"
    name: "pdump test"
    parameters:
      umdPackageName: "x86_64-mtgpu_linux-xorg-release-hw-pdump.tar.gz"
  - job: "test.perf"
    name: "pref test"
  - job: "test.mtgl_cts"
    name: "mtgl cts test"
  - job: "test.dma_transfer"
    name: "dma_transfer test"
