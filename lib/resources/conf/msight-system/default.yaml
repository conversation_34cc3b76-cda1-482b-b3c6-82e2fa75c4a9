builds:
  - job: "build.msight-system"
    name: "linux/x86_64_internal"
    parameters:
      cmd: "python3 build.py --ci --version=0.4.0"
      containerImage: "sh-harbor.mthreads.com/sdk/msight:v13"
      nodeLabel: "Linux_build"
      output: "target/Release/package"
      pkgName: "moore-perf-system_${currentDate}_x86_64_internal.tar.gz"
  - job: "build.msight-system"
    name: "linux/x86_64_external"
    parameters:
      cmd: "python3 build.py --ci --version=0.4.0 --external"
      containerImage: "sh-harbor.mthreads.com/sdk/msight:v13"
      nodeLabel: "Linux_build"
      output: "target/Release/package"
      pkgName: "moore-perf-system_${currentDate}_x86_64_external.tar.gz"
  - job: "build.msight-system"
    name: "linux/coverage"
    parameters:
      cmd: "python3 build.py --ci --coverage --version=0.4.0 --build_kuae --coverage"
      containerImage: "sh-harbor.mthreads.com/sdk/msight:v13"
      nodeLabel: "Linux_build"
      output: "target/Release/package"
      pkgName: ""
      coverageReportPath: "build/coverageReport"
  - job: "build.msight-system"
    name: "linux/aarch64_internal"
    parameters:
      cmd: "python3 build.py --ci --version=0.4.0 --sys_proc=aarch64"
      containerImage: "sh-harbor.mthreads.com/sdk/msight:v12"
      nodeLabel: "Linux_build"
      output: "target/Release/package"
      pkgName: "moore-perf-system_${currentDate}_aarch64_internal.tar.gz"
  - job: "build.msight-system"
    name: "linux/aarch64_external"
    parameters:
      cmd: "python3 build.py --ci --version=0.4.0 --sys_proc=aarch64 --external"
      containerImage: "sh-harbor.mthreads.com/sdk/msight:v12"
      nodeLabel: "Linux_build"
      output: "target/Release/package"
      pkgName: "moore-perf-system_${currentDate}_aarch64_external.tar.gz"
  - job: "build.msight-system"
    name: "linux/loongarch64_internal"
    parameters:
      cmd: "python3 build.py --ci --version=0.4.0 --sys_proc=loongarch64"
      containerImage: "sh-harbor.mthreads.com/sdk/msight:v13"
      nodeLabel: "Linux_build"
      output: "target/Release/package"
      pkgName: "moore-perf-system_${currentDate}_loongarch64_internal.tar.gz"
  - job: "build.msight-system"
    name: "linux/loongarch64_external"
    parameters:
      cmd: "python3 build.py --ci --version=0.4.0 --sys_proc=loongarch64 --external"
      containerImage: "sh-harbor.mthreads.com/sdk/msight:v13"
      nodeLabel: "Linux_build"
      output: "target/Release/package"
      pkgName: "moore-perf-system_${currentDate}_loongarch64_external.tar.gz"
  - job: "build.msight-system"
    name: "linux/vdi_x86_64_internal"
    parameters:
      cmd: "python3 build.py --ci --version=0.4.0 --vdi_only"
      containerImage: "sh-harbor.mthreads.com/sdk/msight-vdi:v8"
      nodeLabel: "Linux_build"
      output: "target/Release/package"
      pkgName: "moore-perf-system_${currentDate}_vdi_internal.tar.gz"
  - job: "build.msight-system"
    name: "linux/kuae_x86_64_internal"
    parameters:
      cmd: "python3 build.py --ci --version=0.4.0 --build_kuae"
      containerImage: "sh-harbor.mthreads.com/sdk/msight:v13"
      nodeLabel: "Linux_build"
      output: "target/Release/package"
      pkgName: "moore-perf-system_${currentDate}_kuae_x86_64_internal.tar.gz"
  - job: "build.msight-system"
    name: "linux/kuae_x86_64_external"
    parameters:
      cmd: "python3 build.py --ci --version=0.4.0 --build_kuae --external"
      containerImage: "sh-harbor.mthreads.com/sdk/msight:v13"
      nodeLabel: "Linux_build"
      output: "target/Release/package"
      pkgName: "moore-perf-system_${currentDate}_kuae_x86_64_external.tar.gz"
  - job: "build.msight-system"
    name: "linux/m1000_aarch64_internal"
    parameters:
      cmd: "python3 build.py --ci --version=0.4.0  --sys_proc=aarch64 --build_m1000"
      containerImage: "sh-harbor.mthreads.com/sdk/msight:v12"
      nodeLabel: "Linux_build"
      output: "target/Release/package"
      pkgName: "moore-perf-system_${currentDate}_m1000_aarch64_internal.tar.gz"
  - job: "build.msight-system"
    name: "linux/m1000_aarch64_external"
    parameters:
      cmd: "python3 build.py --ci --version=0.4.0  --sys_proc=aarch64 --build_m1000 --external"
      containerImage: "sh-harbor.mthreads.com/sdk/msight:v12"
      nodeLabel: "Linux_build"
      output: "target/Release/package"
      pkgName: "moore-perf-system_${currentDate}_m1000_aarch64_external.tar.gz"
  - job: "build.msight-system.win"
    name: "win/clang-tidy"
    parameters:
      cmd: >-
        cmd /k ""C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat" -arch=x64 && python build.py --ci --clang_tidy"
      buildPath: "msight-system"
  - job: "Coverity_Access_msight_test"
    name: "jenkins/coverity linux test"
  - job: "Coverity_Access_win_msight_test"
    name: "jenkins/coverity win test"
  - job: "CI_Win_ms_build"
    name: "msight-system/win build"
