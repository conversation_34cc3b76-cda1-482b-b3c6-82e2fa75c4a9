builds:
  - job: "build.gr-umd"
    name: "x86_64-mtgpu_linux-xorg-${pkgType}-hw"
    parameters:
      cmd: "./umd_build.sh -w xorg -p mtgpu_linux -r 1.0.0.0 -d 0 -o hw -b ${pkgType} -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v26"
      packageName: "x86_64-mtgpu_linux-xorg-${pkgType}-hw"
  - job: "build.gr-umd"
    name: "x86_64-mtgpu_linux-xorg-${pkgType}-hw-glvnd"
    parameters:
      cmd: "./umd_build.sh -w xorg -p mtgpu_linux -r 1.0.0.0 -d 0 -o hw -b ${pkgType} -g -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v26"
      packageName: "x86_64-mtgpu_linux-xorg-${pkgType}-hw-glvnd"
  - job: "build.gr-umd"
    name: "x86_64-mtgpu_linux-xorg-${pkgType}-hw-pdump"
    parameters:
      cmd: "./umd_build.sh -w xorg -p mtgpu_linux -r 1.0.0.0 -d 1 -o hw -b ${pkgType} -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v26"
      packageName: "x86_64-mtgpu_linux-xorg-${pkgType}-hw-pdump"
  - job: "build.gr-umd"
    name: "x86_64-mtgpu_linux-xorg-${pkgType}-vps"
    parameters:
      cmd: "./umd_build.sh -w xorg -p mtgpu_linux -r 1.0.0.0 -d 0 -o vps -b ${pkgType} -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v26"
      packageName: "x86_64-mtgpu_linux-xorg-${pkgType}-vps"
  - job: "build.gr-umd"
    name: "x86_64-mtgpu_linux-nulldrmws-${pkgType}-vps"
    parameters:
      cmd: "./umd_build.sh -w nulldrmws -p mtgpu_linux -r 1.0.0.0 -o vps -b ${pkgType} -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v26"
      packageName: "x86_64-mtgpu_linux-nulldrmws-${pkgType}-vps"
  - job: "build.gr-umd"
    name: "x86_64-mtgpu_linux-wayland-${pkgType}-hw"
    parameters:
      cmd: "./umd_build.sh -w wayland -p mtgpu_linux -r 1.0.0.0 -d 0 -o hw -b ${pkgType} -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v26"
      packageName: "x86_64-mtgpu_linux-wayland-${pkgType}-hw"
  - job: "build.gr-umd"
    name: "x86_64-mtgpu_linux-xorg-${pkgType}-vps-ph1"
    parameters:
      cmd: "./umd_build.sh -w xorg -p mtgpu_linux -r 1.0.0.0 -d 0 -o vps -b ${pkgType} -j9 --mt_arch=ph1"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v26"
      packageName: "x86_64-mtgpu_linux-xorg-${pkgType}-vps-ph1"
  - job: "build.gr-umd"
    name: "loongarch64-mtgpu_linux-xorg-${pkgType}-hw"
    parameters:
      cmd: "./umd_build.sh -a loongarch64 -c loongarch64-linux-gnu- -p mtgpu_linux -w xorg -r 1.0.0.0 -d 0 -o hw -b ${pkgType} -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v26"
      packageName: "loongarch64-mtgpu_linux-xorg-${pkgType}-hw"
  - job: "build.gr-umd"
    name: "loongarch64-mtgpu_linux-xorg-${pkgType}-hw-glvnd"
    parameters:
      cmd: "./umd_build.sh -a loongarch64 -c loongarch64-linux-gnu- -p mtgpu_linux -w xorg -r 1.0.0.0 -d 0 -o hw -b ${pkgType} -g -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v26"
      packageName: "loongarch64-mtgpu_linux-xorg-${pkgType}-hw-glvnd"
  - job: "build.gr-umd"
    name: "i386-mtgpu_linux-xorg-${pkgType}-hw-wine"
    parameters:
      cmd: "./umd_build.sh -a i386 -t -g -w xorg -p mtgpu_linux -r 1.0.0.0 -o hw -b ${pkgType} -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v26"
      packageName: "i386-mtgpu_linux-xorg-${pkgType}-hw-wine"
tests:
  - job: "test.graphic_cts"
    name: "graphic cts test for gl_cts_pr"
    parameters:
      testConfig: "{\"gl4.1\":{\"workdir\":\"/root/cts_gl\",\"binary\":\"glcts\",\"case\":\"https://oss.mthreads.com/sw-build/gr-umd/${branch}/test/cts/gl_cts_pr.txt\"}}"
  - job: "test.graphic_cts"
    name: "graphic cts test for gl_cs_cts_pr"
    parameters:
      testConfig: "{\"gl4.2+\":{\"workdir\":\"/root/cts_gl\",\"binary\":\"glcts\",\"case\":\"https://oss.mthreads.com/sw-build/gr-umd/${branch}/test/cts/gl_cs_cts_pr.txt\"}}"
  - job: "test.graphic_cts"
    name: "graphic cts test for gles_cts_pr"
    parameters:
      testConfig: "{\"gles\":{\"workdir\":\"/root/cts_gles\",\"binary\":\"glcts\",\"case\":\"https://oss.mthreads.com/sw-build/gr-umd/${branch}/test/cts/gles_cts_pr.txt\"}}"
  - job: "test.graphic_cts"
    name: "graphic cts test for vk_cts_pr"
    parameters:
      testConfig: "{\"vk\":{\"workdir\":\"/root/cts_vk\",\"binary\":\"deqp-vk\",\"case\":\"https://oss.mthreads.com/sw-build/gr-umd/${branch}/test/cts/vk_cts_pr.txt\"}}"
  - job: "test.piglit"
    name: "piglit test"
  - job: "test.dma_transfer"
    name: "dma_transfer test"
