builds:
  - job: "build.gr-umd"
    name: "x86_64-mtgpu_linux-nulldrmws-release-vps"
    parameters:
      cmd: "./umd_build.sh -w nulldrmws -p mtgpu_linux -r 1.0.0.0 -o vps -b release -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v26"
      packageName: "x86_64-mtgpu_linux-nulldrmws-release-vps"
  - job: "build.gr-umd"
    name: "x86_64-nohw_linux-nulldrmws-release-vps-pdump"
    parameters:
      cmd: "./umd_build.sh -w nulldrmws -p nohw_linux -r 1.0.0.0 -o vps -b release -d 1 -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v26"
      packageName: "x86_64-nohw_linux-nulldrmws-release-vps-pdump"
  - job: "build.gr-umd"
    name: "x86_64-mtgpu_linux-xorg-release-hw"
    parameters:
      cmd: "./umd_build.sh -w xorg -p mtgpu_linux -r 1.0.0.0 -d 0 -o hw -b release -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v26"
      packageName: "x86_64-mtgpu_linux-xorg-release-hw"
  - job: "build.gr-umd"
    name: "x86_64-mtgpu_linux-xorg-release-hw-glvnd"
    parameters:
      cmd: "./umd_build.sh -w xorg -p mtgpu_linux -r 1.0.0.0 -d 0 -o hw -b release -g -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v26"
      packageName: "x86_64-mtgpu_linux-xorg-release-hw-glvnd"
  - job: "build.gr-umd"
    name: "x86_64-mtgpu_linux-xorg-release-hw-pdump"
    parameters:
      cmd: "./umd_build.sh -w xorg -p mtgpu_linux -r 1.0.0.0 -d 1 -o hw -b release -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v26"
      packageName: "x86_64-mtgpu_linux-xorg-release-hw-pdump"
  - job: "build.gr-umd"
    name: "x86_64-mtgpu_linux-xorg-release-hw-pdump-glvnd"
    parameters:
      cmd: "./umd_build.sh -w xorg -p mtgpu_linux -r 1.0.0.0 -d 1 -o hw -b release -g -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v26"
      packageName: "x86_64-mtgpu_linux-xorg-release-hw-pdump-glvnd"
  - job: "build.gr-umd"
    name: "x86_64-mtgpu_linux-xorg-release-vps"
    parameters:
      cmd: "./umd_build.sh -w xorg -p mtgpu_linux -r 1.0.0.0 -d 0 -o vps -b release -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v26"
      packageName: "x86_64-mtgpu_linux-xorg-release-vps"
  - job: "build.gr-umd"
    name: "x86_64-mtgpu_linux-xorg-release-vps-ph1"
    parameters:
      cmd: "./umd_build.sh -w xorg -p mtgpu_linux -r 1.0.0.0 -d 0 -o vps -b release -m SUPPORT_DMA_TRANSFER=1 -j9 --mt_arch=ph1"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v26"
      packageName: "x86_64-mtgpu_linux-xorg-release-vps-ph1"
  - job: "build.gr-umd"
    name: "arm64-mtgpu_linux-xorg-release-hw"
    parameters:
      cmd: "./umd_build.sh -w xorg -p mtgpu_linux -r 1.0.0.0 -d 0 -o hw -b release -a arm64 -c aarch64-linux-gnu- -j18"
      exports: "PATH=/toolchain/aarch64-linux-gnu-55/bin:$PATH"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v26"
      packageName: "arm64-mtgpu_linux-xorg-release-hw"
  - job: "build.gr-umd"
    name: "arm64-mtgpu_linux-xorg-release-hw-glvnd"
    parameters:
      cmd: "./umd_build.sh -w xorg -p mtgpu_linux -r 1.0.0.0 -d 0 -o hw -b release -a arm64 -c aarch64-linux-gnu- -g -j18"
      exports: "PATH=/toolchain/aarch64-linux-gnu-55/bin:$PATH"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v26"
      packageName: "arm64-mtgpu_linux-xorg-release-hw-glvnd"
  - job: "build.gr-umd"
    name: "arm64-mtgpu_linux-xorg-release-hw-armhf"
    parameters:
      cmd: "./umd_build.sh -w xorg -p mtgpu_linux -r 1.0.0.0 -d 0 -o hw -b release -a arm -c arm-linux-gnueabihf- -s arm-linux-gnueabihf- -g -j9"
      exports: "PATH=/toolchain/aarch64-linux-gnu-55/bin:$PATH"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v26"
      packageName: "arm64-mtgpu_linux-xorg-release-hw-armhf"
  - job: "build.gr-umd"
    name: "i386-mtgpu_linux-xorg-release-hw-wine"
    parameters:
      cmd: "./umd_build.sh -a i386 -t -g -w xorg -p mtgpu_linux -r 1.0.0.0 -o hw -b release -j18"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v26"
      packageName: "i386-mtgpu_linux-xorg-release-hw-wine"
tests:
  - job: "test.graphic_cts"
    name: "jenkins/gl_cts_test"
    parameters:
      testConfig: "{\"gl4.1\":{\"workdir\":\"/root/cts_gl\",\"binary\":\"glcts\",\"case\":\"https://oss.mthreads.com/sw-build/gr-umd/${branch}/test/cts/gl_cts_pr.txt\"}}"
  - job: "test.graphic_cts"
    name: "jenkins/gl_cs_cts_test"
    parameters:
      testConfig: "{\"gl4.2+\":{\"workdir\":\"/root/cts_gl\",\"binary\":\"glcts\",\"case\":\"https://oss.mthreads.com/sw-build/gr-umd/${branch}/test/cts/gl_cs_cts_pr.txt\"}}"
  - job: "test.graphic_cts"
    name: "jenkins/gles_cts_test"
    parameters:
      testConfig: "{\"gles\":{\"workdir\":\"/root/cts_gles\",\"binary\":\"glcts\",\"case\":\"https://oss.mthreads.com/sw-build/gr-umd/${branch}/test/cts/gles_cts_pr.txt\"}}"
  - job: "test.graphic_cts"
    name: "jenkins/vk_cts_test"
    parameters:
      testConfig: "{\"vk\":{\"workdir\":\"/root/cts_vk\",\"binary\":\"deqp-vk\",\"case\":\"https://oss.mthreads.com/sw-build/gr-umd/${branch}/test/cts/vk_cts_pr.txt\"}}"
  - job: "test.vps"
    name: "jenkins/quyuan2_vps_test"
    parameters:
      kmdPackageName: "x86_64-mtgpu_linux-xorg-release-vps.tar.gz"
      umdPackageName: "x86_64-mtgpu_linux-xorg-release-vps.tar.gz"
      model: "cmodel"
      chipType: "quyuan2"
  - job: "test.vps"
    name: "jenkins/ph1_vps_test"
    parameters:
      kmdPackageName: "x86_64-mtgpu_linux-xorg-release-vps.tar.gz"
      umdPackageName: "x86_64-mtgpu_linux-xorg-release-vps-ph1.tar.gz"
      model: "amodel"
      chipType: "ph1"
  - job: "test.renderdoc"
    name: "jenkins/renderdoc_test"
  - job: "test.piglit"
    name: "jenkins/piglit_test"
    parameters:
      openGL42CaseListUrl: "https://oss.mthreads.com/sw-build/gr-umd/${branch}/test/cts/piglit_lianying_pr.txt"
  - job: "test.glmark2"
    name: "jenkins/ubuntu_x86_64_glmark2_test"
    parameters:
      nodeLabel: "piglit_test"
      kmdPackageName: "x86_64-mtgpu_linux-xorg-release-hw.deb"
  - job: "test.pdump"
    name: "jenkins/pdump_test"
    parameters:
      umdPackageName: "x86_64-mtgpu_linux-xorg-release-hw-pdump-glvnd.tar.gz"
      kmdPackageName: "x86_64-mtgpu_linux-xorg-release-hw-pdump.deb"
  - job: "test.perf"
    name: "jenkins/linux perf test"
  - job: "test.mtgl_cts"
    name: "jenkins/linux-mtgl_cts"
  - job: "test.dma_transfer"
    name: "jenkins/linux dma_transfer test"
  - job: "test.musa_cts"
    name: "jenkins/musa test"
    parameters:
      runChoice: "node"
      testMark: "not musa_xorg"
      testType: "ddksmk"
