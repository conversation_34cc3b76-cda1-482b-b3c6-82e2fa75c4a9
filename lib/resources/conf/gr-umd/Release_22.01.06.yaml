builds:
  - job: "build.gr-umd"
    name: "x86_64-mtgpu_linux-nulldrmws-debug-hw"
    parameters:
      cmd: "./umd_build.sh -w nulldrmws -p mtgpu_linux -r 1.0.0.0 -d 0 -o hw -b debug -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v23"
      packageName: "x86_64-mtgpu_linux-nulldrmws-debug-hw"
  - job: "build.gr-umd"
    name: "x86_64-mtgpu_linux-nulldrmws-debug-vps"
    parameters:
      cmd: "./umd_build.sh -w nulldrmws -p mtgpu_linux -r 1.0.0.0 -d 0 -o vps -b debug -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v23"
      packageName: "x86_64-mtgpu_linux-nulldrmws-debug-vps"
  - job: "build.gr-umd"
    name: "x86_64-mtgpu_linux-nulldrmws-release-vps"
    parameters:
      cmd: "./umd_build.sh -w nulldrmws -p mtgpu_linux -r 1.0.0.0 -o vps -b release -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v23"
      packageName: "x86_64-mtgpu_linux-nulldrmws-release-vps"
  - job: "build.gr-umd"
    name: "x86_64-nohw_linux-nulldrmws-release-vps-pdump"
    parameters:
      cmd: "./umd_build.sh -w nulldrmws -p nohw_linux -r 1.0.0.0 -o vps -b release -d 1 -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v23"
      packageName: "x86_64-nohw_linux-nulldrmws-release-vps-pdump"
  - job: "build.gr-umd"
    name: "x86_64-mtgpu_linux-wayland-release-hw"
    parameters:
      cmd: "./umd_build.sh -w wayland -p mtgpu_linux -r 1.0.0.0 -d 0 -o hw -b release -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v23"
      packageName: "x86_64-mtgpu_linux-wayland-release-hw"
  - job: "build.gr-umd"
    name: "x86_64-mtgpu_linux-wayland-release-vps"
    parameters:
      cmd: "./umd_build.sh -w wayland -p mtgpu_linux -r 1.0.0.0 -d 0 -o vps -b release -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v23"
      packageName: "x86_64-mtgpu_linux-wayland-release-vps"
  - job: "build.gr-umd"
    name: "x86_64-mtgpu_linux-xorg-release-hw"
    parameters:
      cmd: "./umd_build.sh -w xorg -p mtgpu_linux -r 1.0.0.0 -m SUPPORT_DMA_TRANSFER=1 -d 0 -o hw -b release -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v23"
      packageName: "x86_64-mtgpu_linux-xorg-release-hw"
  - job: "build.gr-umd"
    name: "x86_64-mtgpu_linux-xorg-release-hw-pdump"
    parameters:
      cmd: "./umd_build.sh -w xorg -p mtgpu_linux -r 1.0.0.0 -d 1 -o hw -b release -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v23"
      packageName: "x86_64-mtgpu_linux-xorg-release-hw-pdump"
  - job: "build.gr-umd"
    name: "x86_64-mtgpu_linux-xorg-release-vps"
    parameters:
      cmd: "./umd_build.sh -w xorg -p mtgpu_linux -r 1.0.0.0 -d 0 -o vps -b release -j9"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v23"
      packageName: "x86_64-mtgpu_linux-xorg-release-vps"
  - job: "build.gr-umd"
    name: "x86_64-mtgpu_linux-xorg-release-vps-ph1"
    parameters:
      cmd: "./umd_build.sh -w xorg -p mtgpu_linux -r 1.0.0.0 -d 0 -o vps -b release -m SUPPORT_DMA_TRANSFER=1 -j9 --mt_arch=ph1"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v23"
      packageName: "x86_64-mtgpu_linux-xorg-release-vps-ph1"
  - job: "build.gr-umd"
    name: "arm64-mtgpu_linux-xorg-release-hw"
    parameters:
      cmd: "./umd_build.sh -w xorg -p mtgpu_linux -r 1.0.0.0 -m SUPPORT_DMA_TRANSFER=1 -d 0 -o hw -b release -a arm64 -c aarch64-linux-gnu- -j18"
      exports: "PATH=/toolchain/aarch64-linux-gnu-55/bin:$PATH"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v23"
      packageName: "arm64-mtgpu_linux-xorg-release-hw"
  - job: "build.gr-umd"
    name: "arm64-mtgpu_linux-xorg-release-hw-armhf"
    parameters:
      cmd: "./umd_build.sh -w xorg -p mtgpu_linux -r 1.0.0.0 -d 0 -o hw -b release -a arm -c arm-linux-gnueabihf- -s arm-linux-gnueabihf- -g -j9"
      exports: "PATH=/toolchain/aarch64-linux-gnu-55/bin:$PATH"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v23"
      packageName: "arm64-mtgpu_linux-xorg-release-hw-armhf"
  - job: "build.gr-umd"
    name: "i386-mtgpu_linux-xorg-release-hw-wine"
    parameters:
      cmd: "./umd_build.sh -a i386 -t -g -w xorg -p mtgpu_linux -r 1.0.0.0 -o hw -b release -j18"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v23"
      packageName: "i386-mtgpu_linux-xorg-release-hw-wine"
tests:
  - job: "test.dma_transfer"
    name: "dma_transfer test"
