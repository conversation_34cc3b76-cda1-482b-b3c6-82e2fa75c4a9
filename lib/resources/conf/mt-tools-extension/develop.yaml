buildImage: "sh-harbor.mthreads.com/sdk/msight:v12"
builds:
  - job: "build.mt-tools-extension"
    name: "mttx-x86_64"
    parameters:
      cmd: "python3 build.py --ci"
      output: "build/release-ci"
      relVersion: "v0.2"
      pkgName: "mt-tools-extension_x86_64"
  - job: "build.mt-tools-extension"
    name: "mttx-aarch64"
    parameters:
      cmd: "python3 build.py --ci --sys_proc aarch64"
      output: "build/release-ci"
      relVersion: "v0.2"
      pkgName: "mt-tools-extension_aarch64"
  - job: "build.mt-tools-extension"
    name: "mttx-loongarch64"
    parameters:
      cmd: "python3 build.py --ci --sys_proc loongarch64"
      output: "build/release-ci"
      relVersion: "v0.2"
      pkgName: "mt-tools-extension_loongarch64"
