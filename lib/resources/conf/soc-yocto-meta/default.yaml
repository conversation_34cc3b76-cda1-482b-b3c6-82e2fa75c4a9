defaultTestParameters:
  testNodeLabel: "m1000-aibook-tester"
  testRepoName: "apollo-test"
  testRepoBranch: "master"
  testMusaDeb: "https://oss.mthreads.com/product-release/release_M1000_1.1.0/20241125/musa_1.1.0-M1000_arm64.deb"
  upgradeScript: "m1000_test/upgrade/upgrade.sh ${packageName}"

builds:
  - job: "build.soc-yocto-meta"
    name: "aibook-uefi"
    parameters:
      cmd: "./meta/base/ci/build.sh --target aibook-6.6-ubuntu --output dist --debug false --clean true"
      output: "dist"
      packageName: "aibook-uefi-image.tar.gz"
  - job: "build.soc-yocto-meta"
    name: "t021-uefi"
    parameters:
      cmd: "./meta/base/ci/build.sh --target bringup-6.6-ubuntu --output dist --debug false --clean true"
      output: "dist"
      packageName: "t021-uefi-image.tar.gz"
tests:
  - job: "test.m1000-reboot-new"
    name: "aibook/reboot"
    parameters:
      packageName: "aibook-uefi-image.tar.gz"
      testScript: "m1000_test/cases/system-reboot.sh"
      bootTimeTestScript: "m1000_test/cases/boot-time/boot-time-test.sh"
      bootTimeTestLabel: "aibook/boot-time-test"
      s3TestScript: "m1000_test/cases/s3.sh"
      s3TestLabel: "aibook/s3-test"

tests.todo:
  - job: "test.m1000-cpu"
    name: "aibook/cpu-test"
    parameters:
      testScript: "m1000_test/cases/cpu/cpu-pytest-ci-tag.sh"
  - job: "test.m1000-ddr"
    name: "aibook/ddr-test"
    parameters:
      testScript: "m1000_test/cases/ddr/ddr-pytest-ci-tag.sh"
  - job: "test.m1000-disp-i2s"
    name: "aibook/disp-i2s-test"
    parameters:
      testScript: "m1000_test/cases/disp-i2s/disp-i2s-pytest-aibook-tag.sh"
  - job: "test.m1000-i2c"
    name: "aibook/i2c-test"
    parameters:
      packageName: "aibook-uefi-image.tar.gz"
      testScript: "m1000_test/cases/i2c/i2c-pytest-aibook-tag.sh"
  - job: "test.kernel-debug"
    name: "aibook/memory-check"
    parameters:
      packageName: "aibook-uefi-image.tar.gz"
      testScript: "m1000_test/cases/kernel/kernel-pytest-ci-tag.sh"
  - job: "test.m1000-npu"
    name: "aibook/npu-test"
    parameters:
      testScript: "m1000_test/cases/npu/npu-pytest-aibook-tag.sh"
  - job: "test.m1000-pcie"
    name: "aibook/pcie-test"
    parameters:
      testScript: "m1000_test/cases/pcie/aibook_pcie_test.sh"
  - job: "test.m1000-peri-i2s"
    name: "aibook/peri-i2s-test"
    parameters:
      testScript: "m1000_test/cases/peri-i2s/peri-i2s-pytest-aibook-tag.sh"
  - job: "test.m1000-usb"
    name: "aibook/usb-test"
    parameters:
      testScript: "m1000_test/cases/USB/usb-pytest-aibook-tag.sh"
  - job: "test.m1000-vpu"
    name: "aibook/vpu-test"
    parameters:
      testScript: "m1000_test/cases/vpu/vpu-pytest-ci-tag.sh"
