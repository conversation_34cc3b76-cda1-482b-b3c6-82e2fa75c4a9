builds:
  - job: "build.mtJPEG2000"
    buildAfterMerged: "true"
    name: "jenkins/linux-build"
    parameters:
      containerImage: "sh-harbor.mthreads.com/qa/linux-ddk:v14"
      mtccPackageUrl: "https://oss.mthreads.com/release-ci/computeQA/musa/newest/mtcc-nightly-x86_64-linux-gnu-ubuntu-20.04.tar.gz"
      cmd: "./build.sh -a 21,22; tar czf mtJPEG2000.tar.gz mtjpeg2k_sdk mtjpeg2k_test"
      mtJpegPackageName: "mtJPEG2000.tar.gz"
