builds:
  - job: "build.linux-ddk"
    name: "jenkins/linux-ddk-vulkan"
    parameters:
      containerImage: "sh-harbor.mthreads.com/qa/linux-ddk:v10"
      ddkBranch: "master"
      packageName: "ddk2.0_vulkan.tar.gz"
      cmd: "./ddk_build.sh -a 0 -V 2"
      uploadCase: "true"
      exports: "KERNELVER=5.4.0-42-generic\nKERNELDIR=/lib/modules/5.4.0-42-generic/build"
      submoduleConfig:  '{"shared_include":"head", "libgfxc":"head", "m3d":"head"}'
tests:
  - job: "test.graphic_cts"
    name: "jenkins/ddk2.0_vk_vulkan"
    parameters:
      nodeLabel: "(S80 || X300) && Ubuntu22.04 && x86_64"
      vulkanPackageName: "ddk2.0_vulkan.tar.gz"
      testConfig: '{"vk2":{"workdir":"/root/cts_vk_vulkan","binary":"deqp-vk","case":"https://oss.mthreads.com/sw-build/linux-ddk/${branch}/test/cts/cts_ci_linux.txt"}}'
      ddkBranch: "master"
