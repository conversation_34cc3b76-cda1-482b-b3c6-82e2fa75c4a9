builds:
  - job: "build.msight-compute"
    name: "linux/x86_64_external"
    parameters:
      cmd: "python3 build.py --ci --external --version=0.4.0"
      containerImage: "sh-harbor.mthreads.com/sdk/msight:v13"
      nodeLabel: "Linux_build"
      output: "target/Release/package"
      pkgName: "moore-perf-compute_${currentDate}_x86_64.tar.gz"
  - job: "build.msight-compute"
    name: "linux/x86_64_internal"
    parameters:
      cmd: "python3 build.py --ci --version=0.4.0"
      containerImage: "sh-harbor.mthreads.com/sdk/msight:v13"
      nodeLabel: "Linux_build"
      output: "target/Release/package"
      pkgName: "moore-perf-compute_${currentDate}_x86_64_internal.tar.gz"
  - job: "build.msight-compute"
    name: "linux/clang-tidy"
    parameters:
      cmd: "python3 build.py --ci --clang-tidy --version=0.4.0"
      containerImage: "sh-harbor.mthreads.com/sdk/msight:v13"
      nodeLabel: "Linux_build"
      output: "target/Release/package"
      pkgName: ""
  - job: "build.msight-compute"
    name: "linux/coverage"
    parameters:
      cmd: "python3 build.py --ci --coverage --version=0.4.0"
      containerImage: "sh-harbor.mthreads.com/sdk/msight:v13"
      nodeLabel: "Linux_build"
      output: "target/Release/package"
      pkgName: ""
      coverageReportPath: "build/coverageReport"
  - job: "build.msight-compute.win"
    name: "windows/x86_64_external"
    parameters:
      cmd: "python build.py --ci --external --version=0.4.0"
      buildPath: "msight-compute"
  - job: "build.msight-compute.win"
    name: "windows/x86_64_internal"
    parameters:
      cmd: "python build.py --ci --version=0.4.0"
      buildPath: "msight-compute"
