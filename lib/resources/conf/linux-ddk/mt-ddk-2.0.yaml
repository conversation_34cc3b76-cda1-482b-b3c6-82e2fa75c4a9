builds:
  - job: "build.linux-ddk"
    name: "jenkins/linux-ddk-build"
    parameters:
      containerImage: "sh-harbor.mthreads.com/qa/linux-ddk:v6"
      pkgName: "ddk2.0"
      cmd: "./ddk_build.sh"
      # add ddk info when triggered by submodule
      # ddkBranch: mt-ddk-2.0
      # submodules: '{"MUSA-Runtime":"head","fec-linux":"head","fec-trusted-firmware-a":"head","gpu-fw":"head","gr-kmd":"head","gr-umd":"head","libdrm-mt":"head","m3d":"head","media-driver":"head","ogl":"head","shared_include":"head"}'
tests:
  - job: "test.libdrm_ut"
    name: "jenkins/ddk2.0_libdrm_test"
    parameters:
      ddkBranch: "mt-ddk-2.0"
      caseListUrl: "https://oss.mthreads.com/sw-build/linux-ddk/${branch}/test/libdrm_mr_test_list.txt"
  - job: "test.gr-umd_ut"
    name: "jenkins/ddk2.0_umd_test"
    parameters:
      ddkBranch: "mt-ddk-2.0"
      caseListUrl: "https://oss.mthreads.com/sw-build/linux-ddk/${branch}/test/gr_umd_test_list.txt"
  - job: "test.perf"
    name: "jenkins/ddk2.0_perf_test"
    parameters:
      nodeLabel: "DDK2.0_perf"
      runGlmark2: "true"
      ratedGlmark2Score: "3500.0"
      runGlxgears: "true"
      ratedGlxgearsScore: "4200.0"
  - job: "test.graphic_cts"
    name: "jenkins/ddk2.0_gles_cts"
    parameters:
      nodeLabel: "DDK2.0_cts"
      testConfig: '{"gles2":{"workdir":"/root/cts_gles","binary":"glcts","case":"https://oss.mthreads.com/sw-build/linux-ddk/${branch}/test/cts/gles_cts_pr_ddk2.txt"}}'
  - job: "test.graphic_cts"
    name: "jenkins/ddk2.0_vk_cts"
    parameters:
      nodeLabel: "DDK2.0_cts"
      testConfig: '{"vk2":{"workdir":"/root/cts_vk","binary":"deqp-vk","case":"https://oss.mthreads.com/sw-build/linux-ddk/${branch}/test/cts/vk_cts_pr_ddk2.txt"}}'
  - job: "test.m3d_cts"
    name: "jenkins/ddk2.0_m3d_cts_test"
    parameters:
      linuxDdkBranch: ${branch}
      linuxDdkcommitId: ${commitId}
      testLabel: "jenkins/ddk2.0_m3d_cts_test"
      testMark: "-m 'drm_ut_test or m3d_ut_test or m3d_api_test'"
  - job: "test.m3d_musa_cts"
    name: "jenkins/ddk2.0_musa_cts_test"
    parameters:
      branch: "m3d_master"
      testType: "smokeM3d"
      testLabel: "jenkins/ddk2.0_musa_cts_test"
  - job: "test.ogl_cts"
    name: "linux/ogl_cts_Test_gl"
    parameters:
      testLabel: "linux/ogl_cts_Test_gl"
      dependency : 'https://oss.mthreads.com/release-ci/computeQA/m3d/m3d_ogl_cts_smoke_ci/vulkan-cts-gl.tar.gz'
      testConfig: '{"gl4.3": {"workdir": "build-mt-main","binary": "glcts","case": "https://oss.mthreads.com/sw-build/m3d/ogl/master/passlist-4.3.txt"}}'
  - job: "test.ogl_cts"
    name: "linux/ogl_cts_Test_es"
    parameters:
      testLabel: "linux/ogl_cts_Test_es"
      dependency : 'https://oss.mthreads.com/release-ci/computeQA/m3d/m3d_ogl_cts_smoke_ci/vulkan-cts-es.tar.gz'
      testConfig: '{"es4.2": {"workdir": "build-mt-opengl-cts-4.6.0.3","binary": "glcts","case": "https://oss.mthreads.com/sw-build/m3d/ogl/master/passlist-es-4.2.txt"}}'
  - job: "test.vaapi_fits"
    name: "linux/vpu_Test"
    parameters:
      testLabel: "linux/vpu_Test"
      testType : 'smokesubset'
