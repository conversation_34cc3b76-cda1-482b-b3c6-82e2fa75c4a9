builds:
  - job: "build.mthreads-gmi"
    buildAfterMerged: "true"
    name: "jenkins/build"
    parameters:
      containerImage: "sh-harbor.mthreads.com/sdk/management:v10"
  - job: "build.mthreads-gmi"
    name: "jenkins/clang-tidy"
    parameters:
      containerImage: "sh-harbor.mthreads.com/sdk/management:v11"
      clangTidyCmd: "./build_ci.sh DEBUG x86_64 0.1.0 NO HOST YES"
tests:
  - job: "test.mthreads-gmi"
    name: "jenkins/gmi_test"
    parameters:
      containerImage: "sh-harbor.mthreads.com/sdk/management:v10"
      runCov: "true"
