builds:
  - job: "build.management"
    name: "Debug_x86_64"
    parameters:
      cmd: "./build_ci.sh DEBUG x86_64"
      containerImage: "sh-harbor.mthreads.com/sdk/management:v10"
      runChoice: pod
      cluster: "shfarm"
      podNodeSelector: "In=mt=buildserver"
      podResources: "requests=cpu=8;requests=memory=8Gi;limits=cpu=8;limits=memory=8Gi"
      packageName: "mtml_Debug_x86_64"
      packagePath: "oss/sw-pr/management/PR/"
      isX86: "YES"
  - job: "build.management"
    name: "Release_x86_64"
    parameters:
      cmd: "./build_ci.sh RELEASE x86_64"
      containerImage: "sh-harbor.mthreads.com/sdk/management:v10"
      runChoice: pod
      cluster: "shfarm"
      podNodeSelector: "In=mt=buildserver"
      podResources: "requests=cpu=8;requests=memory=8Gi;limits=cpu=8;limits=memory=8Gi"
      packageName: "mtml_Release_x86_64"
      packagePath: "oss/sw-pr/management/PR/"
      isX86: "YES"
  - job: "build.management"
    name: "Debug_aarch64"
    parameters:
      cmd: "./build_ci.sh DEBUG aarch64"
      containerImage: "sh-harbor.mthreads.com/sdk/management:v10"
      runChoice: pod
      cluster: "shfarm"
      podNodeSelector: "In=mt=buildserver"
      podResources: "requests=cpu=8;requests=memory=8Gi;limits=cpu=8;limits=memory=8Gi"
      packageName: "mtml_Debug_aarch64"
      isX86: "NO"
  - job: "build.management"
    name: "Release_aarch64"
    parameters:
      cmd: "./build_ci.sh RELEASE aarch64"
      containerImage: "sh-harbor.mthreads.com/sdk/management:v10"
      runChoice: pod
      cluster: "shfarm"
      podNodeSelector: "In=mt=buildserver"
      podResources: "requests=cpu=8;requests=memory=8Gi;limits=cpu=8;limits=memory=8Gi"
      packageName: "mtml_Release_aarch64"
      isX86: "NO"
  - job: "build.management"
    name: "Debug_loongarch64"
    parameters:
      cmd: "./build_ci.sh DEBUG loongarch64"
      containerImage: "sh-harbor.mthreads.com/sdk/management:v10"
      runChoice: pod
      cluster: "shfarm"
      podNodeSelector: "In=mt=buildserver"
      podResources: "requests=cpu=8;requests=memory=8Gi;limits=cpu=8;limits=memory=8Gi"
      packageName: "mtml_Debug_loongarch64"
      isX86: "NO"
  - job: "build.management"
    name: "Release_loongarch64"
    parameters:
      cmd: "./build_ci.sh RELEASE loongarch64"
      containerImage: "sh-harbor.mthreads.com/sdk/management:v10"
      runChoice: pod
      cluster: "shfarm"
      podNodeSelector: "In=mt=buildserver"
      podResources: "requests=cpu=8;requests=memory=8Gi;limits=cpu=8;limits=memory=8Gi"
      packageName: "mtml_Release_loongarch64"
      isX86: "NO"
tests:
  - job: "test.management"
    name: "sudi_linux_mtml_test"
    parameters:
      nodeLabel: "(S50 || S30) && Ubuntu22.04 && x86_64"
      type: "sudi"
      runKmd: "false"
      runMpc: "false"
      runCov: "true"
      dockerImage: "sh-harbor.mthreads.com/sdk/management:v10"
      packagePath: "oss/sw-pr/management/PR/"
  - job: "test.management"
    name: "qy1_linux_mtml_test"
    parameters:
      nodeLabel: "(S80 || X300) && Ubuntu22.04 && x86_64"
      type: "qy1"
      runKmd: "false"
      runMpc: "false"
      runCov: "true"
      dockerImage: "sh-harbor.mthreads.com/sdk/management:v10"
      packagePath: "oss/sw-pr/management/PR/"
  - job: "test.management"
    name: "mpc_mtml_test"
    parameters:
      nodeLabel: "(S80 || X300) && Ubuntu22.04 && x86_64"
      type: "qy1"
      runKmd: "false"
      runMpc: "true"
      runCov: "true"
      dockerImage: "sh-harbor.mthreads.com/sdk/management:v10"
      packagePath: "oss/sw-pr/management/PR/"
  - job: "test.mtml_vdi"
    name: "vdi_linux_mtml_test"
    parameters:
      nodeLabel: "Linux_VDI"
      vdiBranch: "vgpu_2.7.0_release"
      runKmd: "false"
      runCov: "true"
      dockerImage: "sh-harbor.mthreads.com/sdk/management:v10"
