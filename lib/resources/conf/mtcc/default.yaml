# Reusable test groups
test_groups:
  mtcc_tests:
    gfxc_tests: &gfxc_tests
      name: "gfxc_shader_tests"
      timelimit: 30
      commitId: cc4b9de7
      script: "gfxc_shader_tests.py"
  wddm_tests:
    hw: &hw_wddm_tests
      S50:
        - name: d3d_test
          caselist: caseslist_dx12.txt
          timelimit: 20
        - name: dx9Sdk_test
          caselist: sdk_cases_list.txt
          timelimit: 5
        - name: dx10Sdk_test
          caselist: dx10sdk_cases_list.txt
          timelimit: 5
        - name: dx11Sdk_test
          caselist: dx11sdk_cases_list.txt
          timelimit: 5
        - name: apitrace_test
          caselist: trace_cases_list.txt
          timelimit: 30
        - name: compiler_test
          caselist: compiler_ci.txt
          timelimit: 20
        - name: renderdoc_test
          caselist: rdc_cases_list_dx12.txt
          timelimit: 20
      S80:
        - name: whlk_test
          caselist: whlk_case_list.csv
          timelimit: 50
        - name: whlk_dx12_test
          caselist: dxc_whql_case_list.csv
          timelimit: 35
      S90:
        - name: pix_apitrace_test
          timelimit: 10
    vps: &vps_wddm_tests
      ph1_amodel:
        - name: d3d_test
          caselist: caseslist_ph1_vps_ci.txt
          timelimit: 30
      ph1_cmodel:
        - name: d3d_test
          caselist: caseslist_ph1_vps_cmodel_ci.txt
          timelimit: 30
      hg_amodel:
        - name: d3d_test
          caselist: caseslist_hg_vps_ci.txt
          timelimit: 30
        - name: whlk_partA_test
          caselist: whlk_case_list_hg_amodel_vps_ci_partA.csv
          timelimit: 120
        - name: whlk_partB_test
          caselist: whlk_case_list_hg_amodel_vps_ci_partB.csv
          timelimit: 120
        - name: whlk_partC_test
          caselist: whlk_case_list_hg_amodel_vps_ci_partC.csv
          timelimit: 120
    daily_hw: &daily_hw_wddm_tests
      S50:
        - name: apitrace_test
          caselist: daily_trace_case_list.txt
          timelimit: 120
        - name: compiler_test
          caselist: daily_test.txt
          timelimit: 120
        - name: d3d_test
          caselist: caseslist.txt
          timelimit: 20
        - name: renderdoc_test
          caselist: rdc_cases_list.txt
          timelimit: 20
        - name: whlk_Geometry_test
          timelimit: 5
        - name: yuanshenTrace_test
          timelimit: 5
      S80:
        - name: renderdoc_test
          caselist: rdc_daily_cases_list_dx12.txt
          timelimit: 40

packageName:
  ossSavePath:
    libgfxc_mr: &libgfxc_mr_ossPath "sw-pr/mtcc/mr"
    libgfxc_push: &libgfxc_push_ossPath "sw-pr/mtcc/push"
    libgfxc_daily: &libgfxc_daily_ossPath "sw-pr/mtcc/daily"
  libgfxc: &libgfxc_packageName
    name: "musa_compiler_shared"
    path: "libgfxc/gfx"
  wddm:
    mr: &wddm_mr_packageName "mtcc_mr_wddm"
    push: &wddm_push_packageName "mtcc_push_wddm"
    daily: &wddm_daily_packageName "mtcc_daily_wddm"

mr:
  builds:
    - job: "build.libgfxc.linux"
      name: "jenkins/build_libgfxc_linux"
      parameters:
        containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v36"
        packageReName: &libgfxc_linux_pac "musa_compiler_shared_linux"
        package: *libgfxc_packageName
        ossPath: *libgfxc_mr_ossPath
        cmd: "python cmake_mtgfxc.py -p 1 -j 16 --test-rule basic -s ${shared_include_path}"
      tests:
        - job: "test.libgfxc.linux"
          name: "jenkins/test_libgfxc_linux"
          parameters:
            testType: "gfxc_shader_tests"
            containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v36"
            ossPath: *libgfxc_mr_ossPath
            package: *libgfxc_linux_pac
            testGroups: *gfxc_tests
    - job: "build.libgfxc.windows"
      name: "jenkins/build_libgfxc_windows"
      parameters:
        packageReName: &libgfxc_win_pac "musa_compiler_shared_win"
        package: *libgfxc_packageName
        ossPath: *libgfxc_mr_ossPath
        cmd: "python cmake_mtgfxc.py -a x64 Win32 -p 1 --test-rule basic -s ${shared_include_path}"
        buildDriver: 'hw,vps'
        wddmDriverName: *wddm_mr_packageName
      tests:
        - job: "test.libgfxc.windows"
          name: "jenkins/test_libgfxc_windows_hw"
          parameters:
            testType: "wddm_hw"
            ossPath: *libgfxc_mr_ossPath
            package: *wddm_mr_packageName
            testGroups: *hw_wddm_tests
            infFile: "MT-GEN1-ENCODE"
        - job: "test.libgfxc.windows"
          name: "jenkins/test_libgfxc_windows_hw_oglmtcc"
          parameters:
            testType: "ogl_mtcc_tests"
            ossPath: *libgfxc_mr_ossPath
            package: *wddm_mr_packageName
        - job: "test.libgfxc.windows"
          name: "jenkins/test_libgfxc_windows_hg_vps"
          parameters:
            testType: "wddm_vps"
            ossPath: *libgfxc_mr_ossPath
            package: *wddm_mr_packageName
            testGroups: *vps_wddm_tests
            infFile: "MT-VPS-HG-Amodel"

push:
  builds:
    - job: "build.libgfxc.linux"
      name: "jenkins/build_libgfxc_linux_push"
      parameters:
        containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v36"
        packageReName: "musa_compiler_shared_linux"
        package: *libgfxc_packageName
        ossPath: *libgfxc_push_ossPath
        cmd: "python cmake_mtgfxc.py -p 1 -j 16 --test-rule basic -s ${shared_include_path}"
    - job: "build.libgfxc.windows"
      name: "jenkins/build_libgfxc_windows"
      parameters:
        packageReName: "musa_compiler_shared_win_push"
        package: *libgfxc_packageName
        ossPath: *libgfxc_push_ossPath
        cmd: "python cmake_mtgfxc.py -a x64 Win32 -p 1 --test-rule basic -s ${shared_include_path}"
        buildDriver: 'hw,vps'
        wddmDriverName: *wddm_push_packageName

daily:
  builds:
    - job: "build.libgfxc.linux"
      name: "jenkins/build_libgfxc_linux_daily"
      parameters:
        containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v36"
        packageReName: &libgfxc_linux_dpac "musa_compiler_shared_linux"
        package: *libgfxc_packageName
        ossPath: *libgfxc_daily_ossPath
        cmd: "python cmake_mtgfxc.py -p 1 -j 16 --test-rule basic -s ${shared_include_path}"
      tests:
        - job: "test.libgfxc.linux"
          name: "jenkins/test_libgfxc_linux"
          parameters:
            testType: "gfxc_shader_tests"
            containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v36"
            ossPath: *libgfxc_daily_ossPath
            package: *libgfxc_linux_pac
            testGroups: *gfxc_tests
    - job: "build.libgfxc.linux"
      name: "jenkins/build_libgfxc_linux_release_branch_daily"
      parameters:
        containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v36"
        packageReName: &libgfxc_linux_dpac_rb "musa_compiler_shared_linux_release_branch"
        package: *libgfxc_packageName
        ossPath: *libgfxc_daily_ossPath
        cmd: "python cmake_mtgfxc.py -p 1 -j 16 --no-test -s ${shared_include_path} -d OFF"
    - job: "build.libgfxc.windows"
      name: "jenkins/build_libgfxc_windows_release_branch_daily"
      parameters:
        packageReName: &libgfxc_win_dpac_rb "musa_compiler_shared_win_release_branch"
        package: *libgfxc_packageName
        ossPath: *libgfxc_daily_ossPath
        cmd: "python cmake_mtgfxc.py -a x64 Win32 -p 1 --no-test -s ${shared_include_path} -d OFF"
    - job: "build.libgfxc.windows"
      name: "jenkins/build_libgfxc_windows_daily"
      parameters:
        packageReName: &libgfxc_win_dpac "musa_compiler_shared_win"
        package: *libgfxc_packageName
        ossPath: *libgfxc_daily_ossPath
        cmd: "python cmake_mtgfxc.py -a x64 Win32 -p 1 --test-rule basic -s ${shared_include_path}"
        buildDriver: 'hw,vps'
        wddmDriverName: *wddm_daily_packageName
      tests:
        - job: "test.libgfxc.windows"
          name: "jenkins/test_libgfxc_windows_hw"
          parameters:
            testType: "wddm_hw"
            ossPath: *libgfxc_daily_ossPath
            package: *wddm_daily_packageName
            testGroups: *daily_hw_wddm_tests
            infFile: "MT-GEN1-ENCODE"
        - job: "test.libgfxc.windows"
          name: "jenkins/test_libgfxc_windows_hw"
          parameters:
            testType: "ogl_mtcc_tests"
            ossPath: *libgfxc_daily_ossPath
            package: *wddm_daily_packageName
        - job: "test.libgfxc.windows"
          name: "jenkins/test_libgfxc_windows_hg_vps"
          parameters:
            testType: "wddm_vps"
            ossPath: *libgfxc_daily_ossPath
            package: *wddm_daily_packageName
            testGroups: *vps_wddm_tests
            infFile: "MT-VPS-HG-Amodel"
  update_packages:
    ossPath: *libgfxc_daily_ossPath
    packageRepo: "graphics-compiler-package"
    packageName: *libgfxc_packageName
    packages:
      - name: *libgfxc_linux_dpac
        path: "Linux"
      - name: *libgfxc_win_dpac
        path: "Win"
      - name: *libgfxc_win_dpac_rb
        path: "Win_Release"
      - name: *libgfxc_linux_dpac_rb
        path: "Linux_Release"
