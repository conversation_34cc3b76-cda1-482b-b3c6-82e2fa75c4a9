tests:
  - job: "test.vaapi_fits"
    name: "linux/PH1_vpu_Test"
    parameters:
      branch: "scj_dev"
      testType : 'phci'
      containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-video:v7"
      sleepBeforeModprobe: "120"
      modprobeOptions: "options mtgpu mtgpu_drm_major=2"
      linuxDdkBranch: "release_KUAE_2.0_for_PH1_M3D"
  - job: "test.m3d_musa_cts"
    name: "jenkins/ph1_musa_cts_test"
    parameters:
      branch: "m3d_master"
      testType: "smokeM3d"
      testLabel: "jenkins/ph1_musa_cts_test"
      envExport: export MUSA_INSTALL_PATH=/usr/local/musa;export TEST_TYPE=$testType;export PATH=/usr/local/musa/bin:$PATH;export LD_LIBRARY_PATH=/usr/local/musa/lib:/usr/lib/x86_64-linux-gnu/musa/:/usr/local/lib/musa/lib/x86_64-linux-gnu/:/usr/lib/x86_64-linux-gnu/:${LD_LIBRARY_PATH};export TIMEOUT_FACTOR=4;
      gpuArch: "mp_31"
      modprobeOptions: "options mtgpu compute_only=1 enable_mem_stats=1"
      sleepBeforeModprobe: "120"
      linuxDdkBranch: "release_KUAE_2.0_for_PH1_M3D"
  - job: "test.mtcc"
    name: "jenkins/test_linux_mtcc_ph1"
    parameters:
      testType: "smoke"
      testArgs: "--device=ph1"
      linuxDdkBranch: "release_KUAE_2.0_for_PH1_M3D"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "node"
  - job: "test.muRAND_cts"
    name: "jenkins/test_murand_cts_ph1"
    parameters:
      linuxDdkBranch: "release_KUAE_2.0_for_PH1_M3D"
      testType: "smoke"
      compileArgs: "-DMUSA_ARCHS=31 -DDISABLE_FP64=OFF -DDISABLE_8_16_BIT=ON"
      testArgs: "-m murand"
      containerImage: "sh-harbor.mthreads.com/qa/murand_test-ubuntu-22-04:v2"
      runChoice: "node"
  - job: "test.muAlg_cts"
    name: "jenkins/test_muAlg_cts_ph1"
    parameters:
      linuxDdkBranch: "release_KUAE_2.0_for_PH1_M3D"
      compileArgs: "-DMUSA_ARCHS=31"
      testType: "no test"
      testArgs: "-m mualg"
      containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
      runChoice: "node"
  - job: "test.muBLAS_cts"
    name: "jenkins/test_muBLAS_cts_ph1"
    parameters:
      linuxDdkBranch: "release_KUAE_2.0_for_PH1_M3D"
      testType: "smoke"
      packageDependency: '{"muBLASPackageUrl":"muBLAS.tar.gz"}'
      containerImage: "sh-harbor.mthreads.com/qa/mublas_test-ubuntu-22-04:v2"
      runChoice: "node"
  - job: "test.muThrust_cts"
    name: "jenkins/test_muThrust_cts_ph1"
    parameters:
      linuxDdkBranch: "release_KUAE_2.0_for_PH1_M3D"
      compileArgs: "-DMUSA_ARCHS=31"
      testType: "no test"
      testArgs: "-m muthrust"
      containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
      runChoice: "node"
  - job: "test.muFFT_cts"
    name: "jenkins/test_muFFT_cts_ph1"
    parameters:
      linuxDdkBranch: "release_KUAE_2.0_for_PH1_M3D"
      testType: "smoke"
      packageDependency: '{"muFFTPackageUrl":"muFFT.tar.gz"}'
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "node"
  - job: "test.mudnn_cts"
    name: "jenkins/test_mudnn_cts_ph1"
    parameters:
      linuxDdkBranch: "release_KUAE_2.0_for_PH1_M3D"
      test_type: "smoke"
      test_mark: "mudnn"
      packageDependency: '{"muDNNPackageUrl":"mudnn_dev2.8.0_mp31.tar.gz"}'
      runChoice: "node"
