builds:
  - job: "build.libgfxcLinux"
    name: "jenkins/buildlibgfxcAndroid"
    parameters:
      branch: "master"
      containerImage: "sh-harbor.mthreads.com/qa/linux-ddk:v23"
      testType: "daily"
      linuxDdkBuild: "false"
      linuxDdkBranch: "master"
      libgfxcPakcageRepo: "graphics-compiler-package"
      libgfxcPakcagebBranch: "master"
      libgfxcBuildCmd: "python cmake_mtgfxc.py --system Android -a aarch64 armhf -p -j 16"
      ossPath: "oss/release-ci/computeQA/libgfxcbuild"
  - job: "build.libgfxcLinux"
    name: "jenkins/buildlibgfxcLinux"
    parameters:
      branch: "master"
      containerImage: "sh-harbor.mthreads.com/qa/linux-ddk:v23"
      testType: "daily"
      linuxDdkBuild: "false"
      linuxDdkBranch: "master"
      libgfxcPakcageRepo: "graphics-compiler-package"
      libgfxcPakcagebBranch: "master"
      libgfxcBuildCmd: "python cmake_mtgfxc.py -a x86_64 x86 aarch64 armhf -p -j 16 --upload_dbgsym"
      ossPath: "oss/release-ci/computeQA/libgfxcbuild"
