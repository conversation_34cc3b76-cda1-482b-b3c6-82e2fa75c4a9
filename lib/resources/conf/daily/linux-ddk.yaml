builds:
  - job: "build.linux-ddk"
    name: "linux-ddk-musa-trace-on build"
    parameters:
      containerImage: "sh-harbor.mthreads.com/qa/linux-ddk:v6"
      packageName: "ddk2.0_musa_trace_on"
      cmd: "./ddk_build.sh -a 0 -r 3"
      ddkBranch: "mt-ddk-2.0"
      submoduleConfig: '{"gr-umd": "head", "libdrm-mt": "head", "MUSA-Runtime": "head", "shared_include": "head", "m3d": "head"}'
      buildChoice: "tar.gz"
tests:
  - job: "test.m3d_cts"
    name: "linux/m3d cts test"
    parameters:
      linuxDdkBranch: "mt-ddk-2.0"
      testLabel: "linux/m3d cts test"
      testMark: "-m 'drm_ut_test or m3d_ut_test or m3d_api_test'"
      testType: "daily"
  - job: "test.m3d_musa_cts"
    name: "linux/m3d musa cts test"
    parameters:
      branch: "m3d_master"
      testType: "dailyM3d"
      testLabel: "linux/m3d musa cts test"
  - job: "test.m3d_musa_cts"
    name: "linux/m3d mttrace musa cts test"
    parameters:
      musaRuntimePackageName: "ddk2.0_musa_trace_on.tar.gz"
      branch: "m3d_master"
      testType: "mttraceDaily"
      mtTraceCaptureUrl: "https://oss.mthreads.com/release-ci/DiagSys/pre-silicon/stable/mt_trace_capturer"
      envExport: "export MUSA_INSTALL_PATH=/usr/local/musa;export TEST_TYPE=$testType;export PATH=/usr/local/musa/bin:$PATH;export LD_LIBRARY_PATH=/usr/local/musa/lib:/usr/lib/x86_64-linux-gnu/musa/:/usr/local/lib/musa/lib/x86_64-linux-gnu/:/usr/lib/x86_64-linux-gnu/:${LD_LIBRARY_PATH};export TIMEOUT_FACTOR=4;export MTTRACE_ENABLE=1"
      traceFileSavePath: "oss/sw-build/m3d/daily/${date}/"
      testLabel: "linux/m3d mttrace musa cts test"
  - job: "test.ogl_cts"
    name: "linux/ogl_cts_Test_gl"
    parameters:
      testLabel: "linux/ogl_cts_Test_gl"
      dependency : 'https://oss.mthreads.com/release-ci/computeQA/m3d/m3d_ogl_cts_smoke_ci/vulkan-cts-gl.tar.gz'
      testConfig: '{"gl4.3": {"workdir": "build-mt-main","binary": "glcts","case": "https://oss.mthreads.com/sw-build/m3d/ogl/master/passlist-4.3.txt"}}'
  - job: "test.ogl_cts"
    name: "linux/ogl_cts_Test_es"
    parameters:
      testLabel: "linux/ogl_cts_Test_es"
      dependency : 'https://oss.mthreads.com/release-ci/computeQA/m3d/m3d_ogl_cts_smoke_ci/vulkan-cts-es.tar.gz'
      testConfig: '{"es4.2": {"workdir": "build-mt-opengl-cts-4.6.0.3","binary": "glcts","case": "https://oss.mthreads.com/sw-build/m3d/ogl/master/passlist-es-4.2.txt"}}'
  - job: "test.vaapi_fits"
    name: "linux/vpu_Test_smokesubset"
    parameters:
      testLabel: "linux/vpu_Test"
      testType : 'smokesubset'
  - job: "daily.vaapi_fits_test"
    name: "linux/vpu_Test_x300"
    parameters:
      testLabel: "linux/vpu_Test"
      testType : 'daily'
      TIMEOUT: '360'
  - job: "daily.vaapi_fits_test"
    name: "linux/vpu_Test_s90"
    parameters:
        branch: "scj_dev"
        testLabel: "linux/S90_vpu_Test"
        testType : 's4000'
        nodeLabel: '172.31.8.15'
        containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-video:v7"
        TIMEOUT: '360'
  - job: "daily.vaapi_fits_test"
    name: "linux/vpu_Test_s4000"
    parameters:
        branch: "scj_dev"
        testLabel: "linux/S4000_vpu_Test"
        testType : 's4000'
        nodeLabel: 'S4000_video_test'
        containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-video:v7"
        TIMEOUT: '360'
  - job: "daily.vaapi_fits_test"
    name: "linux/vpu_Test_s50"
    parameters:
        branch: "scj_dev"
        testLabel: "linux/S50_vpu_Test"
        testType : 's50'
        nodeLabel: 'S50_video_test'
        containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-video:v7"
        TIMEOUT: '480'
  - job: "daily.vaapi_fits_test"
    name: "linux/vpu_Test_s30"
    parameters:
        branch: "scj_dev"
        testLabel: "linux/S30_vpu_Test"
        testType : 's30'
        nodeLabel: 'S30_video_test'
        containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-video:v7"
        TIMEOUT: '360'
  - job: "daily.vaapi_fits_test"
    name: "linux/vpu_Test_s10"
    parameters:
        branch: "scj_dev"
        testLabel: "linux/S10_vpu_Test"
        testType : 's10'
        nodeLabel: 'S10_video_test'
        containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-video:v7"
        TIMEOUT: '360'
