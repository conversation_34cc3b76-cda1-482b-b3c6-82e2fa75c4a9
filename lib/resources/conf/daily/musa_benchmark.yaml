tests:
  - job: "test.musa_benchmark"
    name: "jenkins/musa_perf_8_15_release_KUAE_2.0_for_PH1_M3D"
    parameters:
      branch: "m3d_master"
      testMark: "musa_benchmarks_mr_perf_on_m3d"
      containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-musa-perf-image:v5"
      testType: "daily"
      nodeLabel: "S90_Ubuntu22.04_x86_64_8.15"
      testLabel: "musa_perf"
      dailyDdkBranch: "release_KUAE_2.0_for_PH1_M3D"
  - job: "test.musa_benchmark"
    name: "jenkins/musa_perf_8_17_release_KUAE_2.0_for_PH1_M3D"
    parameters:
      branch: "m3d_master"
      testMark: "musa_benchmarks_mr_perf_on_m3d"
      containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-musa-perf-image:v5"
      testType: "daily"
      nodeLabel: "S90_Ubuntu22.04_x86_64_8.17"
      testLabel: "musa_perf"
      dailyDdkBranch: "release_KUAE_2.0_for_PH1_M3D"
  - job: "test.musa_benchmark"
    name: "jenkins/musa_perf_40_68_release_KUAE_2.0_for_PH1_M3D"
    parameters:
      branch: "m3d_master"
      testMark: "musa_benchmarks_mr_perf_on_m3d"
      containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-musa-perf-image:v5"
      testType: "daily"
      nodeLabel: "S5000_Ubuntu22.04_x86_64_40.68"
      testLabel: "PH1_musa_perf"
      dailyDdkBranch: "release_KUAE_2.0_for_PH1_M3D"
      envExport: "export MUSA_INSTALL_PATH=/usr/local/musa;export TEST_TYPE=$testType;export PATH=/usr/local/musa/bin:$PATH;export  LD_LIBRARY_PATH=/usr/local/musa/lib:/usr/lib/x86_64-linux-gnu/musa/:/usr/local/lib/musa/lib/x86_64-linux-gnu/:/usr/lib/x86_64-linux-gnu/:${LD_LIBRARY_PATH};"
  - job: "test.musa_benchmark"
    name: "jenkins/musa_perf_40_38_release_KUAE_2.0_for_PH1_M3D"
    parameters:
      branch: "m3d_master"
      testMark: "musa_benchmarks_mr_perf_on_m3d"
      containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-musa-perf-image:v5"
      testType: "daily"
      nodeLabel: "S5000_Ubuntu22.04_x86_64_40.38"
      testLabel: "PH1_musa_perf"
      dailyDdkBranch: "release_KUAE_2.0_for_PH1_M3D"
      envExport: "export MUSA_INSTALL_PATH=/usr/local/musa;export TEST_TYPE=$testType;export PATH=/usr/local/musa/bin:$PATH;export  LD_LIBRARY_PATH=/usr/local/musa/lib:/usr/lib/x86_64-linux-gnu/musa/:/usr/local/lib/musa/lib/x86_64-linux-gnu/:/usr/lib/x86_64-linux-gnu/:${LD_LIBRARY_PATH};export MUSA_USERQ=1;"
  - job: "test.musa_benchmark"
    name: "jenkins/musa_perf_40_33_release_KUAE_2.0_for_PH1_M3D"
    parameters:
      branch: "m3d_master"
      testMark: "musa_benchmarks_mr_perf_on_m3d"
      containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-musa-perf-image:v5"
      testType: "daily"
      nodeLabel: "S5000_Ubuntu22.04_x86_64_40.33"
      testLabel: "PH1_musa_perf"
      dailyDdkBranch: "release_KUAE_2.0_for_PH1_M3D"
      envExport: "export MUSA_INSTALL_PATH=/usr/local/musa;export TEST_TYPE=$testType;export PATH=/usr/local/musa/bin:$PATH;export  LD_LIBRARY_PATH=/usr/local/musa/lib:/usr/lib/x86_64-linux-gnu/musa/:/usr/local/lib/musa/lib/x86_64-linux-gnu/:/usr/lib/x86_64-linux-gnu/:${LD_LIBRARY_PATH};export MUSA_USERQ=1;"
  - job: "test.musa_benchmark"
    name: "jenkins/musa_perf_40_48_release_KUAE_2.0_for_PH1_M3D"
    parameters:
      branch: "m3d_master"
      testMark: "musa_benchmarks_mr_perf_on_m3d"
      containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-musa-perf-image:v5"
      testType: "daily"
      nodeLabel: "S5000_Ubuntu22.04_x86_64_40.48"
      testLabel: "PH1_musa_perf_nouserq"
      dailyDdkBranch: "release_KUAE_2.0_for_PH1_M3D"
      envExport: "export MUSA_INSTALL_PATH=/usr/local/musa;export TEST_TYPE=$testType;export PATH=/usr/local/musa/bin:$PATH;export  LD_LIBRARY_PATH=/usr/local/musa/lib:/usr/lib/x86_64-linux-gnu/musa/:/usr/local/lib/musa/lib/x86_64-linux-gnu/:/usr/lib/x86_64-linux-gnu/:${LD_LIBRARY_PATH};export MUSA_USERQ=0;"
  - job: "test.musa_benchmark"
    name: "jenkins/musa_perf_8_15_master"
    parameters:
      branch: "m3d_master"
      testMark: "musa_benchmarks_mr_perf_on_m3d"
      containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-musa-perf-image:v5"
      testType: "daily"
      nodeLabel: "S90_Ubuntu22.04_x86_64_8.15"
      testLabel: "musa_perf"
      dailyDdkBranch: "master"
  - job: "test.musa_benchmark"
    name: "jenkins/musa_perf_8_17_master"
    parameters:
      branch: "m3d_master"
      testMark: "musa_benchmarks_mr_perf_on_m3d"
      containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-musa-perf-image:v5"
      testType: "daily"
      nodeLabel: "S90_Ubuntu22.04_x86_64_8.17"
      testLabel: "musa_perf"
      dailyDdkBranch: "master"
  - job: "test.musa_benchmark"
    name: "jenkins/musa_perf_40_38_master"
    parameters:
      branch: "m3d_master"
      testMark: "musa_benchmarks_mr_perf_on_m3d"
      containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-musa-perf-image:v5"
      testType: "daily"
      nodeLabel: "S5000_Ubuntu22.04_x86_64_40.38"
      testLabel: "PH1_musa_perf"
      dailyDdkBranch: "master"
  - job: "test.musa_benchmark"
    name: "jenkins/musa_perf_40_33_master"
    parameters:
      branch: "m3d_master"
      testMark: "musa_benchmarks_mr_perf_on_m3d"
      containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-musa-perf-image:v5"
      testType: "daily"
      nodeLabel: "S5000_Ubuntu22.04_x86_64_40.33"
      testLabel: "PH1_musa_perf"
      dailyDdkBranch: "master"
  - job: "test.musa_benchmark"
    name: "jenkins/musa_perf_40_68_master"
    parameters:
      branch: "m3d_master"
      testMark: "musa_benchmarks_mr_perf_on_m3d"
      containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-musa-perf-image:v5"
      testType: "daily"
      nodeLabel: "S5000_Ubuntu22.04_x86_64_40.68"
      testLabel: "PH1_musa_perf"
      dailyDdkBranch: "master"
  - job: "test.musa_benchmark"
    name: "jenkins/musa_perf_40_48_master"
    parameters:
      branch: "m3d_master"
      testMark: "musa_benchmarks_mr_perf_on_m3d"
      containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-musa-perf-image:v5"
      testType: "daily"
      nodeLabel: "S5000_Ubuntu22.04_x86_64_40.48"
      testLabel: "PH1_musa_perf_userq"
      dailyDdkBranch: "master"
      envExport: "export MUSA_INSTALL_PATH=/usr/local/musa;export TEST_TYPE=$testType;export PATH=/usr/local/musa/bin:$PATH;export  LD_LIBRARY_PATH=/usr/local/musa/lib:/usr/lib/x86_64-linux-gnu/musa/:/usr/local/lib/musa/lib/x86_64-linux-gnu/:/usr/lib/x86_64-linux-gnu/:${LD_LIBRARY_PATH};export MUSA_USERQ=1;"
