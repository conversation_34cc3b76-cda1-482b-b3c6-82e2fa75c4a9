builds:
  # - job: "build.mt-rm"
  #   name: "jenkins/linux-mt-rm"
  #   parameters:
  #     containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v45"
  #     rmInKmd: "false"
  - job: "build.mt-rm"
    name: "jenkins/linux-mt-rm_kmd"
    parameters:
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v45"
      cmd: "./kmd_build.sh -r 1.0.0.0 -p mtgpu_linux -w xorg -o hw -j32 -b release -d 0 -g deb RM_ENABLE=1"
      rmInKmd: "true"
      kmdBranch: "develop"
