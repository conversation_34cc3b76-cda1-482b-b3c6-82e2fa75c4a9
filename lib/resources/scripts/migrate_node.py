# import json
# import os

# from jen<PERSON> import <PERSON>

# old_server = <PERSON>(
#     "http://sh-jenkins.mthreads.com/", username="tingjun.shen-ext", password="Stj752081"
# )
# new_server = <PERSON>(
#     "http://swqa-jks.mthreads.com/",
#     username="admin",
#     password="97f6a92e818d4c11870c3b9986c061e7",
# )


# def export_nodes():
#     os.makedirs("nodes", exist_ok=True)
#     for node in old_server.get_nodes():
#         if node["name"] not in ["master", "Built-In Node"]:
#             with open(f"nodes/{node['name']}.json", "w") as f:
#                 json.dump(node, f)
#             print(f"export: {node['name']}")


# def import_nodes():
#     for node_file in os.listdir("nodes"):
#         with open(f"nodes/{node_file}", "r") as f:
#             node_info = json.load(f)
#             node_info.pop("name", None)
#             node_info.pop("offline", None)
#             node_name = node_file[:-5]
#             if new_server.node_exists(node_name):
#                 new_server.reconfig_node(node_name, node_info)
#                 print(f"update: {node_name}")
#             else:
#                 new_server.create_node(node_name, **node_info)
#                 print(f"import: {node_name}")


# def offline_nodes():
#     for node_file in os.listdir("nodes"):
#         node_name = node_file[:-5]
#         if new_server.node_exists(node_name):
#             new_server.disable_node(node_name)
#             print(f"Set Offline: {node_name}")


# if __name__ == "__main__":
#     export_nodes()
#     import_nodes()
#     offline_nodes()

import re

import requests
from requests.auth import HTTPBasicAuth

# 配置参数
SOURCE_JENKINS_URL = "http://sh-jenkins.mthreads.com"
SOURCE_JENKINS_USERNAME = "tingjun.shen-ext"
SOURCE_JENKINS_API_TOKEN = ""
TARGET_JENKINS_URL = "http://swqa-jks.mthreads.com"
TARGET_JENKINS_USERNAME = "admin"
TARGET_JENKINS_API_TOKEN = ""
REGEX_PATTERN = "^S50|S80|X300|CI_|"  # 修改为你的正则规则


# 获取源 Jenkins 的节点列表
def get_nodes_from_jenkins(jenkins_url):
    url = f"{jenkins_url}/computer/api/json"
    response = requests.get(
        url, auth=HTTPBasicAuth(SOURCE_JENKINS_USERNAME, SOURCE_JENKINS_API_TOKEN)
    )

    if response.status_code == 200:
        nodes = response.json()["computer"]
        return nodes
    else:
        print(f"❌ 请求失败: {response.status_code}")
        return []


# 获取节点的配置
def get_node_config(jenkins_url, node_name):
    url = f"{jenkins_url}/computer/{node_name}/config.xml"
    response = requests.get(
        url, auth=HTTPBasicAuth(SOURCE_JENKINS_USERNAME, SOURCE_JENKINS_API_TOKEN)
    )

    if response.status_code == 200:
        return response.text
    else:
        print(f"❌ 获取配置失败: {node_name}")
        return None


# 创建目标 Jenkins 上的节点
def create_node_on_target_jenkins(node_config, node_name):
    url = f"{TARGET_JENKINS_URL}/computer/doCreateItem?name={node_name}&mode=EXCLUSIVE"
    headers = {"Content-Type": "application/xml"}

    response = requests.post(
        url,
        data=node_config,
        auth=HTTPBasicAuth(TARGET_JENKINS_USERNAME, TARGET_JENKINS_API_TOKEN),
        headers=headers,
    )

    if response.status_code == 200:
        print(f"✅ 节点 {node_name} 创建成功")
    else:
        print(f"❌ 创建节点 {node_name} 失败: {response.status_code}")


# 获取符合正则的节点
def filter_nodes_by_regex(nodes, regex_pattern):
    filtered_nodes = [
        node for node in nodes if re.match(regex_pattern, node["displayName"])
    ]
    return filtered_nodes


# 获取符合标签的节点
def filter_nodes_by_assigned_label(nodes, labels):
    filtered_nodes = [
        node
        for node in nodes
        if "assignedLabels" in node
        and any(lbl["name"] in labels for lbl in node["assignedLabels"])
    ]
    return filtered_nodes


# 主流程
if __name__ == "__main__":
    # 获取源 Jenkins 的节点列表
    source_nodes = get_nodes_from_jenkins(SOURCE_JENKINS_URL)
    # filtered_nodes = filter_nodes_by_regex(source_nodes, REGEX_PATTERN)
    labels = [
        "S80",
        "X300",
        "S10",
        "S30",
        "S50",
        "S70",
        "S90",
        "S3000",
        "S5000",
        "M1000",
        "Win11",
        "CI_GFX_Win10_hg_amodel",
        "CI_GFX_Win10_ph1_amodel",
        "CI_GFX_Win10_quyuan2_cmodel",
        "CI_GFX_Win10_quyuan1_cmodel",
        "Linux_build",
        "Linux_vps_build",
        "Linux_jump",
    ]  # 要筛选的标签，修改为你需要的标签
    filtered_nodes = filter_nodes_by_assigned_label(source_nodes, labels)

    print(f"✅ 找到 {len(filtered_nodes)} 个符合条件的节点")

    # 迁移所有符合条件的节点
    for node in filtered_nodes:
        node_name = node["displayName"]
        node_config = get_node_config(SOURCE_JENKINS_URL, node_name)
        if node_config:
            create_node_on_target_jenkins(node_config, node_name)

    print("🎉 迁移完成！")
