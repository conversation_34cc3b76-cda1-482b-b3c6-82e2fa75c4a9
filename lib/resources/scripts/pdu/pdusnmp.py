# _*_ coding:utf-8 _*_
from pysnmp.entity.rfc3413.oneliner import cmdgen
from pysnmp.proto import rfc1902


def validate_ip(ip_str):
    sep = ip_str.split(".")
    if len(sep) != 4:
        return False
    for i, x in enumerate(sep):
        try:
            int_x = int(x)
            if int_x < 0 or int_x > 255:
                return False
        except ValueError:
            return False
    return True


def validate_Sock(nSock):
    if nSock <= 0 or nSock > 8:
        return False
    return True


def get_value(devip, soid):
    cg = cmdgen.CommandGenerator()
    errorIndication, errorStatus, errorIndex, varBinds = cg.getCmd(
        cmdgen.CommunityData("pudinfo", "public", 0),
        cmdgen.UdpTransportTarget((devip, 161)),
        soid,
    )
    sResult = varBinds[0][1]
    return sResult


# 获取设备名称
def GetDeviceName(sDevIp):
    if not validate_ip(sDevIp):
        print("invalid ip address!")
        return None
    devName = get_value(sDevIp, "*******.4.1.23273.4.1.0")
    devName.asOctets().decode("unicode_escape", "ignore")
    return devName


# 获取总电压
def GetTotalVoltage(sDevIp):
    if not validate_ip(sDevIp):
        print("invalid ip address!")
        return None
    value = get_value(sDevIp, "*******.4.1.23273.4.2.0")
    rt_value = float(value) / 100
    return rt_value


# 获取总电流
def GetTotalCurrent(sDevIp):
    if not validate_ip(sDevIp):
        print("invalid ip address!")
        return None
    value = get_value(sDevIp, "*******.4.1.23273.4.3.0")
    rt_value = float(value) / 100
    return rt_value


# 获取总功率
def GetTotalPower(sDevIp):
    if not validate_ip(sDevIp):
        print("invalid ip address!")
        return None
    value = get_value(sDevIp, "*******.4.1.23273.4.4.0")
    rt_value = float(value) / 10
    return rt_value


# 获取总电能
def GetTotalEnergy(sDevIp, model):
    if not validate_ip(sDevIp):
        print("invalid ip address!")
        return None
    value = get_value(sDevIp, "*******.4.1.23273.4.5.0")
    if model == "3":
        rt_value = float(value) / 1000
    elif model == "2":
        rt_value = float(value) / 100
    return rt_value


# 获取温度
def GetTemprature(sDevIp):
    if not validate_ip(sDevIp):
        print("invalid ip address!")
        return None
    value = get_value(sDevIp, "*******.4.1.23273.4.6.0")
    rt_value = float(value) / 10
    return rt_value


# 获取湿度
def GetHumidity(sDevIp):
    if not validate_ip(sDevIp):
        print("invalid ip address!")
        return None
    value = get_value(sDevIp, "*******.4.1.23273.4.7.0")
    rt_value = float(value) / 10
    return rt_value


# 打开或关闭指定插口
def TurnOnOff(sDevIp, sock, onoff):
    if not validate_ip(sDevIp):
        print("invalid ip address!")
        return None
    if not validate_Sock(sock):
        print("invalid sock!")
        return None
    sOId = "*******.4.1.23273.4.%d.0" % (15 + sock)
    if onoff:
        state = "ON"
    else:
        state = "OFF"

    cg = cmdgen.CommandGenerator()
    errorIndication, errorStatus, errorIndex, varBinds = cg.setCmd(
        cmdgen.CommunityData("pudinfo", "public", 0),
        cmdgen.UdpTransportTarget((sDevIp, 161)),
        (sOId, rfc1902.OctetString(state)),
    )

    return errorStatus


# 获取插口状态 0-关闭 1-开启
def GetStatus(sDevIp, sock):
    if not validate_ip(sDevIp):
        print("invalid ip address!")
        return None
    if not validate_Sock(sock):
        print("invalid sock!")
        return None
    sOId = "*******.4.1.23273.4.%d.0" % (15 + sock)
    cg = cmdgen.CommandGenerator()
    errorIndication, errorStatus, errorIndex, varBinds = cg.getCmd(
        cmdgen.CommunityData("pudinfo", "public", 0),
        cmdgen.UdpTransportTarget((sDevIp, 161)),
        sOId,
    )
    sResult = varBinds[0][1]
    print(f"sResult = [{sResult}]")
    return sResult
    # if sResult == 'ON':
    #    return 1
    # elif sResult == 'OFF':
    #    return 0
    #
    # return errorStatus


# 获取指定插口电流
def GetCurrent(sDevIp, nsock):
    if not validate_ip(sDevIp):
        print("invalid ip address!")
        return None
    if not validate_Sock(nsock):
        print("invalid sock!")
        return None
    sOId = "*******.4.1.23273.4.%d.0" % (23 + nsock)
    value = get_value(sDevIp, sOId)
    rt_value = float(value) / 100
    return rt_value


# 获取指定插口电能
def GetEnergy(sDevIp, nSock, model):
    if not validate_ip(sDevIp):
        print("invalid ip address!")
        return None
    if not validate_Sock(nSock):
        print("invalid sock!")
        return None
    sOId = "*******.4.1.23273.4.%d.0" % (31 + nSock)
    value = get_value(sDevIp, sOId)
    if model == "3":
        rt_value = float(value) / 1000
    elif model == "2":
        rt_value = float(value) / 100
    return rt_value


# 获取指定插口名称
def GetSockName(sDevIp, nSock):
    if not validate_ip(sDevIp):
        print("invalid ip address!")
        return None
    if not validate_Sock(nSock):
        print("invalid sock!")
        return None
    sOId = "*******.4.1.23273.4.%d.0" % (7 + nSock)
    value = get_value(sDevIp, sOId).asOctets().decode("unicode_escape", "ignore")
    return value
