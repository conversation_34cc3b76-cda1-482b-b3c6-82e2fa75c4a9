# _*_ coding:utf-8 _*_
import argparse
import json
import os
import time

import pdusnmp as pdu

pdu_ip = ""
sleep_time = 0.4


class PDU:
    def __init__(self, ip):
        self.ip = ip
        config_file = os.path.join(
            os.path.dirname(os.path.abspath(__file__)),
            "pdu_slots_mapping_for_duts.json",
        )

        with open(config_file, "r") as hd:
            slots_content = json.load(hd)
            self.slots = slots_content[self.ip]

    def TurnOnAll(self):
        for i in range(1, 9):
            self.TurnOnSlot(i)
        print("All slots are powered on.")

    def TurnOffAll(self):
        for i in range(1, 9):
            time.sleep(sleep_time)
            self.TurnOffSlot(i)
        print("All slots are powered off.")

    def TurnOnSlot(self, slotID):
        result = pdu.TurnOnOff(self.ip, slotID, True)
        print(f"slot {slotID} is powered on with result <{result}>!")
        # time.sleep(5)

    def TurnOnSlotByNodeName(self, nodeName):
        self.TurnOnSlot(self.slots[nodeName])

    def TurnOffSlotByNodeName(self, nodeName):
        self.TurnOffSlot(self.slots[nodeName])

    def RestartSlotByNodeName(self, nodeName):
        self.RestartSlot(self.slots[nodeName])

    def RestartSlot(self, slotID):
        self.TurnOffSlot(slotID)
        time.sleep(10)
        self.TurnOnSlot(slotID)
        print(f"slot {slotID} is restarted!")

    def RestartAllSlot(self):
        for i in range(1, 9):
            time.sleep(sleep_time)
            self.TurnOffSlot(i)
        time.sleep(4)
        for i in range(1, 9):
            time.sleep(sleep_time)
            self.TurnOnSlot(i)
        print("all slots are restarted!")

    # 打开开关slotID 【1..8】
    def TurnOffSlot(self, slotID):
        result = pdu.TurnOnOff(self.ip, slotID, False)
        print(f"slot {slotID} is powered off with result <{result}> !")

    # 总电压
    def GetTotalVoltoage(self):
        total_votoage = pdu.GetTotalVoltage(self.ip)
        print(f"Total Voltoage is: {total_votoage} V.")
        return total_votoage

    # 设备名称
    def GetDeviceName(self):
        return pdu.GetDeviceName(self.ip)

    # 电流

    def GetTotalCurrent(self):
        totalCurrent = pdu.GetTotalCurrent(self.ip)
        print(f"Total Current is: {totalCurrent} A.")
        return totalCurrent

    # 总功率

    def GetTotalPower(self):
        totalPower = pdu.GetTotalPower(self.ip)
        print(f"total Power is: {totalPower} W.")
        return totalPower

    def GetNodeStatus(self, nodeName):
        nodeStatus = self.GetSlotStatus(self.slots[nodeName])
        print(f"{nodeName} status is: {nodeStatus}.")
        return nodeStatus

    # 插口的状态获取 0 close 1 open
    def GetSlotStatus(self, slotID):
        slotStatus = pdu.GetStatus(self.ip, slotID)
        print(f"slot {slotID} status is: {slotStatus}.")
        return slotStatus

    # 总电能
    def GetTotalEnergy(self, model):
        return pdu.GetTotalEnergy(self.ip, model)

    # 环境温度

    def GetTemprature(self):
        return pdu.GetTemprature(self.ip)

    # 环境湿度

    def GetHumidity(self):
        return pdu.GetHumidity(self.ip)

    # 指定插口电能

    def GetSlotEnergy(self, slotID, model):
        return pdu.GetEnergy(self.ip, slotID, model)

    # 指定插口电流

    def GetSlotCurrent(self, slotID):
        return pdu.GetCurrent(self.ip, slotID)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="mt remote test framework")
    parser.add_argument(
        "-i", "--ipofpdu", help="the IP address of the PDU you wanna control"
    )
    parser.add_argument(
        "-t", "--targetmachineip", help="short ip of machine to control", required=False
    )
    parser.add_argument(
        "-s", "--slot", help="slot number of the machine plugged in", required=False
    )
    parser.add_argument(
        "-o",
        "--operate",
        help="support on/off/restart/restartall/turnonall/turnoffall/slotEnergy/totalEnergy",
        default="on",
    )
    parser.add_argument("-m", "--model", help="support 2/3", required=False)
    args = parser.parse_args()

    config_file = os.path.join(
        os.path.dirname(os.path.abspath(__file__)), "pdu_slots_mapping_for_duts.json"
    )
    with open(config_file, "r") as hd:
        slots_content = json.load(hd)
        for ip in slots_content:
            if args.targetmachineip:
                if args.targetmachineip in slots_content[ip].keys():
                    pdu_ip = ip
    pdu_ip = args.ipofpdu if args.ipofpdu else pdu_ip
    if not pdu_ip:
        raise Exception("no PDU found")
    print(f"To be controlled PDU's IP is: [{pdu_ip}]")
    ipdu = PDU(pdu_ip)
    if args.operate and args.targetmachineip:
        if args.operate == "on":
            ipdu.TurnOnSlotByNodeName(args.targetmachineip)
        elif args.operate == "off":
            ipdu.TurnOffSlotByNodeName(args.targetmachineip)
        elif args.operate == "restart":
            ipdu.RestartSlotByNodeName(args.targetmachineip)
    if args.operate and args.slot:
        if args.operate == "on":
            ipdu.TurnOnSlot(int(args.slot))
        elif args.operate == "off":
            ipdu.TurnOffSlot(int(args.slot))
        elif args.operate == "restart":
            ipdu.RestartSlot(int(args.slot))
        elif args.operate == "slotEnergy":
            print(ipdu.GetSlotEnergy(int(args.slot), args.model))
    if args.operate in ("restartall", "turnonall", "turnoffall", "totalEnergy"):
        if args.ipofpdu:
            if args.operate == "restartall":
                ipdu.RestartAllSlot()
            elif args.operate == "turnonall":
                ipdu.TurnOnAll()
            elif args.operate == "turnoffall":
                ipdu.TurnOffAll()
            elif args.operate == "totalEnergy":
                print(ipdu.GetTotalEnergy(args.model))
        else:
            print(
                f"Error: {args.operate} need provide the IP address of PDU by -i option!"
            )
