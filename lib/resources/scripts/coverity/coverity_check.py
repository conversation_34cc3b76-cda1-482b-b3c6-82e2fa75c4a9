#!/usr/bin/python3
import json
import sys
from bs4 import BeautifulSoup

linenum_list = []


def checkout_report(argv):
    with open(argv[1], "r") as f:
        data = json.load(f)
    issueInfo_list = data["issueInfo"]
    for issueInfo in issueInfo_list:
        if not issueInfo["presentInComparisonSnapshot"]:
            # if issueInfo["triage"]["action"] !="Ignore" and issueInfo["triage"]["classification"] !="False Positive" and issueInfo["triage"]["classification"] !="Intentional":
            linenum_list.append(
                str(issueInfo["occurrences"][0]["file"].replace("/", ""))
                + str(issueInfo["occurrences"][0]["mainEventLineNumber"])
            )
            print(linenum_list)
    htmlfile = open(argv[2], "r", encoding="utf-8")
    htmlhandle = htmlfile.read()
    soup = BeautifulSoup(htmlhandle, "lxml")
    for item in soup.find_all("tr"):
        html_info = (
            item.find_all("td")[2].get_text().strip().replace("/", "")
            + item.find_all("td")[3].get_text().strip()
        )
        if html_info not in linenum_list:
            item.extract()
    html = soup.prettify("utf-8")
    with open(argv[2], "wb") as file:
        file.write(html)


if __name__ == "__main__":
    checkout_report(sys.argv)
