"""
<AUTHOR> <EMAIL>
-------
@description : compute 各个数学库代码覆盖率数据上传时序数据库 influxdb
---------
"""

import re
import argparse
from datetime import datetime
import influxdb_client
from influxdb_client.client.write_api import SYNCHRONOUS


################ compute qa influxdb ################
# g_influxdb_url = "http://************:58086"
g_influxdb_url = "http://swqa-influxdb.mthreads.com"
g_influxdb_token = "EQd3XoyipeVnZeqEyGEu"
g_influxdb_org = "compute_qa"
g_influxdb_bucket = "Coverage_mathX"
g_influxdb_measurement = "mathX"


def getCoveragedata(file):
    data = {}
    line_covered = None
    line_total = None
    line_coverage = None
    function_covered = None
    function_total = None
    function_coverage = None

    with open(file, "r") as f:
        html_content = f.read()
        # Compile regular expression
        pattern = re.compile(r"headerCovTableEntry.*>([0-9.]+)")
        # Use findall method to find all matches
        matches = re.findall(pattern, html_content)
        # print("matches: ", matches)

        # Assign matches to variables
        line_covered = matches[0]
        line_total = matches[1]
        line_coverage = matches[2]
        function_covered = matches[3]
        function_total = matches[4]
        function_coverage = matches[5]
        data["line_covered"] = int(line_covered)
        data["line_total"] = int(line_total)
        data["line_coverage"] = float(line_coverage)
        data["function_covered"] = int(function_covered)
        data["function_total"] = int(function_total)
        data["function_coverage"] = float(function_coverage)

    print("data: ", data)
    return data


def writetoInfluxDB(product, data, url="", token="", org="", bucket="", measurement=""):
    # @description :
    # ---------
    # @param :product:具体数学库名称,如muBLAS_cts
    #         data:代码覆盖率结果，字典
    #         url, token, org:influxDB鉴权用
    #         bucket(桶):数据库表名称,数据的存储容器或命名空间
    #         _measurement(测量):按照类型或类别组织的数据集
    #         _field:代表数据点中的字段名称
    #         _value:代表数据点中的字段值
    #         _time:代表数据点的时间戳。
    # -------
    # @Returns :
    # -------

    # 创建客户端
    client = influxdb_client.InfluxDBClient(url=url, token=token, org=org)
    # 使用写API准备要写入数据库的数据
    write_api = client.write_api(write_options=SYNCHRONOUS)

    time_now = datetime.utcnow()
    print("time_now: ", time_now)
    point = {
        "measurement": measurement,
        "time": time_now,
        "tags": {"product": product},  # 添加标签值
        "fields": data,
    }
    write_api.write(bucket=bucket, record=point)
    print("Coverage data successfully written to InfluxDB")


def deletefromInfluxDB(url="", token="", org="", bucket="", measurement=""):
    client = influxdb_client.InfluxDBClient(url=url, token=token, org=org)
    start = "2023-10-24T00:00:00Z"
    # stop = "2023-01-05T23:00:00Z"
    stop = datetime.now()
    client.delete_api().delete(
        start, stop, "_measurement={}".format(measurement), bucket=bucket, org=org
    )
    print(f"Measurement '{measurement}' deleted successfully")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--indexfile",
        help="set indexfile path which exist code coverage",
        type=str,
        default="",
    )
    parser.add_argument(
        "--product", help="set product name, such as muBLAS_cts", type=str, default=""
    )
    parser.add_argument(
        "--bucket", help="set influxdb bucket name", type=str, default=g_influxdb_bucket
    )
    parser.add_argument(
        "--measurement", help="set influxdb measurement name", type=str, default=g_influxdb_measurement
    )
    args = parser.parse_args()

    data = getCoveragedata(file=args.indexfile)

    # deletefromInfluxDB(
    #     url=g_influxdb_url,
    #     token=g_influxdb_token,
    #     org=g_influxdb_org,
    #     bucket=args.bucket,
    #     measurement=args.measurement)

    writetoInfluxDB(
        product=args.product,
        data=data,
        url=g_influxdb_url,
        token=g_influxdb_token,
        org=g_influxdb_org,
        bucket=args.bucket,
        measurement=args.measurement,
    )

    # 运行命令参考: python mathx_coverage_writetoInfluxDB.py --indexfile /home/<USER>/xiawei/tmp/muthrust_llvm_coverage_report/index.html --product muBLAS_cts