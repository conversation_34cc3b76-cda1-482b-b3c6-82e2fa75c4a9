#!/bin/bash
log_path=$1
case_file=$2
cp -rf /root/${case_file} ${log_path}/tmp_cases.txt
export LD_LIBRARY_PATH=/root/piglit/lib
for((i=0;i<10;i++))
do
    pushd /root/piglit
    ./piglit run --timeout 7200 all --test-list ${log_path}/tmp_cases.txt -v ${log_path}/data
    ./piglit summary html ${log_path}/html  ${log_path}/data
    popd
    fail_num=`grep -E "data/.*.html"  ${log_path}/html/problems.html |awk -F "/|.html" ' {print $2}' | wc -l`

    if [ "$fail_num" -eq "0" ];
    then
        echo "test pass"
        break
    fi
    if [[ "$fail_num" -gt "1" && $i -eq 4 ]];
    then
        echo "test fail !!"
        echo $fail_num
        exit 1
    fi
    grep -E "data/.*.html"  ${log_path}/html/problems.html |awk -F "/|.html" '{print $2}' > ${log_path}/tmp_cases.txt
    sed -i '1d' ${log_path}/tmp_cases.txt
done
