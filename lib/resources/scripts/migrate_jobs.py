import os

from jenkins import Jenkins

old_server_url = "http://sh-jenkins.mthreads.com/"
new_server_url = "http://swqa-jks.mthreads.com/"

old_server = <PERSON>(url=old_server_url, username="tingjun.shen-ext", password="")
new_server = <PERSON>(
    url=new_server_url, username="admin", password="97f6a92e818d4c11870c3b9986c061e7"
)


def export_jobs():
    os.makedirs("jobs", exist_ok=True)
    for job_name in old_server.get_jobs():
        job_name = job_name["name"]
        job_conf = old_server.get_job_config(job_name)
        with open(f"jobs/{job_name}.xml", "w", encoding="utf-8") as job_file:
            job_file.write(job_conf)
        print(f"Exported job: {job_name}")


def import_jobs():
    for job_file in os.listdir("jobs"):
        job_name, ext = os.path.splitext(job_file)
        if ext == ".xml":
            file_path = f"jobs/{job_file}"
            with open(file_path, "r", encoding="utf-8") as file:
                job_conf = file.read()

            if new_server.job_exists(job_name):
                new_server.reconfig_job(job_name, job_conf)
                print(f"Updated job: {job_name}")
            else:
                new_server.create_job(job_name, job_conf)
                print(f"Imported job: {job_name}")


jobs_to_migrate = """build.gr-kmd
build.gr-umd
build.libdrm-mt
build.linux-ddk
build.linux-ddk-ogl
build.linux-ddk-vps
build.mt-media-driver
build.mt-tools-extension
build.mtcc
build.muDNN
build.MUSA-Runtime
build.muSOLVER
build.ogl
build_directstream_tmp
C_B_ogl_nohw
C_B_wddm_release
C_T_dxva_dxc
C_T_ogl_pdump
C_T_PH_mt_trace
C_T_PH_pdump
C_T_wddm_apitrace
Ccache_umd_test
CI_ACX_build_gitlab
**********************
CI_Compiler_Mtcc_B_HG
CI_Compiler_Mtcc_B_new
CI_Compiler_mtcc_test_pipline
CI_Compiler_PH_mtcc_build_nohw_pipeline
CI_Compiler_PH_mtcc_build_pipeline
CI_Compiler_PH_mtcc_whlk_test_pipeline
CI_dcgm_test_gitlab
CI_DDK_2.0_build_gitlab_subjob
CI_DDK_2.0_fw_gitlab
CI_DDK_2.0_kmd_gitlab
CI_DDK_2.0_libdrm_gitlab
CI_DDK_2.0_libdrm_test
CI_DDK_2.0_umd_test
CI_DDK_dma_transfer_test_gitlab
CI_DDK_gcbs_gpu_test_gitlab
CI_DDK_glmark_test_gitlab
CI_DDK_media_test_gitlab
CI_DDK_VDI_test_gitlab
CI_DDK_video_test_gitlab
CI_Decodecheck_test_pipeline
CI_directstream_build_gitlab
CI_DirectStream_test_pipline
CI_DirectStream_vaapi-fits_test_pipline
CI_DX11_build_pipeline
CI_DX12_build_pipeline
CI_FFmpeg_build_gitlab
CI_gmi_test_gitlab
CI_infra-**********************
CI_M3D_test_pipline
CI_management_controller_gitlab
CI_management_platform_gitlab
CI_management_test_gitlab
CI_msight-graphics_build_gitlab
CI_msight-system_test_gitlab
CI_msys_cli_test
CI_mt-perf_test_gitlab
CI_MTAPI_pipline
CI_MTJPEGSDK_linux_gitlab
CI_MTML_TEST
CI_OGL_4_0_test_pipline
CI_OGL_build_linux_gitlab
CI_OGL_build_pipline
CI_OGL_mtcc_test
CI_OGL_newapi_test_pipline
CI_OGL_test_pipline
CI_PES_gitlab
CI_PES_Windows_coverage_pipline
CI_PES_Windows_feat_pipline
CI_QtInstallerFramework_build_pipline
CI_VDI_Guest_build_pipeline
CI_VDI_Host_B
CI_VDI_Linux_media_gitlab
CI_VDI_Linux_umd_gitlab
CI_VDI_mtml_test_gitlab
test.vdi.win
CI_video_drv_build_gitlab
CI_video_mtgpu_build_gitlab
CI_video_mtgpu_test_gitlab
CI_video_play_test_gitlab
CI_wave517_fw_cppcheck_gitlab
CI_wave627_fw_cppcheck_gitlab
CI_WDDM_Compiler_Test_pipline
CI_WDDM_DirectStream_test_pipline
CI_WDDM_DXVA_test_pipline
CI_WDDM_FW_pipline
CI_WDDM_HG_VPS_test_pipeline
CI_WDDM_pdump_test_pipline
CI_WDDM_PH_VPS_test_pipline
CI_WDDM_test_pipline
CI_WDDM_VPS_test_pipline
CI_WDDM_WHQL_Compute_test_pipline
CI_WDDM_WHQL_test_pipline
CI_win_mt-perf_build_pipeline
CI_Windows_ffmpeg_build_pipline
CI_Windows_msight-graphics_build_pipeline
CI_Windows_msight-system_build_pipeline
CI_Windows_mtml_pipline
CI_Windows_vaapi-fits_test_pipline
CI_Windows_video_drv_nohw_pipeline
CI_Windows_video_drv_pipline
compile_musa_pkg_apollo
compute_cuda-samples_pkg_gitlab
compute_ddk2_musa_pkg_gitlab
compute_ddk_pkg
**********************
compute_KUAE_MPC_1.0_pkg
compute_mathX_pkg_gitlab
compute_musa_pkg
compute_musa_pkg_gitlab
**********************
compute_musa_pkg_ningsi
compute_release_ci
compute_release_ci_build
compute_release_ci_test
compute_release_ddk
compute_release_KUAE_1.0
Cov_Acc_kmd
coverage_data_fetch
coverity-linux-apollo
Coverity_Access_compiler_test
Coverity_Access_directstream_linux_test
Coverity_Access_gmi_test
Coverity_Access_gpu-fw_test
Coverity_Access_m3d_ogl_test
Coverity_Access_m3d_test
Coverity_Access_m3d_wddm_test
Coverity_Access_management_test
Coverity_Access_media_test
Coverity_Access_msight_test
Coverity_Access_mtdcgm_test
Coverity_Access_mtdxum_test
Coverity_Access_mtjpegsdk_test
Coverity_Access_mtml_platform_test
Coverity_Access_mtperf_test
Coverity_Access_mtTool_test
Coverity_Access_ogl_test
Coverity_Access_pes_test
Coverity_Access_umd_dispatcher
Coverity_Access_umd_test
Coverity_Access_video_drv
Coverity_Access_wave517_fw_test
Coverity_Access_wddm_test
Coverity_Access_win_msight-graphics_test
Coverity_Access_win_msight_test
Coverity_Access_win_pes_test
Coverity_Access_win_videodrv_test
Coverity_alphacore_test
Coverity_audio_test
Coverity_compiler_test
Coverity_directstream_linux_test
Coverity_directstream_test
Coverity_fw_test
Coverity_gmi_test
Coverity_gpu-fw_test
Coverity_kmd_test
Coverity_kmd_vdi_test
Coverity_m3d_ogl_test
Coverity_m3d_test
Coverity_m3d_wddm_test
Coverity_management_test
Coverity_media_test
Coverity_msight_test
Coverity_mtdcgm_test
Coverity_mtjpegsdk_test
Coverity_mtml_platform_test
Coverity_mtperf_test
Coverity_MTSystemReporter_test
Coverity_mttool_test
Coverity_ogl_test
Coverity_pes_test
Coverity_umd_test
Coverity_video_drv_test
Coverity_wddm_test
Coverity_win_msight_test
Coverity_win_obs_test
Coverity_win_pes_test
Coverity_win_videodrv_test
CTS_ALL_TEST
cuda_compatible_pkg
CUDACompatible_HygonKylin_release2.7_rc4
CUDACompatible_release2.7_rc4_KylinARM64
D-wddm-build
d3d_regression_test
D_compiler_mtcc_B
D_compiler_mtcc_B_hg
D_compiler_mtcc_B_manual
D_compiler_mtcc_B_ph
D_compiler_mtcc_B_ph_release
D_compiler_mtcc_B_release
ddk_deb_build_pipeline_new
ddk_deb_build_pipline
ddk_directstream_build
DDK_DirectStream_decode_test_gitlab
DDK_DirectStream_encode_test_gitlab
DDK_DirectStream_mtcapture_gitlab
DDK_DirectStream_musa_codec_gitlab
DDK_DirectStream_vaapi-fits_gitlab
DDK_ffmpeg-vaapi-fit_test_gitlab
ddk_management_build
DDK_media_encode_test_gitlab
DDK_mt_media_unit_test_gitlab
ddk_pes_build
ddk_umd_build
ddk_video_build
dev_infra-renderdoc_test_pipeline
Diagsys_CI
diagsys_ci_gitlab
directstream_build
driver install and uninstall test
dx11_windows_build
dxc_windows_build
energy_consumption_monitoring
fec_linux_pkg_build_gitlab
fec_linux_smoke_ci
fec_trusted_firmware_pkg_build_gitlab
fec_trusted_firmware_smoke_ci
ffmpeg_windows_build
Game_continue_trace_test
gitlab-trigger
gmi_build_pipeline
HG-amodel_whlk_test_on_ATF
Init_MR_Status
jenkins_auto_start
jiayi_debug
kmd
kmd_vdi
Linux-ddk_2.0_PKG_Tool
M-vdi-build
M-wddm-build
M-wddm-dispatcher
m3d
m3d_gles_cts_gitlab_ddk2.0
m3d_m3d_cts_smoke_ci_gitlab
m3d_media_driver_smoke_ci_ddk2.0
m3d_merge_build_ogl
m3d_ogl_cts_gitlab_ddk2.0
m3d_pkg_build_libdrm_gitlab
m3d_pkg_build_media_driver_gitlab
m3d_pkg_build_ogl_gitlab
m3d_vpu_test_ddk2_gitlab
m3d_vpu_test_gitlab
m3d_vulkan_cts_gitlab_ddk2.0
M_B_compiler
MCCP_test_mccl_cts_s4000
mr.gr-kmd
mr.gr-kmd-jiangbo
mr.gr-umd
mr.linux-ddk
mr.m3d
mr.mt-tools-extension
mr.MUSA-Runtime
mr.muSOLVER
mr.muSOLVER_cts
mr.ogl
mr.swqa-ci
msight-system_build
msight-system_win_build
mt-management
mt-perf_build
mt-perf_merge_build
mt_dcgm_build
mtml_deb_build
mtml_windows_build
mtts_pkg_build_gitlab
Multi_media_weekly_task
MUSA-Runtime_pdump_test_gitlab
mutlass_benchmark_gitlab
mutlass_smoke_ci_gitlab
mutlass_test_gitlab
new_branch
OGL_merge_build
pes_dispatcher
pes_windows_feat_build
pes_windows_signature_build
PH-amodel_whlk_test_on_ATF
print-waiting-job
push.gr-kmd
push.gr-umd
push.libdrm-mt
push.linux-ddk
push.ogl
Qt-Installer-Framework_windows_build
RAM_monitor
release_kuae_1.2-hygon_ubuntu_S4000_build
release_kuae_1.2-hygon_ubuntu_S4000_ddk_build
release_kuae_1.2-hygon_ubuntu_S4000_temp
Restart_Node_By_PDU
restart_node_pdu
restart_whlk_test_nodes
restart_win_test_nodes
S80_CI_WHLK_Video_test
SAST_Coverity_Dispatcher
set_gitlab_mr_status
set_tag
stj Pipeline
Sudi_apitrace_continue_test
Sudi_unigine_test
test.dkms_intree
test.dma_transfer
test.gcbs_gpu
test.glmark2
test.gr-umd_ut
test.graphic_cts
test.libdrm_ut
test.m1000_mathx_cts
test.m1000_mudnn_cts
test.m1000_musa_cts
test.m3d_cts
test.m3d_musa_cts
test.mtcc
test.mtgl_cts
test.mtml
test.mtml_vdi
test.mttrace_replay
test.muBLAS_cts
test.mudnn_cts
test.musa-runtime_pdump
test.musa_cts
test.muSOLVER_cts
test.musrt_pdump
test.ogl_cts
test.pdump
test.perf
test.piglit
test.renderdoc
test.vaapi_fits
test.vps
test.vps2.0
test_directstream_test_tmp
test_mupti_cts_gitlab
Timespy_test
tmp_test
Unigine_DX11_test
Unigine_OPenGL_test
vdi_build_manual
vdi_dkms_build
vdi_driver_build_pipeline
vdi_guest_build
vdi_guest_release_build
vdi_host_build
VDI_Linux_build
vgpu_daemon_build_pipeline
vgpu_daemon_windows_build
wddm_driver_sign
wddm_driver_sign_manual
wddm_m3d_compiler_build_pipeline
wddm_m3d_compiler_test_pipeline
wddm_master_auto_build_new
wddm_master_auto_build_nohw_new
wddm_release_auto_build
weekly_infra-renderdoc_test_pipeline
win-m3d-build
win-m3d-dispatcher
win-m3d-test
win-vdi-build
win-wddm-build
win-wddm-dispatcher
win-wddm-test
Win_Decode_perf_test_pipeline
win_start_vps_nodes
windows_compiler_merge_information
windows_m3d_merge_information
windows_ogl_merge_information
Windows_update_driver
windows_wddm_merge_information"""

if __name__ == "__main__":
    export_jobs()
    import_jobs()
