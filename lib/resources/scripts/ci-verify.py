import json
import re
import sys
import time
from threading import Timer

import mysql.connector
import requests

merge_requests = []
status_timer = None
run_time = 0
time_interval = int(sys.argv[1])
cause_map = {
    "pending": "timeout",
    "running": "timeout",
    "failed": "unknown",
    "success": "",
}
need_modify_repos_str = sys.argv[2]
need_modify_repos = need_modify_repos_str.split("-") if need_modify_repos_str else []
headers = {"PRIVATE-TOKEN": sys.argv[3]}
database_pwd = sys.argv[4]
ci_verify_no = sys.argv[5]
file_path = "repos.json"

with open(file_path, "r", encoding="utf-8") as file:
    repos = json.load(file)


def modify_reademe(name, repo, branch):
    readme_file = repo["readme_file"] if "readme_file" in repo else "README%2Emd"
    api_url = (
        "https://sh-code.mthreads.com/api/v4/projects/"
        + str(repo["id"])
        + "/repository/files/"
        + readme_file
    )
    data = {
        "branch": branch,
        "content": str(int(time.time())),
        "commit_message": "update README.md",
    }
    try:
        res = requests.put(api_url, headers=headers, data=data)
        res_data = res.json()
        if "message" in res_data:
            message = res_data["message"]
            if "doesn't exist" in message or "不存在" in message:
                requests.post(api_url, headers=headers, data=data)
        return True
    except Exception:
        print("Failed to modify readme file of repo " + name)
        return False


def create_merge_request():
    for repo in repos:
        timestamp = str(int(time.time()))
        name = repo["repoName"]
        id = str(repo["id"])
        target_branch = repo["targetBranch"]
        trigger_numbers = repo["triggerNumber"] if "triggerNumber" in repo else 1
        head_commit = "null"

        try:
            commit_api_url = f"https://sh-code.mthreads.com/api/v4/projects/{id}/repository/commits/{target_branch}"
            print(commit_api_url)
            commit_res = requests.get(commit_api_url, headers=headers)
            commit_res_data = commit_res.json()
            head_commit = commit_res_data["short_id"]
            print(head_commit)
        except Exception as e:
            print("Failed to get head commit")
            print(e)

        for i in range(trigger_numbers):
            _target_branch = target_branch if i == 0 else f"{target_branch}_{i}"
            if ci_verify_no and ci_verify_no != "null":
                _target_branch = (
                    target_branch
                    if ci_verify_no == "0"
                    else f"{target_branch}_{ci_verify_no}"
                )
            source_branch = f"ci_verify_for_{_target_branch}"
            modify_reademe_result = modify_reademe(name, repo, source_branch)
            if not modify_reademe_result:
                continue
            api_url = (
                "https://sh-code.mthreads.com/api/v4/projects/" + id + "/merge_requests"
            )
            data = {
                "id": repo["id"],
                "source_branch": source_branch,
                "target_branch": target_branch,
                "title": "[SW-" + timestamp + "] ci verify ",
            }
            respose = requests.post(api_url, headers=headers, data=data)
            res_data = respose.json()
            if "web_url" in res_data:
                merge_requests.append(
                    {
                        "repo_name": name,
                        "merge_request_iid": res_data["iid"],
                        "project_id": res_data["project_id"],
                        "pipeline_status": "pending",
                        "head_commit": head_commit,
                        "source_branch": source_branch,
                        "target_branch": target_branch,
                    }
                )
            else:
                if (
                    "message" in res_data
                    and isinstance(res_data["message"], list)
                    and len(res_data["message"]) > 0
                ):
                    message = res_data["message"][0]
                    if "already exists" in message or "已存在" in message:
                        match = re.findall(r"(\d+)", message)
                        merge_requests.append(
                            {
                                "repo_name": name,
                                "merge_request_iid": match[0],
                                "project_id": repo["id"],
                                "pipeline_status": "pending",
                                "head_commit": head_commit,
                                "source_branch": source_branch,
                                "target_branch": target_branch,
                            }
                        )
                else:
                    print("Failed to create merge request for Repo " + name + ".<br>")


def close_merge_request():
    for mr in merge_requests:
        api_url = (
            "https://sh-code.mthreads.com/api/v4/projects/"
            + str(mr["project_id"])
            + "/merge_requests/"
            + str(mr["merge_request_iid"])
        )
        data = {
            "id": mr["project_id"],
            "merge_request_iid": mr["merge_request_iid"],
            "state_event": "close",
        }
        requests.put(api_url, headers=headers, data=data)


def get_head_pipeline(mr):
    api_url = (
        "https://sh-code.mthreads.com/api/v4/projects/"
        + str(mr["project_id"])
        + "/merge_requests/"
        + str(mr["merge_request_iid"])
        + "/pipelines"
    )
    params = {
        "id": mr["project_id"],
        "merge_request_iid": mr["merge_request_iid"],
        "per_page": 1,
    }

    res = requests.get(api_url, headers=headers, params=params)
    res_data = res.json()

    return res_data[0] if (isinstance(res_data, list) and len(res_data) > 0) else {}


def convert_seconds_to_hours(seconds):
    return time.strftime("%Hh %Mm %Ss", time.gmtime(seconds))


def send_dingtalk_msg(msg):
    print(msg)
    try:
        data = json.dumps(
            {"msgtype": "markdown", "markdown": {"title": "CI verify", "text": msg}}
        )
        headers = {"Content-Type": "application/json"}
        # https://oapi.dingtalk.com/robot/send?access_token=703b74ac75bdd0464a0e91197911b39d35cff62c406ab842ad0422be1184b092
        # https://oapi.dingtalk.com/robot/send?access_token=52cd8374b28dd0ce3c09ef3069ebd59e292736fce8aaea2858ce458142a66005  ci-verify
        requests.post(
            "https://oapi.dingtalk.com/robot/send?access_token=52cd8374b28dd0ce3c09ef3069ebd59e292736fce8aaea2858ce458142a66005",
            headers=headers,
            data=data,
        )
    except Exception as e:
        print("Send dingtalk msg failed.")
        print(e)


def get_pipeline_statues_by_commitid(projectId, commitId, ref):
    api_url = f"https://sh-code.mthreads.com/api/v4/projects/{str(projectId)}/repository/commits/{commitId}/statuses?per_page=100&ref={ref}"
    res = requests.get(api_url, headers=headers)
    res_data = res.json()
    return res_data


def set_result_by_pipeline(next_timer):
    is_pending = False
    is_running = False
    is_failed = False

    for mr in merge_requests:
        pipeline_data = get_head_pipeline(mr)
        if "web_url" in pipeline_data:
            # this mr has pipeline
            old_status = mr["pipeline_status"]
            status = pipeline_data["status"]
            mr["pipeline_status"] = pipeline_data["status"]
            mr["pipeline_url"] = pipeline_data["web_url"]
            mr["pipeline_id"] = pipeline_data["id"]
            mr["ref"] = pipeline_data["ref"]
            mr["commit_id"] = pipeline_data["sha"]

            run_time_hours = convert_seconds_to_hours(run_time)

            if old_status != status:
                print(
                    "The status of pipeline "
                    + mr["pipeline_url"]
                    + " of repo "
                    + mr["repo_name"]
                    + " changed from "
                    + old_status
                    + " to "
                    + status
                    + "."
                )

            if status == "running" or status == "pending":
                is_running = status == "running"
                is_pending = status == "pending"
                mr["duration"] = run_time_hours
                mr["duration_seconds"] = run_time
                if not next_timer:
                    is_failed = True
            else:
                if status == "failed":
                    is_failed = True

                if old_status != status:
                    mr["duration"] = run_time_hours
                    mr["duration_seconds"] = run_time
        else:
            is_pending = True
            if not next_timer:
                is_failed = True

    if (is_pending or is_running) and next_timer:
        status_timer = Timer(time_interval, check_pipeline_status)
        status_timer.start()
    else:
        description = ""
        msg_list = []
        if is_failed:
            description += "CI verify failed.<br><br>"
            msg_list.append("## CI verify failed")
        else:
            description += "CI verify success.<br><br>"
            msg_list.append("## CI verify success")

        values = []
        sql = "INSERT INTO ci_verify_info (repo, status, duration, cause, pipeline_url, target_commit, target_branch, source_branch, failed_pipelines) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)"

        for idx in range(len(merge_requests)):
            mr = merge_requests[idx]
            if "pipeline_url" in mr:
                msg = (
                    '<a href="'
                    + mr["pipeline_url"]
                    + '" target="_blank">'
                    + mr["repo_name"]
                    + "</a>: "
                    + mr["pipeline_status"]
                    + ", duration: "
                    + mr["duration"]
                )
                print(msg)
                description += msg + "<br>"
                if idx != 0:
                    msg_list.append("---")
                msg_list.append(
                    f"### [{mr['repo_name']}]({mr['pipeline_url']}): {mr['pipeline_status']}, target_commit: {mr['head_commit']}, duration: {mr['duration']}"
                )
                failed_pipelines = []
                if is_failed:
                    statuses = get_pipeline_statues_by_commitid(
                        mr["project_id"], mr["commit_id"], mr["ref"]
                    )
                    for status in statuses:
                        if status["status"] in ["failed"]:
                            msg_list.append(
                                f"[{status['name']}]({status['target_url']}): {status['status']}"
                            )
                            failed_pipelines.append(status["target_url"])
                values.append(
                    (
                        mr["repo_name"],
                        mr["pipeline_status"],
                        mr["duration_seconds"],
                        cause_map[mr["pipeline_status"]],
                        mr["pipeline_url"],
                        mr["head_commit"],
                        mr["target_branch"],
                        mr["source_branch"],
                        json.dumps(failed_pipelines),
                    )
                )

        with open("desc.txt", "w") as file:
            file.write(description)

        delimiter = " \n\n "
        msg = delimiter.join(msg_list)
        send_dingtalk_msg(msg)

        save_info_to_database(sql, values)
        # close_merge_request()
        sys.exit(0)


def check_pipeline_status():
    print("check pipeline status")
    if len(merge_requests) == 0:
        print("CI verify failed.<br><br>")
        print("There is no any merge request was created!<br>")
        sys.exit(0)

    global run_time
    # If it doesn’t finish within 6 hours, it will be considered a failure.
    if run_time > 11 * 60 * 60:
        set_result_by_pipeline(False)

    run_time = run_time + time_interval
    set_result_by_pipeline(True)


def save_info_to_database(sql, values):
    try:
        db = mysql.connector.connect(
            user="swqa_ci",
            password=database_pwd,
            host="**************",
            port=3306,
            database="swqa_ci",
            charset="utf8mb4",
        )
        cursor = db.cursor()
        cursor.executemany(sql, values)
        db.commit()
        print("save to database success.")
    except Exception as e:
        print(e)
        print("save to database failed.")
        sys.exit(1)


if __name__ == "__main__":
    create_merge_request()
    status_timer = Timer(60, check_pipeline_status)
    status_timer.start()
