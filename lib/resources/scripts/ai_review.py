import sys
import json
import requests

output_file_path = sys.argv[1]
output_files = []
headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJtdGhyZWFkcyIsImlzcyI6Im10aHJlYWRzIiwic3ViIjoibXVzYWNoYXQifQ.0cp9hDHvO8feKT6fcYVooPEhDYxKIaDFlExd6wb6jUk",
}

with open(output_file_path, "r", encoding="utf-8") as f:
    for line in f:
        output_files.append(line.strip())

print(output_files)

for file in output_files:
    try:
        with open(file, "r", encoding="utf-8") as f:
            code_content = f.read()
            res = requests.post(
                "https://aibook-api.mthreads.com:30256/api/v1/chat/completions",
                headers=headers,
                data=json.dumps(
                    {
                        "model": "DeepSeek-R1-Distill-Llama-70B",
                        "stream": False,
                        "messages": [
                            {
                                "role": "user",
                                "content": f"保留原内容，逐行分析下面的差量代码是否存在问题并使用英文给出分析结果: {code_content}",
                            }
                        ],
                    }
                ),
            )
            res_data = res.json()
            with open(f"ai_review_{file}.txt", "w", encoding="utf-8-sig") as f1:
                f1.write(res_data["choices"][0]["message"]["content"])
    except Exception as e:
        print(e)
