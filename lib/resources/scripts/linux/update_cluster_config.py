import argparse
import subprocess
import yaml
from urllib.parse import urlparse

yaml_file = "clusterconfig.yaml"


def parse_oss_url(url):
    """解析OSS URL为配置组成部分"""
    parsed = urlparse(url)
    if not parsed.netloc.endswith("oss.mthreads.com"):
        raise ValueError("仅支持 oss.mthreads.com 的URL")

    path_parts = parsed.path.strip("/").split("/")
    if len(path_parts) < 2:
        raise ValueError("URL路径格式不正确")

    return {"bucketName": path_parts[0], "objectName": "/".join(path_parts[1:])}


def update_oss_config(component, oss_params):
    """更新组件的OSS配置"""
    if "oss" not in component:
        component["oss"] = {}

    component["oss"].update(
        {
            "bucketName": oss_params["bucketName"],
            "objectName": oss_params["objectName"],
            # 保留其他已有字段
            "endpoint": "oss.mthreads.com",
            "id": "mtoss",
            "secret": "mtoss123",
        }
    )


def download_clusterconfig(context):
    try:
        # 使用 kubectl 获取配置并写入文件
        result = subprocess.run(
            ["kubectl", "get", "clusterconfig", "-o", "yaml", "--context", context],
            capture_output=True,
            text=True,
            check=True,
        )
        with open(yaml_file, "w") as f:
            f.write(result.stdout)
        print(f"ClusterConfig has download to {yaml_file}")
    except subprocess.CalledProcessError as e:
        print(f"Error during kubectl get clusterconfig: {e.stderr}")
        exit(1)


def modify_clusterconfig(linuxDdkPackageUrl, mtmlpackageUrl, context):
    try:
        musa_params = parse_oss_url(linuxDdkPackageUrl)
        mtml_params = parse_oss_url(mtmlpackageUrl)
        with open(yaml_file, "r") as f:
            config = yaml.safe_load(f)
        components = (
            config.setdefault("items", {})[0]
            .setdefault("spec", {})
            .setdefault("nodes", {})[0]
            .setdefault("drivers", {})
        )
        for comp in components:
            if comp.get("kind") == "musa":
                update_oss_config(comp, musa_params)
            elif comp.get("kind") == "libmtml.so":
                update_oss_config(comp, mtml_params)

        with open(yaml_file, "w") as f:
            yaml.dump(config, f)
        print(f"ClusterConfig is update to {yaml_file}")
    except Exception as e:
        print(f"Error during YAML modification: {e}")
        exit(1)


# Step 3: 使用 kubectl apply -f 将修改后的文件应用到集群
def apply_clusterconfig(context):
    try:
        # 使用 kubectl apply 命令将修改后的文件应用到集群
        result = subprocess.run(
            ["kubectl", "apply", "-f", yaml_file, "--context", context],
            capture_output=True,
            text=True,
            check=True,
        )
        print(f"ClusterConfig is apply: {result.stdout}")
    except subprocess.CalledProcessError as e:
        print(f"Error during kubectl apply: {e.stderr}")
        exit(1)


# 主函数执行
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="operator Update")
    parser.add_argument(
        "-l", "--linuxDdkPackageUrl", required=True, help="linuxDdkPackageUrl"
    )
    parser.add_argument("-m", "--mtmlpackageUrl", required=True, help="mtmlpackageUrl")
    parser.add_argument(
        "-c",
        "--context",
        required=True,
        help="Kubernetes context include cdfarm dailyfarm releasefarm",
    )

    args = parser.parse_args()
    download_clusterconfig(context=args.context)
    modify_clusterconfig(
        linuxDdkPackageUrl=args.linuxDdkPackageUrl,
        mtmlpackageUrl=args.mtmlpackageUrl,
        context=args.context,
    )
    apply_clusterconfig(context=args.context)
