#!/bin/bash

test_count=$1
logfile=${2:-"TestResult.log"}
check_hwr=${3:-0}   # 0: no check, 1: check

num=$(grep -cE 'Failed:[[:space:]]+0/' ${logfile})
echo "$num"
if [[ $num -ne $test_count ]]; then
    grep 'Fail (' ${logfile} -B 2 >cts_failcase.txt
    head -30 cts_failcase.txt
    echo "CTS Test Failure"
    exit 1
else
    echo "CTS Test Success"
    if [[ $check_hwr -eq 1 ]]; then
        echo "Checking for HWR (Hardware Reset) issues..."
        if journalctl -b | grep -q "reboot fw"; then
            echo "HWR detected in system logs!"
            exit 1
        else
            echo "Success: All tests passed and no HWR detected."
            exit 0
        fi
    else
        echo "Success: CTS passed (HWR check skipped)."
        exit 0
    fi
fi
