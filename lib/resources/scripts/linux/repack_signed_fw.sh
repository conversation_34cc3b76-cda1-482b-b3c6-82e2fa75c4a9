#!/bin/bash

# 定义常量
SIGN_HEADER_LEN=64
SIGN_SIG_LEN=256
SIGN_EXTRA_LEN=64

# 参数检查
if [ $# -ne 1 ]; then
    echo "Usage: $0 <zip_file>"
    exit 1
fi

ZIP_FILE="$1"

# 检查文件是否存在
if [ ! -f "$ZIP_FILE" ]; then
    echo "Error: File $ZIP_FILE not found"
    exit 1
fi

# 创建临时目录
TEMP_DIR=$(mktemp -d)
trap 'rm -rf "$TEMP_DIR"' EXIT

# 解压ZIP文件
echo "Extracting $ZIP_FILE..."
unzip -q "$ZIP_FILE" -d "$TEMP_DIR"
if [ $? -ne 0 ]; then
    echo "Error: Failed to extract $ZIP_FILE"
    exit 1
fi

# 查找所需的文件
BIN_FILE=$(find "$TEMP_DIR" -name "*.bin" | head -1)
SIG_FILE=$(find "$TEMP_DIR" -name "*.sig" | head -1)
EXTRA_FILE=$(find "$TEMP_DIR" -name "*.extra" | head -1)
METADATA_FILE=$(find "$TEMP_DIR" -name "metadata.json" | head -1)

# 检查必需文件是否存在
if [ -z "$BIN_FILE" ] || [ -z "$SIG_FILE" ] || [ -z "$EXTRA_FILE" ]; then
    echo "Error: Required files (.bin, .sig, .extra) not found in the archive"
    exit 1
fi

# 检查文件大小
SIG_SIZE=$(stat -c%s "$SIG_FILE")
EXTRA_SIZE=$(stat -c%s "$EXTRA_FILE")

if [ "$SIG_SIZE" -ne "$SIGN_SIG_LEN" ]; then
    echo "Error: .sig file size mismatch. Expected: $SIGN_SIG_LEN, Got: $SIG_SIZE"
    exit 1
fi

if [ "$EXTRA_SIZE" -ne "$SIGN_EXTRA_LEN" ]; then
    echo "Error: .extra file size mismatch. Expected: $SIGN_EXTRA_LEN, Got: $EXTRA_SIZE"
    exit 1
fi

# 确定头部内容
HEADER_CONTENT="sig"
if [ -n "$METADATA_FILE" ]; then
    if grep -q "encrypt" "$METADATA_FILE"; then
        HEADER_CONTENT="encrypt"
    fi
fi

echo "Using header content: $HEADER_CONTENT"

# 获取.bin文件名（包含路径和扩展名）
BIN_BASENAME=$(basename "$BIN_FILE")
OUTPUT_FILE="$BIN_BASENAME"

# 创建输出文件
echo "Creating output file: $OUTPUT_FILE"

# 创建64字节的头部
printf "%s%0*d" "$HEADER_CONTENT" $((SIGN_HEADER_LEN - ${#HEADER_CONTENT})) 0 | tr '0-9' '\0' > "$OUTPUT_FILE"

# 拼接文件: header + .bin + .sig + .extra
cat "$BIN_FILE" "$SIG_FILE" "$EXTRA_FILE" >> "$OUTPUT_FILE"

# 显示结果信息
BIN_SIZE=$(stat -c%s "$BIN_FILE")
TOTAL_SIZE=$((SIGN_HEADER_LEN + BIN_SIZE + SIG_SIZE + EXTRA_SIZE))

echo "Successfully created $OUTPUT_FILE"
echo "File composition:"
echo "  Header ($HEADER_CONTENT): $SIGN_HEADER_LEN bytes"
echo "  Bin file: $BIN_SIZE bytes"
echo "  Sig file: $SIG_SIZE bytes"
echo "  Extra file: $EXTRA_SIZE bytes"
echo "  Total size: $TOTAL_SIZE bytes"
