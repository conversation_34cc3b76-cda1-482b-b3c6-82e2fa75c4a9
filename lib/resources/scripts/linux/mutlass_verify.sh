#!/bin/bash

INPUT_FILE="./tools/profiler/test_mutlass_profiler_gemm.gemm.junit.xml"

if [[ ! -f "$INPUT_FILE" ]]; then
  echo "Error: File '$INPUT_FILE' does not exist."
  exit 1
fi

error_found=0
last_operation=""

while IFS= read -r line; do
  if [[ $line =~ Operation:\ (.+) ]]; then
    last_operation="${BASH_REMATCH[1]}"
  fi

  if [[ $line =~ Disposition:\ ([^[:space:]]+) ]]; then
    disposition=${BASH_REMATCH[1]}

    if [[ "$disposition" != "Passed" ]]; then
      echo "Error: Disposition is '$disposition' instead of 'Passed' for Operation '$last_operation"
      error_found=1
    fi
  fi
done < "$INPUT_FILE"

if [[ $error_found -eq 0 ]]; then
  echo "All Disposition fields are 'Passed'."
else
  echo "One or more Disposition fields are not 'Passed'."
  exit 1
fi
