import argparse
from datetime import datetime

from kubernetes import client, config

# Define constants
CONTEXT = "shfarm"
NAMESPACE = "mt-jenkins-jobs"
DEFAULT_RETURN_TIME = 21  # Default return time, default is 21:00

# Define default minimum node counts
DEFAULT_MIN_BUILDSERVER = 4
DEFAULT_MIN_VPS = 20
DEFAULT_COMPUTEBUILD_RATIO = 0.5

# Load Kubernetes config with specified context
config.load_kube_config(context=CONTEXT)

# Initialize Kubernetes API
v1 = client.CoreV1Api()


def adjust_node_labels(return_hour, min_buildserver, min_vps, computebuild_ratio):
    # Get current time
    current_time = datetime.now()
    current_hour = current_time.hour

    # Print current time
    print(f"Current time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")

    # If current time is return hour, execute special logic
    if current_hour == return_hour:
        print(f"Reached return time {return_hour}:00, adjusting node labels...")
        # Get all current nodes (only count nodes with mt=buildserver and mt=vps)
        nodes = v1.list_node().items
        filtered_nodes = [
            node
            for node in nodes
            if node.metadata.labels.get("mt") in ["buildserver", "vps"]
        ]

        # Count current mt=buildserver and mt=vps nodes
        current_build_nodes = [
            node
            for node in filtered_nodes
            if node.metadata.labels.get("mt") == "buildserver"
        ]
        current_vps_nodes = [
            node for node in filtered_nodes if node.metadata.labels.get("mt") == "vps"
        ]

        # Print current label counts
        print(f"Current mt=buildserver node count: {len(current_build_nodes)}")
        print(f"Current mt=vps node count: {len(current_vps_nodes)}")

        # If current mt=buildserver nodes exceed required, adjust excess to mt=vps
        if len(current_build_nodes) > min_buildserver:
            nodes_to_adjust = current_build_nodes[min_buildserver:]
            print(
                f"Adjusting {len(nodes_to_adjust)} nodes from mt=buildserver to mt=vps"
            )
            for node in nodes_to_adjust:
                patch_node_label(node.metadata.name, {"mt": "vps"})

        # If current mt=buildserver nodes are insufficient, adjust some mt=vps to mt=buildserver
        elif len(current_build_nodes) < min_buildserver:
            needed = min_buildserver - len(current_build_nodes)
            nodes_to_adjust = current_vps_nodes[:needed]
            print(
                f"Adjusting {len(nodes_to_adjust)} nodes from mt=vps to mt=buildserver"
            )
            for node in nodes_to_adjust:
                patch_node_label(node.metadata.name, {"mt": "buildserver"})

        # If current mt=buildserver node count matches required, no adjustment needed
        else:
            print(
                "Current mt=buildserver node count meets requirements, no adjustment needed."
            )

        # Adjust computebuild labels after buildserver adjustment
        adjust_computebuild_labels(current_build_nodes, computebuild_ratio)

        print("Adjustment complete.")
        return

    # Get Pending Pods in namespace=mt-jenkins-jobs
    pending_pods = v1.list_namespaced_pod(
        namespace=NAMESPACE, field_selector="status.phase=Pending"
    ).items

    # If no Pending Pods, exit
    if not pending_pods:
        print("No Pending Pods, no adjustment needed.")
        return

    # Count build- prefixed Pending Pods
    build_pending = len(
        [pod for pod in pending_pods if pod.metadata.name.startswith("build-")]
    )

    # Count non build- prefixed Pending Pods
    non_build_pending = len(pending_pods) - build_pending

    # Print Pending Pod ratios
    print(f"Current Pending Pod count: {len(pending_pods)}")
    print(
        f"build- prefixed Pending Pod count: {build_pending} (ratio: {build_pending / len(pending_pods) * 100:.2f}%)"
    )
    print(
        f"Non build- prefixed Pending Pod count: {non_build_pending} (ratio: {non_build_pending / len(pending_pods) * 100:.2f}%)"
    )

    # Calculate ratios
    if build_pending == 0:
        build_ratio = 0
        non_build_ratio = 1
    elif non_build_pending == 0:
        build_ratio = 1
        non_build_ratio = 0
    else:
        build_ratio = build_pending / len(pending_pods)
        non_build_ratio = non_build_pending / len(pending_pods)

    # Get total node count (only count mt=buildserver and mt=vps nodes)
    nodes = v1.list_node().items
    filtered_nodes = [
        node
        for node in nodes
        if node.metadata.labels.get("mt") in ["buildserver", "vps"]
    ]
    total_nodes = len(filtered_nodes)

    # Calculate target node counts
    build_nodes = int(total_nodes * build_ratio)
    vps_nodes = int(total_nodes * non_build_ratio)

    # Ensure minimum node counts
    if build_nodes < min_buildserver:
        build_nodes = min_buildserver
        vps_nodes = total_nodes - build_nodes
    if vps_nodes < min_vps:
        vps_nodes = min_vps
        build_nodes = total_nodes - vps_nodes

    # Adjust node labels
    adjust_labels_based_on_need(
        filtered_nodes, build_nodes, vps_nodes, computebuild_ratio
    )


def adjust_computebuild_labels(build_nodes, computebuild_ratio):
    """Adjust computebuild labels to ensure specified ratio of buildserver nodes have the label"""
    # Calculate how many nodes should have computebuild label
    target_count = int(len(build_nodes) * computebuild_ratio)

    # Count current nodes with computebuild label
    current_computebuild_nodes = [
        node
        for node in build_nodes
        if node.metadata.labels.get("mtswqa") == "computebuild"
    ]

    # If too many nodes have computebuild label, remove excess
    if len(current_computebuild_nodes) > target_count:
        nodes_to_remove = current_computebuild_nodes[target_count:]
        print(f"Removing mtswqa=computebuild label from {len(nodes_to_remove)} nodes")
        for node in nodes_to_remove:
            patch_node_label(node.metadata.name, {"mtswqa": None})

    # If insufficient nodes have computebuild label, add
    elif len(current_computebuild_nodes) < target_count:
        needed = target_count - len(current_computebuild_nodes)
        # Select nodes without computebuild label
        nodes_to_add = [
            node
            for node in build_nodes
            if node.metadata.labels.get("mtswqa") != "computebuild"
        ][:needed]
        print(f"Adding mtswqa=computebuild label to {len(nodes_to_add)} nodes")
        for node in nodes_to_add:
            patch_node_label(node.metadata.name, {"mtswqa": "computebuild"})


def adjust_labels_based_on_need(
    nodes, build_nodes_needed, vps_nodes_needed, computebuild_ratio
):
    """Adjust labels based on required node counts, prioritizing nodes with mismatched labels"""
    # Count current labels
    current_build_nodes = [
        node for node in nodes if node.metadata.labels.get("mt") == "buildserver"
    ]
    current_vps_nodes = [
        node for node in nodes if node.metadata.labels.get("mt") == "vps"
    ]

    # Print current label counts
    print(f"Current mt=buildserver node count: {len(current_build_nodes)}")
    print(f"Current mt=vps node count: {len(current_vps_nodes)}")

    # If too many buildserver nodes, adjust to vps
    if len(current_build_nodes) > build_nodes_needed:
        nodes_to_adjust = current_build_nodes[build_nodes_needed:]
        print(f"Adjusting {len(nodes_to_adjust)} nodes from mt=buildserver to mt=vps")
        for node in nodes_to_adjust:
            patch_node_label(node.metadata.name, {"mt": "vps", "mtswqa": None})
            current_vps_nodes.append(node)
            current_build_nodes.remove(node)

    # If too many vps nodes, adjust to buildserver
    if len(current_vps_nodes) > vps_nodes_needed:
        nodes_to_adjust = current_vps_nodes[vps_nodes_needed:]
        print(f"Adjusting {len(nodes_to_adjust)} nodes from mt=vps to mt=buildserver")
        for node in nodes_to_adjust:
            patch_node_label(node.metadata.name, {"mt": "buildserver"})
            current_build_nodes.append(node)
            current_vps_nodes.remove(node)

    # Adjust computebuild labels after buildserver adjustment
    adjust_computebuild_labels(current_build_nodes, computebuild_ratio)

    print("Adjustment complete:")
    print(f"mt=buildserver node count: {len(current_build_nodes)}")
    print(f"mt=vps node count: {len(current_vps_nodes)}")


def patch_node_label(node_name, labels):
    """Update node labels"""
    body = {"metadata": {"labels": labels}}
    v1.patch_node(node_name, body)


if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Adjust Kubernetes node labels")
    parser.add_argument(
        "--return_hour",
        type=int,
        default=DEFAULT_RETURN_TIME,
        help=f"Return time (default: {DEFAULT_RETURN_TIME})",
    )
    parser.add_argument(
        "--min_buildserver",
        type=int,
        default=DEFAULT_MIN_BUILDSERVER,
        help=f"Minimum buildserver node count (default: {DEFAULT_MIN_BUILDSERVER})",
    )
    parser.add_argument(
        "--min_vps",
        type=int,
        default=DEFAULT_MIN_VPS,
        help=f"Minimum vps node count (default: {DEFAULT_MIN_VPS})",
    )
    parser.add_argument(
        "--computebuild_ratio",
        type=float,
        default=DEFAULT_COMPUTEBUILD_RATIO,
        help=f"Computebuild label ratio (default: {DEFAULT_COMPUTEBUILD_RATIO})",
    )
    args = parser.parse_args()

    # Call main function with arguments
    adjust_node_labels(
        args.return_hour, args.min_buildserver, args.min_vps, args.computebuild_ratio
    )
