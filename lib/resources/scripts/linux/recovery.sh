#!/bin/bash

ARCH=$(uname -m)

dpkg --configure -a || :

echo "Restoring /etc/ld.so.conf.d/${ARCH}-linux-gnu.conf"
cat <<EOF >/etc/ld.so.conf.d/${ARCH}-linux-gnu.conf
# Multiarch support
/usr/lib/${ARCH}-linux-gnu/musa      # Add this line
/usr/local/lib/${ARCH}-linux-gnu
/lib/${ARCH}-linux-gnu
/usr/lib/${ARCH}-linux-gnu
/usr/local/lib/
EOF

echo "Restoring /usr/bin/X configuration"
cat <<'EOF' >/usr/bin/X
#!/bin/sh
#
# Execute Xorg.wrap if it exists otherwise execute Xorg directly.
# This allows distros to put the suid wrapper in a separate package.

basedir=/usr/lib/xorg
# basedir=/usr/local/bin
if [ -x "$basedir"/Xorg.wrap ]; then
    exec "$basedir"/Xorg.wrap "$@"
else
    exec "$basedir"/Xorg "$@"
fi
EOF

echo "Removing UMD residual files"
UMD_DIR="/usr/lib/${ARCH}-linux-gnu/dri"
rm -rf $UMD_DIR/kms_swrast_musa_dri.so || :
rm -rf $UMD_DIR/mtgpu_dri.so || :
rm -rf $UMD_DIR/musa_dri.so || :
rm -rf $UMD_DIR/swrast_musa_dri.so || :
rm -rf /usr/lib/${ARCH}-linux-gnu/musa || :
rm /usr/local/lib/* || :
rm -rf /etc/X11/xorg.conf || :

# fix python env
apt_requirements="python3-pip python3-numpy python3-mako"
apt --fix-broken install -y
apt install ${apt_requirements} -y
py_requirements="pytest==7.1.3 allure-pytest pandas attrs"
pip3 install ${py_requirements} --index="http://nexus.infra.shg1.mthreads.com/repository/pypi-public/simple" --trusted-host=nexus.infra.shg1.mthreads.com

ldconfig

echo "Removing MTGPU residual files"
rm -rf /etc/vulkan/icd.d/ || :
rm -rf /var/lib/dkms/mtgpu || :
rm -rf /lib/firmware/mthreads/ || :
dpkg -P mtgpu || :
dpkg -P musa || :
rpm -e mtgpu || :
rpm -e musa || :
find /lib/modules/"$(uname -r)" -name 'mtgpu.ko' -exec rm -f {} \; || :
find /var/lib -name 'mtgpu.ko' -exec rm -f {} \; || :
find /usr/lib -name 'mtgpu.ko' -exec rm -f {} \; || :
echo > /var/log/kern.log || :
echo > /var/log/sys.log || :

echo "Restoring APT settings"
# sed -i "/^[^#].*$(lsb_release -cs)-updates/s/^/# /" /etc/apt/sources.list
sed -i "/^[^#]/ {/nexus\.shg1\.mthreads\.com/! {/professional-packages\.chinauos\.com/! s/^/# /}}" /etc/apt/sources.list
rm -rf /etc/apt/sources.list.d/* || :

echo "Disabling MTGPU autoload on boot"
BLACKLIST_CONF="/etc/modprobe.d/blacklist.conf"
if [ ! -f $BLACKLIST_CONF ]; then
    echo "blacklist mtgpu" >$BLACKLIST_CONF
elif ! grep -q "^blacklist mtgpu" $BLACKLIST_CONF; then
    echo "blacklist mtgpu" >>$BLACKLIST_CONF
fi

echo "Disabling and stopping lightdm and gdm"
for service in lightdm gdm gdm3; do
    systemctl stop $service || :
    systemctl disable $service || :
done

# Removing specified packages
for pkg in mt-media ffmpeg-mt-build libva2-mt-build; do
    dpkg -P $pkg || :
done

echo "Environment restoration complete. If issues persist, please try rebooting the system."
