import argparse

from kubernetes import client, config

CONTEXT = "shfarm"
NAMESPACE = "mt-jenkins-jobs"


def load_kube_config():
    try:
        config.load_kube_config(context=CONTEXT)
    except Exception as e:
        print(f"config load failed: {str(e)}")
        return False
    return True


def get_pending_jobs(keyword):
    v1 = client.CoreV1Api()
    try:
        pods = v1.list_namespaced_pod(
            namespace=NAMESPACE,
            field_selector="metadata.namespace=={}".format(NAMESPACE),
        ).items
    except client.ApiException as e:
        print(f"API 请求异常: {e.status} - {e.reason}")
        return []
    return [
        p
        for p in pods
        if keyword in p.metadata.name.lower() and p.status.phase == "Pending"
    ]


def count_pending_job(keyword):
    if not load_kube_config():
        return
    build_pods = get_pending_jobs(keyword)
    build_count = len(build_pods)
    print(f"检测到含 '{keyword}' 的 Pod 数量: {build_count}")
    return build_count


def update_node_label(node_ip, label_key, label_value):
    if not load_kube_config():
        return
    v1 = client.CoreV1Api()
    try:
        node_ip_list = [ip.strip() for ip in node_ip.split(",")]
        nodes = v1.list_node().items
        target_node = [
            node
            for node in nodes
            if any(
                addr.type == "InternalIP" and addr.address in node_ip_list
                for addr in node.status.addresses
            )
        ]
        body = {"metadata": {"labels": {label_key: label_value}}}
        for node in target_node:
            node_name = node.metadata.name
            v1.patch_node(node_name, body)
            print(f"node {node_name} label {label_key} is update to {label_value}")
        return True
    except client.ApiException:
        return False


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Change node labels based on pending job count."
    )
    parser.add_argument(
        "-n",
        "--node-ips",
        required=True,
        help="Comma-separated list of node IP addresses.",
    )
    parser.add_argument(
        "-j",
        "--job-keyword",
        required=True,
        help="Keyword to filter pending jobs (e.g., 'build' or 'build-mudnn').",
    )
    parser.add_argument(
        "-c",
        "--threshold-count",
        type=int,
        required=True,
        help="Threshold count of pending jobs to trigger label change.",
    )
    parser.add_argument(
        "-k",
        "--label-key",
        required=True,
        help="The key of the label to be updated (e.g., 'mt').",
    )
    parser.add_argument(
        "-v",
        "--label-value",
        required=True,
        help="The new value for the label (e.g., 'buildServer').",
    )
    parser.add_argument(
        "-o",
        "--operation",
        choices=["greater", "less"],
        default=None,
        help="Comparison operation: 'greater' (default) or 'less', or leave empty for other operations.",
    )
    args = parser.parse_args()
    if not args.operation:
        update_node_label(args.node_ips, args.label_key, args.label_value)
    else:
        job_count = count_pending_job(args.job_keyword)
        if (args.operation == "greater" and job_count >= args.threshold_count) or (
            args.operation == "less" and job_count < args.threshold_count
        ):
            update_node_label(args.node_ips, args.label_key, args.label_value)
