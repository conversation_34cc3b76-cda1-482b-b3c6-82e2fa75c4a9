from datetime import datetime, timedelta

import pymysql.cursors
import pytz
import requests

PROJECTS = {
    "linux-ddk": 1568,
    "gr-kmd": 534,
    "gr-umd": 535,
    "mt-media-driver": 648,
    "m3d": 538,
    "wddm": 536,
    "MUSA-Runtime": 504,
    "DirectStream": 557,
    "ogl": 533,
    "mt-management": 658,
    "mthreads-gmi": 629,
}
HOST = "sh-code.mthreads.com"
PROTOCOL = "https"
API_VERSION = "api/v4"


# https://sh-code.mthreads.com/api/v4/projects/534/pipelines
def get_pipelines(session, project_name):
    url = f"{PROTOCOL}://{HOST}/{API_VERSION}/projects/{PROJECTS[project_name]}/pipelines?order_by=id&sort=desc&per_page=100"
    result = []

    now = datetime.now(pytz.timezone("Asia/Shanghai"))
    # start_of_week = now - timedelta(days=7) - timedelta(days=now.weekday())
    start_of_week = now - timedelta(days=now.weekday())
    start_of_week = start_of_week.replace(hour=0, minute=0, second=0, microsecond=0)
    page = 1
    time_format = "%Y-%m-%dT%H:%M:%S.%f%z"
    stop = False
    while not stop:
        res = session.get(f"{url}&page={page}")
        for pipeline in res.json():
            if datetime.strptime(pipeline["created_at"], time_format) < start_of_week:
                stop = True
                break
            result.append(
                {
                    "id": pipeline["id"],
                    "repo_name": project_name,
                    "status": pipeline["status"],
                    "ref": pipeline["ref"],
                    "sha": pipeline["sha"],
                    "created_at": pipeline["created_at"],
                    "updated_at": pipeline["updated_at"],
                }
            )

        page += 1
    return result


def write_db(rows):
    connection = pymysql.connect(
        host="**************",
        user="swqa_ci",
        password="Passw0rd!",
        database="swqa_ci",
        charset="utf8mb4",
        cursorclass=pymysql.cursors.DictCursor,
    )
    with connection.cursor() as cursor:
        # Create a new record
        sql = "INSERT IGNORE INTO `pipelines` (`id`, `repo_name`, `status`, `ref`, `sha`, `created_at`, `updated_at`) VALUES (%s, %s, %s, %s, %s, %s, %s)"
        data = [tuple(v for v in row.values()) for row in rows]
        cursor.executemany(sql, data)

    # connection is not autocommit by default. So you must commit to save
    # your changes.
    connection.commit()


def main():
    session = requests.Session()
    session.headers.update({"PRIVATE-TOKEN": "**************************"})
    for project_name in PROJECTS:
        data = get_pipelines(session, project_name)
        write_db(data)


if __name__ == "__main__":
    main()
