# MTBios Sign and Repack Tool Makefile

BINARY_NAME=mtbios-sign-repack
VERSION?=1.0.0
BUILD_TIME=$(shell date +%Y-%m-%d_%H:%M:%S)
GIT_COMMIT=$(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# Build flags
LDFLAGS=-ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT) -s -w"

# Default target
.PHONY: all
all: clean deps build

# Download dependencies
.PHONY: deps
deps:
	$(GOMOD) download
	$(GOMOD) tidy

# Build for current platform
.PHONY: build
build:
	$(GOBUILD) $(LDFLAGS) -o $(BINARY_NAME) .

# Build for all platforms
.PHONY: build-all
build-all: clean deps
	# Linux
	GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o bin/linux/$(BINARY_NAME) .
	GOOS=linux GOARCH=arm64 $(GOBUILD) $(LDFLAGS) -o bin/linux/$(BINARY_NAME)-arm64 .

	# Windows
	GOOS=windows GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o bin/windows/$(BINARY_NAME).exe .

	# macOS
	GOOS=darwin GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o bin/darwin/$(BINARY_NAME) .
	GOOS=darwin GOARCH=arm64 $(GOBUILD) $(LDFLAGS) -o bin/darwin/$(BINARY_NAME)-arm64 .

# Build for Jenkins shared library
.PHONY: build-jenkins
build-jenkins: clean deps
	mkdir -p ../../../resources/linux
	mkdir -p ../../../resources/windows
	mkdir -p ../../../resources/darwin

	# Build for different platforms
	GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o ../../../resources/linux/$(BINARY_NAME) .
	GOOS=windows GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o ../../../resources/windows/$(BINARY_NAME).exe .
	GOOS=darwin GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o ../../../resources/darwin/$(BINARY_NAME) .

	@echo "Binaries built for Jenkins shared library:"
	@ls -la ../../../resources/*/$(BINARY_NAME)*

# Test
.PHONY: test
test:
	$(GOTEST) -v ./...

# Clean
.PHONY: clean
clean:
	$(GOCLEAN)
	rm -f $(BINARY_NAME)
	rm -rf bin/

# Install locally
.PHONY: install
install: build
	sudo cp $(BINARY_NAME) /usr/local/bin/

# Show help
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  all          - Clean, download deps, and build"
	@echo "  deps         - Download and tidy dependencies"
	@echo "  build        - Build for current platform"
	@echo "  build-all    - Build for all platforms"
	@echo "  build-jenkins- Build for Jenkins shared library"
	@echo "  test         - Run tests"
	@echo "  clean        - Clean build artifacts"
	@echo "  install      - Install binary to /usr/local/bin"
	@echo "  help         - Show this help"
