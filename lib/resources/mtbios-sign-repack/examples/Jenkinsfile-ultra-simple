@Library('swqa-ci')

// MTBios Firmware Signing Pipeline - Ultra Simple Version
// This pipeline uses the enhanced mtbiosSign shared library to sign all firmware files

node {
    // Parameters
    def USERNAME = params.USERNAME ?: '<EMAIL>'
    def PASSWORD = 'xxxxxxx'
    def DEPARTMENT = params.DEPARTMENT ?: 'sw'
    def COOKIE_FILE = './mtbios-login.cookie'
    def LINUX_DDK_PATH = 'workspace/linux-ddk'
    def FIRMWARE_SEARCH_PATH = 'lib/firmware/mthreads'

    try {
        stage('Prepare') {
            echo '=== MTBios Firmware Signing Pipeline Started ==='
            echo "Username: ${USERNAME}"
            echo "Department: ${DEPARTMENT}"
            echo "Linux DDK Path: ${LINUX_DDK_PATH}"

            // Check if linux-ddk directory exists
            if (!fileExists(LINUX_DDK_PATH)) {
                error "Linux DDK directory not found: ${LINUX_DDK_PATH}"
            }

            // Check if MTBios tool is available
            def binaryPath = mtbiosSign.checkTool()
            if (!binaryPath) {
                error 'MTBios signing tool is not available'
            }

            echo 'Environment prepared successfully'
        }

        stage('Login') {
            echo 'Logging in to MTBios signing server...'

            // Check if we need to login
            def needLogin = !fileExists(COOKIE_FILE) || params.FORCE_LOGIN

            if (needLogin) {
                echo 'Performing login...'
                mtbiosSign.login(USERNAME, PASSWORD, DEPARTMENT, COOKIE_FILE)
                echo 'Login successful'
            } else {
                echo 'Using existing credentials'
            }
        }

        stage('Sign All Firmware Files') {
            echo 'Finding and signing all firmware files...'

            // Change to linux-ddk directory
            dir(LINUX_DDK_PATH) {
                // Use the enhanced shared library function to sign all firmware files
                def results = mtbiosSign.signFirmwareInDirectory(
                    FIRMWARE_SEARCH_PATH,     // searchPath
                    'mtfw-gen*.bin',          // pattern
                    'model',                  // excludePattern
                    '../temp-signed-output',  // tempOutputDir
                    "../${COOKIE_FILE}"       // cookieFile
                )

                echo 'Signing completed!'
                echo "Total files: ${results.totalFiles}"
                echo "Successfully signed: ${results.signedCount}"
                echo "Failed: ${results.failedFiles.size()}"

                if (results.failedFiles.size() > 0 && results.signedCount == 0) {
                    error 'All firmware signing operations failed!'
                }
            }
        }

        stage('Verify') {
            echo 'Verifying signed firmware files...'

            dir(LINUX_DDK_PATH) {
                // List all firmware files with their sizes
                sh """
                    echo "=== Signed Firmware Files ==="
                    find ${FIRMWARE_SEARCH_PATH}/ -iname 'mtfw-gen*.bin' | grep -v model | while read file; do
                        if [ -f "\$file" ]; then
                            size=\$(stat -c%s "\$file")
                            hash=\$(sha256sum "\$file" | cut -d' ' -f1 | cut -c1-16)
                            echo "✓ \$file: \$size bytes, SHA256: \$hash..."
                        else
                            echo "✗ \$file: File missing!"
                        fi
                    done
                """
            }

            echo 'Verification completed'
        }
    } catch (Exception e) {
        echo "Pipeline failed with error: ${e.getMessage()}"
        currentBuild.result = 'FAILURE'
        throw e
    } finally {
        stage('Cleanup') {
            echo 'Cleaning up temporary files...'

            // Clean up temporary output directory
            sh 'rm -rf temp-signed-output'

            // Keep cookie file for future use unless explicitly requested to clean
            if (params.CLEAN_COOKIE && fileExists(COOKIE_FILE)) {
                sh "rm -f ${COOKIE_FILE}"
                echo 'Cleaned up cookie file'
            }

            echo 'Cleanup completed'
        }

        // Final status
        if (currentBuild.result != 'FAILURE') {
            echo '=== MTBios Firmware Signing Pipeline Completed Successfully ==='
        } else {
            echo '=== MTBios Firmware Signing Pipeline Failed ==='
        }
    }
}
