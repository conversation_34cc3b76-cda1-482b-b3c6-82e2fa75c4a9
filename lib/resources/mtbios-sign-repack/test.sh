#!/bin/bash

# MTBios Sign and Repack Tool Test Script

set -e

TOOL="./mtbios-sign-repack"
TEST_DIR="./test-output"
COOKIE_FILE="./test-login.cookie"

echo "=== MTBios Sign and Repack Tool Test ==="

# Check if tool exists
if [ ! -f "$TOOL" ]; then
    echo "Error: Tool not found. Please build it first with 'make build'"
    exit 1
fi

# Create test directory
mkdir -p "$TEST_DIR"

echo "1. Testing help output..."
$TOOL -h

echo -e "\n2. Testing version info..."
# Note: Version info would be available if we add version flags to main.go

echo -e "\n3. Testing invalid arguments..."
if $TOOL -f nonexistent.bin -o ./; then
    echo "Error: Should have failed with nonexistent file"
    exit 1
else
    echo "✓ Correctly failed with nonexistent file"
fi

echo -e "\n4. Testing login mode (dry run)..."
# This would require actual credentials, so we'll just test the argument parsing
if $TOOL -m login 2>&1 | grep -q "Username.*required"; then
    echo "✓ Correctly requires username for login mode"
else
    echo "Error: Should require username for login mode"
    exit 1
fi

echo -e "\n5. Testing sign mode validation..."
if $TOOL -m sign 2>&1 | grep -q "required"; then
    echo "✓ Correctly validates required arguments for sign mode"
else
    echo "Error: Should validate required arguments"
    exit 1
fi

echo -e "\n6. Creating test binary file..."
TEST_FILE="$TEST_DIR/test-firmware.bin"
dd if=/dev/urandom of="$TEST_FILE" bs=1024 count=100 2>/dev/null
echo "✓ Created test file: $TEST_FILE ($(stat -c%s "$TEST_FILE") bytes)"

echo -e "\n7. Testing file validation..."
if $TOOL -f "$TEST_FILE" -o "$TEST_DIR" 2>&1 | grep -q "Username.*required\|no valid cookie"; then
    echo "✓ Correctly requires authentication"
else
    echo "Warning: Authentication check may not be working as expected"
fi

echo -e "\n8. Testing cross-platform builds..."
if [ -d "./bin" ]; then
    echo "Available builds:"
    find ./bin -name "mtbios-sign-repack*" -exec ls -lh {} \;

    # Test Linux binary if available
    if [ -f "./bin/linux/mtbios-sign-repack" ]; then
        echo "✓ Testing Linux binary..."
        ./bin/linux/mtbios-sign-repack -h > /dev/null
        echo "✓ Linux binary works"
    fi
else
    echo "No cross-platform builds found. Run 'make build-all' to create them."
fi

echo -e "\n9. Performance test..."
echo "Testing startup time..."
time $TOOL -h > /dev/null

echo -e "\n10. Memory usage test..."
if command -v valgrind >/dev/null 2>&1; then
    echo "Running memory check with valgrind..."
    valgrind --tool=memcheck --leak-check=summary $TOOL -h 2>&1 | grep -E "(ERROR SUMMARY|definitely lost)"
else
    echo "Valgrind not available, skipping memory test"
fi

# Cleanup
echo -e "\nCleaning up test files..."
rm -rf "$TEST_DIR"
rm -f "$COOKIE_FILE"

echo -e "\n=== Test Summary ==="
echo "✓ All basic tests passed!"
echo "✓ Tool is ready for use"
echo ""
echo "Next steps:"
echo "1. Test with real credentials: $TOOL -m login -u <EMAIL> -d department"
echo "2. Test signing: $TOOL -f your-firmware.bin -o ./"
echo "3. Deploy to Jenkins: make build-jenkins"
echo ""
echo "Binary sizes:"
if [ -d "./bin" ]; then
    find ./bin -name "mtbios-sign-repack*" -exec ls -lh {} \; | awk '{print "  " $9 ": " $5}'
else
    ls -lh mtbios-sign-repack 2>/dev/null | awk '{print "  " $9 ": " $5}' || echo "  No binaries found"
fi
