# MTBios Sign and Repack Tool

A Go-based tool for signing binary files through MTBios signing server and repacking them into the final binary format.

## Features

- **Single Binary**: No dependencies, just one executable file
- **Cross-Platform**: Supports Linux, Windows, and macOS
- **Fast**: Quick startup and execution
- **Secure**: Credential management with cookie storage
- **Simple**: Easy command-line interface

## Installation

### Build from Source

```bash
# Clone and build
git clone <repository>
cd mtbios-sign-repack

# Build for current platform
make build

# Build for all platforms
make build-all

# Build for Jenkins shared library
make build-jenkins
```

### Download Binary

Download the pre-built binary for your platform from the releases page.

## Usage

### Login Mode (First Time)

Save your credentials for future use:

```bash
./mtbios-sign-repack -m login -u <EMAIL> -d sw
```

### Sign Mode

Sign and repack a binary file:

```bash
# Using saved credentials
./mtbios-sign-repack -f firmware.bin -o ./

# With explicit credentials
./mtbios-sign-repack -u <EMAIL> -d sw -f firmware.bin -o ./
```

## Command Line Options

```
-u  User email address [required for login mode]
-p  Email login password [optional, will prompt if not provided]
-d  Department name, default is SDM [optional]
-f  Signature file [required for sign mode]
-o  Output directory [required for sign mode]
-c  Cookie file path, default is ./login.cookie [optional]
-m  Mode: 'login' or 'sign' (default) [optional]
```

## Examples

```bash
# First time setup
./mtbios-sign-repack -m login -u <EMAIL> -d engineering

# Sign a firmware file
./mtbios-sign-repack -f mtfw-gen6rv.bin -o ./output/

# Sign with custom cookie file
./mtbios-sign-repack -c ./my-cookies.json -f firmware.bin -o ./
```

## Jenkins Integration

### Using in Jenkins Pipeline

```groovy
// In your Jenkinsfile
pipeline {
    agent any
    stages {
        stage('Sign Firmware') {
            steps {
                script {
                    // Use the shared library function
                    mtbiosSign([
                        mode: 'login',
                        username: '<EMAIL>',
                        department: 'sw'
                    ])

                    mtbiosSign([
                        file: 'firmware.bin',
                        output: './'
                    ])
                }
            }
        }
    }
}
```

### Shared Library Setup

1. Build binaries for Jenkins: `make build-jenkins`
2. The binaries will be placed in the shared library resources directory
3. Use the provided Groovy functions in your pipelines

## Development

### Prerequisites

- Go 1.21 or later
- Make (optional, for using Makefile)

### Building

```bash
# Download dependencies
go mod download

# Build
go build -o mtbios-sign-repack .

# Or use Makefile
make build
```

### Testing

```bash
make test
```

## File Structure

```
mtbios-sign-repack/
├── main.go           # Main application code
├── go.mod           # Go module definition
├── Makefile         # Build automation
├── README.md        # This file
└── bin/             # Built binaries (created by make)
    ├── linux/
    ├── windows/
    └── darwin/
```

## Comparison with Python Version

| Feature | Go Version | Python Version |
|---------|------------|----------------|
| Dependencies | None | requests-html, dateutil, lxml |
| Startup Time | ~1ms | ~200ms |
| Memory Usage | ~10MB | ~50MB |
| Deployment | Single file | Python + packages |
| Cross-compile | Yes | No |
| Performance | Fast | Moderate |

## License

[Your License Here]
