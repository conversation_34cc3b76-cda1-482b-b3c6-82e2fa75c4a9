import groovy.transform.Field
import groovy.json.JsonSlurper

import org.swqa.tools.git
import org.swqa.tools.common

@Field def gitLib = new git()
@Field def commonLib = new common()

// 收集test job数据, 跑什么测试, 哪个node, 参数, 结果, exception

def prePipeline(Map workflow) {
    stage('pre') {
        if (env.JOB_NAME.contains('test.')) {
            sh 'lspci | grep 1ed5 ||:'
        }
        sh 'printenv'
    }
}

def postPipeline(Closure post=null) {
    stage('post') {
        // update gitlab status to finished ?
        // data collection
        // add log path in description if env.logAddress
        post.call()
    }
}

def genTaskStatusName(String name) {
    return "${env.commitId}-${env.branch}-${env.JOB_NAME}-${env.BUILD_ID}-${name.replaceAll(' ', '+')}"
}

def setTaskStatus(String name, String status) {
    String statusName = genTaskStatusName(name)
    utils.curl("http://swqa.mthreads.com:6379/SET/${statusName}/${status}")
}

def getTaskStatus(String name) {
    String statusName = genTaskStatusName(name)
    String jsonStr = utils.curl("http://swqa.mthreads.com:6379/GET/${statusName}")
    def status = new JsonSlurper().parseText(jsonStr)
    return status.GET
}

def waitStatus(String name) {
    while ('done-pass' != getTaskStatus(name)) {
        if ( getTaskStatus(name) == 'done-fail') { throw new GroovyRuntimeException("Dependency - ${name} failed!") }
        println("waiting for status ${name} to be done...")
        sleep(10)
    }
    println("status ${name} is done-pass!")
}

@NonCPS
def waitDepends(depends) {
    depends.each { depend -> waitStatus(depend) }
}

def runJob(Map task) {
    log.info("running sub job ${task.job} now...")
    log.info("parameters: ${task.parameters}")
    def subJob = build job: task.job, parameters: task.parameters.collect { string(name: it.key, value: it.value) }, wait: task.getOrDefault('wait', true)
    if (subJob) {
        try {
            log.info("click here to check job ${subJob?.getFullDisplayName()}: ${subJob?.getAbsoluteUrl()}")
            env.subJobs = env.subJobs ? "${env.subJobs},${task.job}#${subJob.id}" : "${task.job}#${subJob.id}"
        } catch (e) {
            log.error(e.message)
        }
    }
    return subJob
// TODO: add description from subjob???
}

def runClosure(Closure closure) {
    println('running closure now...')
    closure()
}

def runTask(String name, Map task) {
    stage(name) {
        // TODO: check commit status on gitlab, maybe we can skip running this
        println("run stage ${name} with config ${task}")
        name = task.statusName ?: name
        waitTime = task.maxWaitTime?.time ?: 60 * 24 * 2
        timeUnit = task.maxWaitTime?.unit ?: 'MINUTES'
        try {
            // setTaskStatus(name, 'start')
            // this "if" statement is a little redundant
            if (task.setGitlabStatus) {
                // set status to canceled because can not set status from running to running
                gitLib.setGitlabStatus(name, 'canceled')
                gitLib.setGitlabStatus(name, 'running')
            }
            timeout(time: waitTime, unit: timeUnit) {
                if (task.closure) {
                    runClosure(task.closure)
                }
                if (task.job) {
                    runJob(task)
                }
            }
            // setTaskStatus(name, 'done-pass')
            if (task.setGitlabStatus) { gitLib.setGitlabStatus(name, 'success') }
        } catch (exc) {
            // setTaskStatus(name, 'done-fail')
            if (task.setGitlabStatus) { gitLib.setGitlabStatus(name, 'failed') }
            if (isUnix() && env.JOB_NAME.startsWith('test.')) { sh 'dmesg -T || :' }
            String errorMessage = exc.message
            String jobDescription = jenkins.model.Jenkins.instance.getItem(env.JOB_NAME).description
            String rebuildMessage = "Pipeline Rerun: ${env.BUILD_URL}/rebuild"
            if (jobDescription) {
                jobDescription = jobDescription.replaceAll('<BR>', '\n').replaceAll('<br>', '\n')
                errorMessage = "${errorMessage}\n${jobDescription}\n${rebuildMessage}"
            }
            error(errorMessage)
        } finally {
            // env.taskStatus[name] = "done-${result}"
            println("stage ${name} finished")
        }
    }
}

def runWorkflow(Map workflow) {
    def asyncTasks = [:]
    workflow.each { name, task ->
        if (task.getOrDefault('async', false)) {
            asyncTasks[name] = {
                // waitDepends(task.getOrDefault('depends', []))
                runTask(name, task)
            }
        } else {
            if (asyncTasks) {
                parallel(asyncTasks)
                asyncTasks = [:]
            }
            runTask(name, task)
        }
    }
    parallel(asyncTasks)
}

boolean shouldTriggerBuild(item) {
    def isMR = gitLib.triggeredByMR()
    def buildOnlyMerged = item.buildOnlyMerged?.toLowerCase() == 'true'
    def buildAfterMerged = item.buildAfterMerged?.toLowerCase() == 'true'
    return (isMR && !buildOnlyMerged) || (!isMR && (buildOnlyMerged || buildAfterMerged))
}

def groupJobsByDependencyLevel(List jobList) {
    // Automatically generate a unique name for jobs missing the 'name' field
    jobList = jobList.findAll { item -> shouldTriggerBuild(item) }
    jobList.eachWithIndex { item, idx ->
        item.name = item.name ?: "${item.job}_${idx}"
    }
    def nameToJob = jobList.collectEntries { [(it.name): it] }
    def visited = [:]

    Closure<Integer> visit
    visit = { job ->
        if (visited.containsKey(job.name)) {
            return visited[job.name]
        }
        def level = 0
        def deps = job.get('depends', [])
        deps.each { dep ->
            if (nameToJob.containsKey(dep)) {
                level = Math.max(level, visit.call(nameToJob[dep]) + 1)
            } else {
                // ignore if dep doesn't exsit, return level 0
                level = Math.max(level, 0)
            }
        }
        visited[job.name] = level
        return level
    }

    jobList.each { visit.call(it) }

    def grouped = new TreeMap(jobList.groupBy { visited[it.name] })
    return grouped.values().toList()
}

def genJobParameters(jobCandidate, Map parameters=[:]) {
    parameters = [
        repo: env.gitlabSourceRepoName,
        branch: env.gitlabSourceBranch,
        commitId: env.gitlabMergeRequestLastCommit,
        triggerInfo: env.triggerInfo
    ] + parameters
    def name = jobCandidate.name ?: jobCandidate.job
    def _parameters = [testLabel: name] + (jobCandidate.parameters ?: [:])
    def packageName = _parameters.packageName ?: parameters.packageName
    if (packageName && !_parameters.containsKey('linuxDdkPackageUrl') && jobCandidate.job =~ 'test.') {
        // Only effective in test jobs
        _parameters.linuxDdkPackageUrl = constants.genPackageUrl(parameters.repo, parameters.branch, parameters.commitId, packageName)
    }
    if (_parameters.umdPackages && _parameters.umdBranch) {
        _parameters.umdPackageUrls = _parameters.umdPackages.collect { umdPackage ->
            constants.genLatestPackageUrl('gr-umd', _parameters.umdBranch, umdPackage)
        }.join(',')
    }
    if (jobCandidate.job ==~ /(CI_.*|DDK_.*)_test_gitlab/) {
        _parameters.ghprbActualCommit = env.gitlabMergeRequestLastCommit
    }
    /*
        Generally used for multiple package variables
        Replace parameters with map variables, for example:
        muBLASPackageUrl: [
            'packageLabel': 'latest', [build;latest]
            'packageRepo': 'muBLAS'
            'branch': 'master',
            'packageName': 'muBLAS.tar.gz'
        ]
        build :TODO 用变量拼接即可 无需增加测试
        latest : constants.genLatestPackageUrl(packageRepo, branch, packageName)
        TODO 增加其他逻辑
    */
    _parameters.each { key, value ->
        if (value instanceof Map && value.containsKey('packageLabel')) {
            if (value.packageLabel == 'latest') {
                _parameters[key] = constants.genLatestPackageUrl(value.packageRepo, value.branch, value.packageName)
            }
        }
    }
    def jobParameters = utils.toStringParams(parameters + _parameters)
    return jobParameters
}

def runStages(stages, parameters = [:]) {
    println "\n🖨️ Stages Raw Structure:\n${groovy.json.JsonOutput.prettyPrint(groovy.json.JsonOutput.toJson(stages))}"
    stages.eachWithIndex { stageJobs, index ->
        echo "\n🧱 === Running Stage ${index} ==="
        echo 'Jobs in this stage:'

        def parallelJobs = [:]
        stageJobs.eachWithIndex { jobCandidate, idx ->
            def name = jobCandidate.name ?: "${jobCandidate.job}_${idx}"
            def depends = jobCandidate.get('depends', [])
            echo " - ${name} (depends on: ${depends.join(', ')})"
            echo "🚀 Starting job: ${name}"
            def jobParameters = genJobParameters(jobCandidate, parameters)
            parallelJobs[name] = {
                runTask(name, [
                    job: jobCandidate.job,
                    parameters: jobParameters,
                    setGitlabStatus: true
                ])
            }
        }
        if (parallelJobs) {
            echo "🧩 Running parallel jobs in Stage ${index}: ${parallelJobs.keySet().join(', ')}"
            parallel parallelJobs
        } else {
            echo "⚠️  No jobs to run in Stage ${index}"
        }
    }
}

def runYamlWorkflow(config, buildParameters = [:], testParameters = [:] ) {
    def buildStages = groupJobsByDependencyLevel(config.builds ?: [])
    def testStages = groupJobsByDependencyLevel(config.tests ?: [])
    def allStages = buildStages + testStages
    if (env.JOB_NAME.startsWith('mr.')) {
        def tasks = [:]
        allStages.flatten().each { jobCandidate ->
            // set status to canceled because can not set status from running to pending
            tasks[jobCandidate.name] = {
                gitLib.setGitlabStatus(jobCandidate.name, 'canceled')
                gitLib.setGitlabStatus(jobCandidate.name, 'pending')
            }
        }
        stage('init gitlab status') {
            parallel(tasks)
        }
        runWorkflow(['Check Jira ID': [ closure: { utils.checkJiraId() }, setGitlabStatus: true, statusName: 'check_jira_id' ]])
    }
    // run all builds firstly，then run all tests
    try {
        if (buildStages.size() > 0) { runStages(buildStages, buildParameters) }
        if (buildStages.size() > 0 && env.gitlabActionType == 'PUSH') {
            runWorkflow(['Update Latest Txt': [ closure: { constants.updateLatestTxt(env.gitlabSourceRepoName, env.gitlabTargetBranch, env.gitlabMergeRequestLastCommit) }, setGitlabStatus: false ]])
        }
        if (testStages.size() > 0) { runStages(testStages, buildParameters) }
    } catch (exc) {
        error(exc.message)
    } finally {
        if (env.JOB_NAME.startsWith('mr.')) {
            // Introduced the setGitlabStatus property to enable GitLab status updates when using cancelPendingAndRunning
            def workflow = allStages.flatten().collectEntries {
                it['setGitlabStatus'] = true
                [(it.name): it]
            }.findAll { k, v -> k }
            cancelPendingAndRunning(workflow)
            }
        }
    }

def runSyncThenAsyncTasks(Map workflow) {
    def (syncTasks, asyncTasks) = workflow.groupBy {
        it.value.getOrDefault('async', false)
    }.with {
        [it.get(false, [:]), it.get(true, [:])]
    }

    if (syncTasks) { parallel(syncTasks.collectEntries { name, task -> [name, { runTask(name, task) }] }) }
    if (asyncTasks) { parallel(asyncTasks.collectEntries { name, task -> [name, { runTask(name, task) }] }) }
}

def enteringDebug() {
    catchError(buildResult: null, stageResult: null) {
        if (env.debugTime && env.debugTime.toInteger() > 0) {
            stage('debug') {
                timeout(env.debugTime.toInteger()) {
                    input 'debug mode, exit?'
                }
            }
        }
    }
}

def cancelPendingAndRunning(Map workflow, Map options=null) {
    if (env.JOB_NAME.startsWith('mr.') && !(options?.disableCancelGitlabStatus)) {
        try {
            def jobStatuses = []
            workflow.each { name, task ->
                if (task.getOrDefault('setGitlabStatus', false) == true) {
                    jobStatuses.add(task.statusName ?: name)
                }
            }
            def mrStatuses = gitLib.getGitlabCommitAllStatuses(env.gitlabSourceRepoName, env.gitlabMergeRequestLastCommit, env.gitlabSourceBranch)
            def isFail = mrStatuses.any {
                it.status == 'failed'
            }
            if (isFail) {
                List needCancelStatuses = []
                List originalLinks = []
                mrStatuses.each {
                    if ((it.status == 'pending' || it.status == 'running') && jobStatuses.contains(it.name)) {
                        needCancelStatuses.add(it.name)
                        originalLinks.add(it.target_url)
                    }
                }
                gitLib.setGitlabStatus(needCancelStatuses, 'canceled', originalLinks)
            }
        } catch (exc) {
            println exc.message
        }
    }
}

def genWorkflow(workflow, items, parameters = [:]) {
    parameters = [
        repo: env.gitlabSourceRepoName,
        branch: env.gitlabSourceBranch,
        commitId: env.gitlabMergeRequestLastCommit,
        triggerInfo: env.triggerInfo
    ] + parameters
    items = items ?: []
    for (item in items) {
        if (item.job?.endsWith('.win')) { continue }
        def _item = item
        if (shouldTriggerBuild(_item)) {
            def defaultParameters = _item.parameters ?: [:]
            def name = _item.name ?: _item.job
            def _parameters = [
                testLabel: name
            ]
            defaultParameters.each { key, value ->
                _parameters[key] = value
            }

            def packageName = _parameters.packageName ?: parameters.packageName
            if (packageName) {
                // Only effective in test jobs
                _parameters.linuxDdkPackageUrl = constants.genPackageUrl(parameters.repo, parameters.branch, parameters.commitId, packageName)
            }
            if (_parameters.umdPackages && _parameters.umdBranch) {
                _parameters.umdPackageUrls = _parameters.umdPackages.collect { umdPackage ->
                    constants.genLatestPackageUrl('gr-umd', _parameters.umdBranch, umdPackage)
                }.join(',')
            }
            if (_item.job ==~ /(CI_.*|DDK_.*)_test_gitlab/) {
                _parameters.ghprbActualCommit = env.gitlabMergeRequestLastCommit
            }
            def itemParameters = utils.toStringParams(parameters + _parameters)
            workflow[name] = [
                job: _item.job,
                parameters: itemParameters,
                setGitlabStatus: true,
                async: true
            ]
        }
    }
}

def genWorkflowByConfig(config, buildParameters = [:], testParameters = [:]) {
    def workflow = [:]

    genWorkflow(workflow, config.builds, buildParameters)
    // this workflow is for runworkflow() to parallel all builds before tests
    if (gitLib.triggeredByMR() && config.builds.size() > 0 && config.tests.size() > 0) {
        workflow['build complete'] = [
            closure: { println 'build complete' },
            setGitLabStatus: false,
            async: false
        ]
    } else if (!gitLib.triggeredByMR()) {
        workflow['update latest txt'] = [
            closure: { constants.updateLatestTxt(env.gitlabSourceRepoName, env.gitlabTargetBranch, env.gitlabMergeRequestLastCommit) },
            setGitLabStatus: false,
            async: false
        ]
    }
    genWorkflow(workflow, config.tests, testParameters)

    return workflow
}

def call(Map workflow, Map options=null) {
    env.subJobs = ''
    if (env.JOB_NAME.startsWith('mr.')) {
        workflow = [
            'Check Jira ID': [ closure: { utils.checkJiraId() }, setGitlabStatus: true, statusName: 'check_jira_id' ],
            *: workflow
        ]
    }
    // set all status to pending if needed, can not be disabled
    def tasks = [:]
    workflow.each { name, task ->
        if (task.getOrDefault('setGitlabStatus', false) == true) {
            // set status to canceled because can not set status from running to pending
            tasks[task.statusName ?: name] = {
                gitLib.setGitlabStatus(task.statusName ?: name, 'canceled')
                gitLib.setGitlabStatus(task.statusName ?: name, 'pending')
            }
        }
    }
    stage('init gitlab status') {
        parallel(tasks)
    }
    // set gitlab status if need, for this job, and set pass/failure in the end
    // may need to add try/catch below
    if (!options?.disablePre) {
        prePipeline(workflow)
    }
    try {
        runWorkflow(workflow)
    } catch (exc) {
        error(exc.message)
    } finally {
        enteringDebug()
        // if (env.JOB_NAME.contains('test.') && env.runChoice == 'node' && env.disableTearDown != 'true') {
        //     stage('test tear down') { commonLib.tearDown() }
        // }
        // if (!env.JOB_NAME.contains('vps')) {
        //     commonLib.reboot(env.NODE_NAME)
        // }
        if (!(options?.disablePost) && options?.post) {
            catchError(buildResult: null, stageResult: null) {
                postPipeline(options?.post)
            }
        }
        cancelPendingAndRunning(workflow, options)
    }
}
