def upload(String packageName, String packagePath, Boolean recursive=true) {
    def formatPackagePath = packagePath.replaceAll('//+', '/').replaceAll('^oss/|^swci-oss/|/$', '')
    def formatPackageName = packageName.split('/')[-1]
    // if mr: replace oss path, and ossHost
    // next: modify mr.pipeline
    def ossPrefix = constants.genOssPrefix(formatPackagePath)
    def ossPackageUrl = "${ossPrefix}/${formatPackagePath}"
    oss.install()
    if (formatPackagePath.contains('product-release/')) {
        oss.setUp('product-release', 'product-release123')
    }
    def ossAlias = constants.genOssAlias(formatPackagePath)
    oss.cp(packageName, "${ossAlias}/${formatPackagePath}", recursive)
    // When uploading recursively, the complete URL needs to include the file name
    def fileUrl = recursive ? "${ossPackageUrl}/${formatPackageName}" : ossPackageUrl
    def description = currentBuild.description
    def target = '======== ARTIFACTS ========<br>'
    if (description.matches(".*${target}\$")) {
        currentBuild.description = description.replaceFirst("${target}\$", '')
    } else {
        currentBuild.description += target
    }
    currentBuild.description += "${formatPackageName}: <a href='${fileUrl}' target='_blank'>${fileUrl}</a><br>"
    currentBuild.description += target
}

def upload(String repo, String branch, String commitId, String packageName, Boolean recursive=true, String packageReName=null) {
    // def ossBranchPath = constants.genOssPath(repo, branch)
    def formatPackageName = packageReName ? packageReName.split('/')[-1] : packageName.split('/')[-1]
    def ossPackagePath = constants.genOssPath(repo, branch, commitId)
    def ossPrefix = constants.genOssPrefix(ossPackagePath)
    def ossPackageUrl = "${ossPrefix}/${ossPackagePath}"
    isUnix() ? oss.install() : oss.setUp()
    if (ossPackagePath.contains('product-release/')) {
        oss.setUp('product-release', 'product-release123')
    }
    def ossAlias = constants.genOssAlias(ossPackagePath)
    if (packageReName) {
        oss.cp(packageName, "${ossAlias}/${ossPackagePath}/${packageReName}", recursive)
    } else {
        oss.cp(packageName, "${ossAlias}/${ossPackagePath}/", recursive)
    }

    def description = currentBuild.description
    def target = '======== ARTIFACTS ========<br>'
    if (description.matches(".*${target}\$")) {
        currentBuild.description = description.replaceFirst("${target}\$", '')
    } else {
        currentBuild.description += target
    }
    if (formatPackageName == '*') {
        def files = sh(script: "ls -1 ${packageName.replace('*', '')}", returnStdout: true).trim().split('\n')
        files.each { file ->
            def fileUrl = "${ossPackageUrl}/${file}"
            currentBuild.description += "${file}: <a href='${fileUrl}' target='_blank'>${fileUrl}</a><br>"
        }
    } else {
        def fileUrl = "${ossPackageUrl}/${formatPackageName}"
        currentBuild.description += "${formatPackageName}: <a href='${fileUrl}' target='_blank'>${fileUrl}</a><br>"
    }
    currentBuild.description += target
}

def upload(List<Map<String, String>> artifacts) {
    artifacts.each { artifact ->
        dir(artifact.path) {
            if (artifact.ossPath) {
                if (artifact.ossName) {
                    upload(artifact.name, "${artifact.ossPath}/${artifact.ossName}", false)
                } else {
                    upload(artifact.name, artifact.ossPath, true)
                }
            } else {
                if (artifact.ossName) {
                    upload(env.gitlabSourceRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, artifact.name, false, artifact.ossName)
                } else {
                    upload(env.gitlabSourceRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, artifact.name, true)
                }
            }
        }
    }
}

def uploadToUrl(String packageUrl, String packagePath) {
    def ossPath = constants.urlToOSSPath(packageUrl)
    def packageName = packageUrl.split('/')[-1]
    oss.install()
    oss.cp(packagePath, ossPath)
    currentBuild.description += "package - ${packageName}: ${packageUrl}<br>"
}

def download(String repo, String branch, String commitId, String packageName) {
    def ossPackagePath = constants.genOssPath(repo, branch, commitId)
    // def ossPackagePath = "${ossBranchPath}/${commitId}"
    def ossAlias = constants.genOssAlias(ossPackagePath)
    def fullPackagePath = "${ossAlias}/${ossPackagePath}/${packageName}"
    oss.install()
    oss.cp(fullPackagePath, './')
    return fullPackagePath
}

def packageExistsOnOss(String repo, String branch, String commitId, String packageName) {
    String packageOssPath = constants.genPackagePath(repo, branch, commitId, packageName)
    oss.install()
    output = oss.ls(packageOssPath)
    log.debug("output: ${output}")
    return output.contains(packageName)
}

def packageExistsOnOss(String packageOssPath) {
    oss.install()
    output = oss.ls(packageOssPath)
    log.debug("output: ${output}")
    return output.length() > 0
}

def uploadTestReport(String reportPackagePath, String reportOssPath, String fileName = 'TestReport') {
    oss.install()
    oss.cp(reportPackagePath, reportOssPath)
    reportOssPath = reportOssPath.replaceAll(/\/+$/, '')
    ossUrl = constants.ossPathToUrl("${reportOssPath}/${reportPackagePath.split('/')[-1]}")
    currentBuild.description += "${fileName} ossUrl: <a href='${ossUrl}' target='_blank'>${ossUrl}</a> <br>"
}

def uploadLog(String logPath, String ossPath = '') {
    if (fileExists(logPath)) {
        println "upload ${logPath}"
        ossPath = ossPath ?: "swci-oss/sw-pr/${env.gitlabSourceRepoName}/${env.gitlabMergeRequestIid}/${env.JOB_NAME}_${env.build_ID}/log"
        def logName = logPath.split('/')[-1]
        def ossUrl = constants.ossPathToUrl("${ossPath}/${logName}")
        oss.install()
        oss.cp(logPath, "${ossPath}/", false)
        currentBuild.description += "${logPath} ossUrl: <a href='${ossUrl}' target='_blank'>${ossUrl}</a> <br>"
    }
}
