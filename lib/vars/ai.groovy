import groovy.transform.Field
import org.swqa.tools.git

@Field def gitLib = new git()

def codeReview() {
    sh '''
        apt update
        apt install jq -y
        jq --version
        apt install python3-pip -y
        pip3 install requests
    '''

    def changedFiles = sh(script: "git diff origin/${env.gitlabTargetBranch} --name-only", returnStdout: true).trim()
    def changedFilesList = changedFiles.split('\n').findAll { it != '' }

    if (changedFilesList && changedFilesList.size() > 0) {
        def outputFilesList = []
        changedFilesList.each { file ->
            def outputFile = file.replaceAll('/', '_')
            sh """
                git diff -U0 origin/${env.gitlabTargetBranch}  -- "${file}" | grep -E '^\\+|^-|^@@' > "${outputFile}"
                echo ${outputFile} >> outputFiles.txt
            """
            outputFilesList.add(outputFile)
        }

        def python_script = libraryResource('scripts/ai_review.py')
        writeFile(file: 'ai_review.py', text: python_script)
        sh(script: 'python3 ai_review.py outputFiles.txt')

        def urlMap = [:]
        outputFilesList.each {
            if (fileExists("ai_review_${it}.txt")) {
                artifact.upload(env.gitlabSourceRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, "ai_review_${it}.txt")
                urlMap[it.replaceAll('_', '/')] = constants.genPackageUrl(env.gitlabSourceRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, "ai_review_${it}.txt", false, true)
            }
        }

        def note = 'Below is the AI reivew result:<br /><br />'
        urlMap.each { k, v ->
            note = note + "${k}: <a href=\'${v}\' target=\'_blank\'>${v}</a><br /><br />"
        }
        def name = 'jenkins/ai_analysis'
        def encodeParams = URLEncoder.encode("state=success&repo=${env.gitlabSourceRepoName}&commitId=${env.gitlabMergeRequestLastCommit}&name=${name}", 'UTF-8')
        def approveUrl = "http://swqa.mthreads.com/notification/gitlab_state?${encodeParams}"
        note = note + "Click <a href=\'${approveUrl}\' target=\'_blank\'>here</a> to approve the AI review result."

        gitLib.addCommentForMR(note)
    }
}
