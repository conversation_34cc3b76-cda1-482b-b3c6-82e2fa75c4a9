import groovy.transform.Field
import org.apache.commons.lang3.StringEscapeUtils

@Field DB_HOST = '**************'
@Field DB_PORT = '3306'
@Field DB_CREDENTIAL = 'DB-SWQA-CI'

def execute_mysql_command(String host, String port, String credentialId, String db, String command) {
    credentials.runWithCredential(credentialId) {
        def commandPrefix = "mysql -h ${host} -P ${port} -u ${USERNAME} -p\"${PASSWORD}\" -D ${db}"
        def stdout = sh(returnStdout: true, script: "${commandPrefix} -e \"${command}\"")
        log.info(stdout)
        return stdout
    }
}

def escapeSingleQuote(String value) {
    return value.replace("'", "''")
}

def insertData(String dbname, String tablename, Map row) {
    def columns = "${row.keySet().join(', ')}"
    def values = row.values().collect { value -> value == null ? 'NULL' : "'${value}'" }.join(', ')
    def sql = "INSERT INTO ${tablename} (${columns}) VALUES (${values})"
    log.debug(StringEscapeUtils.escapeJava(sql))
    execute_mysql_command(DB_HOST, DB_PORT, DB_CREDENTIAL, dbname, sql)
}

def insertData(String dbname, String tablename, List rows) {
    if (rows.size() == 0) { return }
    def columns = "${rows[0].keySet().join(', ')}"
    def values = rows.collect { row ->
        def _values = row.values().collect { value -> value == null ? 'NULL' : "'${value}'" }.join(', ')
        return "(${_values})"
    }.join(', ')
    def sql = "INSERT INTO ${tablename} (${columns}) VALUES ${values}"
    execute_mysql_command(DB_HOST, DB_PORT, DB_CREDENTIAL, dbname, sql)
}

def selectData(String dbname, String tablename, Map selectwhere, String selectCol = null) {
    //selectwhere eq. ["key":['=','value']]
    def _sql = []
    selectwhere.each { key, value ->
        _sql.add("${key}${value[0]}\'${value[1]}\'")
    }
    def col = selectCol ?: '*'
    def sql = selectwhere.size() == 0 ? "SELECT ${col} from ${tablename}" : "SELECT ${col} from ${tablename} WHERE ${_sql.join(' AND ')}"
    return execute_mysql_command(DB_HOST, DB_PORT, DB_CREDENTIAL, dbname, sql)
}

def updateData(String dbname, String tablename, Map cols, Map selectinfo) {
    //cols eq. ["key":"value"]
    //selectinfo eq. ["key":['=','value']]
    if (cols.size() == 0 || selectinfo.size() == 0) { return }
    def _select_sql = []
    def _update_sql = []
    cols.each { key, value ->
        _update_sql.add("${key}=\'${value}\'")
    }
    selectinfo.each { key, value ->
        _select_sql.add("${key}${value[0]}\'${value[1]}\'")
    }
    def sql = "UPDATE ${tablename} SET ${_update_sql.join(', ')} WHERE ${_select_sql.join(' AND ')}"
    execute_mysql_command(DB_HOST, DB_PORT, DB_CREDENTIAL, dbname, sql)
}
