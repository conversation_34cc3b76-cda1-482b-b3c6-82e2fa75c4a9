import groovy.transform.Field
import org.swqa.tools.common
import org.swqa.tools.git

@Field triggerInfoDelimiter = '==='
@Field mrDependencyDelimiter = '==>'

@Field signtoolSha1 = '9bbe3f5a5987db1a7c6324be90f386824de59526'
@Field signtoolSha1ChengDu = 'a8aa89bc2ae864f722b1f7f3bbabee8129d3d6c3'
@Field dashboardApiUrl = 'http://swqa.mthreads.com/api/v1'

@Field def commonLib = new common()
@Field def gitLib = new git()

@Field List musaChangeBranches = ['master']

@Field List swciOssRepos = ['mt-vgpu', 'wddm', 'DirectStream', 'd3dtests']

@Field Map OSS = [
    URL_PREFIX: 'https://oss.mthreads.com',
    CD_URL_PREFIX: 'https://oss.mthreads.com',
    CI_URL_PREFIX: 'https://swci-oss.mthreads.com',
    PR_BUCKET: 'sw-pr',
    BUILD_BUCKET: 'sw-build',
]

@Field Map IMAGE = [
    JNLP: 'sh-harbor.mthreads.com/qa/jenkins-agent:v4',
    VM: 'sh-harbor.mthreads.com/qa/jenkins-agent-vm:latest'
]

@Field Map GITLAB = [
    SW_GROUP: 'sw',
    SH_CODE_URL: '************************',
]

@Field Map mcUrl = [
    'aarch64': 'https://swci-oss.mthreads.com/dependency/mc/linux-arm64/2022-06-26/mc',
    'arm64': 'https://swci-oss.mthreads.com/dependency/mc/linux-arm64/2022-06-26/mc',
    'x86_64': 'https://swci-oss.mthreads.com/dependency/mc/linux-amd64/2022-06-26/mc'
]

@Field Map defaultBranch = [
    'gr-kmd': 'develop',
    'gr-umd': 'develop',
    'mt-media-driver': 'develop',
    'MUSA-Runtime': 'develop',
    'linux-ddk': 'master',
    'libdrm-mt': 'master'
]

@Field Map mtServiceHosts = [
    'sh-jenkins.mthreads.com': '*************',
    'sh-code.mthreads.com': '*************',
    'oss.mthreads.com': '**************',
    'swci-oss.mthreads.com': '*************',
    'swqa.mthreads.com': '**************',
    'swci-jenkins.mthreads.com': '**********',
    'sh-harbor.mthreads.com': '**************',
    'nexus.shg1.mthreads.com': '**************',
    'swqa-influxdb.mthreads.com': '************',
    'oss-cd.mthreads.com': '*************'
]

@Field Map linuxDdkSubmodulePackageVarName = [
    'test.m3d_cts': 'm3dPackageUrl',
    'test.m3d_musa_cts': 'subModulePackageUrl',
    'test.musa_benchmark': 'subModulePackageUrl',
    'test.ogl_cts': 'oglPackageUrl',
    'test.graphic_cts': 'oglPackageUrl',
    'test.vaapi_fits': 'mediaDriverPackageUrl',
    'test.musa_cts': 'subModulePackageUrl',
    'test.directStreamCodec': 'mediaDriverPackageUrl',
    'test.vps_ph1s': 'linuxDdkPackageUrl',
]

@Field String stableMusaRuntime = 'https://oss.mthreads.com/release-ci/computeQA/cuda_compatible/CI/stable/master/musaRuntime.tar.gz'

def genDefaultMailSubject() {
    return "[Jenkins] ${env.JOB_NAME} - Build # ${env.BUILD_NUMBER} - ${currentBuild.currentResult}"
}

def genDefaultMailContent() {
    return """
<html>
    <head>
    <meta charset="UTF-8">
    <style>
    .type {
        color: blue;
    }
    .pass, .PASS, .null, .SUCCESS, .success {
        color: green;
    }
    .fail, .FAILURE, .FAIL, .failure {
        color: red;
    }
    .unknown {
        color: #CD4F39;
    }
    .untest {
        color: #808080;
    }
</style>
    <title>${env.JOB_NAME}-第${env.BUILD_NUMBER}次构建日志</title>
    </head>

    <body leftmargin="8" marginwidth="0" topmargin="8" marginheight="4"
        offset="0">
        <table width="95%" cellpadding="0" cellspacing="0" style="font-size: 11pt; font-family: Tahoma, Arial, Helvetica, sans-serif">
            <tr>
                <td><h2>
                        <font color="#0000FF">构建结果</font> - <span class="${currentBuild.currentResult}">${currentBuild.currentResult}</span>
                    </h2></td>
            </tr>
            <tr>
                <td><br />
                <b><font color="#0000FF">构建信息</font></b>
                <hr size="2" width="100%" align="center" /></td>
            </tr>
            <tr>
                <td>
                    <ul>
                        <li>项目名称 : ${env.JOB_NAME}</li>
                        <li>构建编号 : 第${env.BUILD_NUMBER}次构建</li>
                        <li>触发原因 : ${currentBuild.getBuildCauses()[0].shortDescription}</li>
                        <li>项目Url : <a href="${env.JOB_URL}">${env.JOB_URL}</a></li>
                        <li>构建Url : <a href="${env.BUILD_URL}">${env.BUILD_URL}</a></li>
                        <li>构建日志 : <a href="${env.BUILD_URL}console">${env.BUILD_URL}console</a></li>
                    </ul>
                </td>
            </tr>
        </table>
    </body>
</html>
"""
}

// package structure:
// repo/branch/latest.txt -> latest commit id
// repo/branch/package.tar.gz -> latest package
// repo/branch/commit/commit_package.tar.gz -> package with commit id, the latest one will also in repo/branch/

def genOssBucket(String repo) {
    return gitLib.triggeredByMR() ? OSS.PR_BUCKET : (repo == 'gr-umd' ? 'release-ci' : OSS.BUILD_BUCKET)
}

def genOssPath(String repo, String branch) {
    def bucket = genOssBucket(repo)
    return "${bucket}/${repo}/${branch}"
}

def genOssPath(String repo, String branch, String commit) {
    def bucket = genOssBucket(repo)
    commit = formatCommitID(commit)
    return repo == 'gr-umd' ? "${bucket}/${repo}/${branch}" : "${bucket}/${repo}/${branch}/${commit}"
}

def genLatestOssPath(String repo, String branch) {
    return repo == 'gr-umd' ? "release-ci/${repo}/${branch}" : "${OSS.BUILD_BUCKET}/${repo}/${branch}"
}

def genLatestOssPath(String repo, String branch, String commit) {
    commit = formatCommitID(commit)
    return repo == 'gr-umd' ? "release-ci/${repo}/${branch}" : "${OSS.BUILD_BUCKET}/${repo}/${branch}/${commit}"
}

def genOssPrefix(String ossPath) {
    if (ossPath.contains('release-ci')) { return OSS.URL_PREFIX }
    return (swciOssRepos.any { ossPath.contains(it) } || ossPath.startsWith('sw-pr/')) ? OSS.CI_URL_PREFIX : OSS.URL_PREFIX
}

def genOssAlias(String ossPath) {
    if (ossPath.contains('release-ci')) { return 'oss' }
    return (swciOssRepos.any { ossPath.contains(it) } || ossPath.startsWith('sw-pr/')) ? 'swci-oss' : 'oss'
}

def genPackageUrl(String repo, String branch, String commit, String pkgName, Boolean isLatest = false, Boolean simpleFormat = false) {
    commit = formatCommitID(commit)
    String ossPath = isLatest ? genLatestOssPath(repo, branch, commit) : genOssPath(repo, branch, commit)
    String ossPrefix = genOssPrefix(ossPath)
    return simpleFormat ? "${ossPrefix}/${ossPath}/${pkgName}" : "${ossPrefix}/${ossPath}/${commit}_${pkgName}"
}

def genPackagePath(String repo, String branch, String commit, String pkgName) {
    commit = formatCommitID(commit)
    String ossPath = genOssPath(repo, branch, commit)
    String ossAlias = genOssAlias(ossPath)
    return "${ossAlias}/${ossPath}/${commit}_${pkgName}"
}

def getLatestPackageCommitId(String repo, String branch=null, Boolean isLatest = false) {
    String ossPath = isLatest ? genLatestOssPath(repo, branch) : genOssPath(repo, branch)
    String content = sh(script: "curl --insecure ${genOssPrefix(ossPath)}/${ossPath}/latest.txt", returnStdout: true).trim()
    if (content =~ 'Error') {
        throw new Exception("No package found for ${repo}/${branch}!")
    }
    // TODO: split again by _, -, +, #
    return content.split('/')[-1]
}

def genLatestPackageUrl(String repo, String branch=null, String pkgName=null) {
    branch = branch ?: defaultBranch[repo]
    String latestCommit = formatCommitID(getLatestPackageCommitId(repo, branch, true))
    return genPackageUrl(repo, branch, latestCommit, pkgName, true)
}

def waitPackageUrl(String packageUrl, int waitTime=60) {
    timeout(time: waitTime, unit: 'MINUTES') {
        oss.install()
        def packagePath = urlToOSSPath(packageUrl)
        def (ossPath, packageName) = [packagePath.substring(0, packagePath.lastIndexOf('/')), packagePath.substring(packagePath.lastIndexOf('/') + 1)]
        while (true) {
            sleep(time: 10, unit: 'SECONDS')
            def findPackage = utils.runCommandWithStdout("mc find ${ossPath} --name ${packageName}")
            if (findPackage) {
                print(findPackage)
                def findPackageName = findPackage.split('/').last().trim()
                def newPackageUrl = packageUrl.replace(packageName, findPackageName)
                return newPackageUrl
            }
        }
    }
}

def genLatestMusaPackageUrl(String branch='develop', String pkgName='dkms+glvnd-pc_amd64.deb') {
    sh '''
        if [ -f "latest.txt" ]; then
            rm latest.txt
        fi
        wget https://oss.mthreads.com/product-release/develop/latest_pc.txt
    '''
    def dateTag = sh(script:'cat latest_pc.txt', returnStdout: true).trim()

    return "https://oss.mthreads.com/product-release/${branch}/${dateTag}+${pkgName}"
}

def downloadPackage(String packageUrl) {
    commonLib.retryByRandomTime({
        sh "wget -q --no-check-certificate ${packageUrl}"
    })
    return packageUrl.split('/')[-1]
}

def downloadLatestPackage(String repo, String branch=null, String pkgName=null) {
    downloadPackage(genLatestPackageUrl(repo, branch, pkgName))
}

def genDateTag(tag_date) {
    return commonLib.validateDateTag(tag_date) ? tag_date : new Date().format('yyyyMMdd')
}

def formatCommitID(String commitId) {
    return (commitId && commitId.length() >= 9) ? commitId.substring(0, 9) : commitId
}

def downloadAndUnzipPackage(String packageUrl, String targetPath=null) {
    def packageName = packageUrl.split('/')[-1].split(';')[0]
    commonLib.retryByRandomTime({
        sh "wget -q --no-check-certificate ${packageUrl} -O ${packageName}"
    })
    def unzipCmd = packageName.endsWith('.gz') ? 'tar -xzf' : 'tar -xf'
    def tarPathCmd = targetPath ? "-C ${targetPath}" : ''
    if (targetPath) { sh "mkdir -p ${targetPath}" }
    def pkgFolder = utils.listFirstFolderInTarFile(packageName)
    sh "${unzipCmd} ${packageName} ${tarPathCmd}"
    return pkgFolder
}

def updateLatestTxt(repo, branch, commitId) {
    // historic reason, too many existed packages name use prefix - commitId[0..8]
    sh "rm -rf ${repo}"
    gitLib.fetchCode(repo, branch, null, [preBuildMerge: false, disableSubmodules: true])
    commitId = formatCommitID(commitId)
    def shouldUpdateLatestTxt = false
    try {
        def latestCommitId = getLatestPackageCommitId(repo, branch)
        dir(repo) {
            shouldUpdateLatestTxt = gitLib.isNewer(commitId, latestCommitId)
        }
    } catch (ex) {
        shouldUpdateLatestTxt = true
    }
    if (shouldUpdateLatestTxt) {
        def ossBranchPath = genLatestOssPath(repo, branch)
        def ossPrefix = genOssPrefix(ossBranchPath)
        def ossAlias = genOssAlias(ossBranchPath)
        def ossPackageUrl = "${ossPrefix}/${ossBranchPath}/${commitId}"
        def specialRepo = [
            'gr-umd': ossPackageUrl,
            'linux-ddk': commitId,
        ]
        content = specialRepo[repo] ?: "${ossPackageUrl}/${commitId}"
        oss.install()
        sh """
            echo ${content} > latest.txt
            mc cp latest.txt ${ossAlias}/${ossBranchPath}/
        """
        try {
            sh """
                mc find ${ossAlias}/${ossBranchPath}/history.txt
                mc cp ${ossAlias}/${ossBranchPath}/history.txt history.txt
                sed -i "1i ${commitId} `date '+%Y-%m-%d %H:%M:%S'`" history.txt
            """
        } catch (e) {
            sh """
                echo "${commitId} `date '+%Y-%m-%d %H:%M:%S'`" > history.txt
            """
        } finally {
            sh "mc cp history.txt ${ossAlias}/${ossBranchPath}/"
        }
    }
}

def ossPathToUrl(String ossPath) {
    return ossPath.replace('swci-oss/', "${OSS.CI_URL_PREFIX}/").replace('oss-cd/', "${OSS.URL_PREFIX}/").replace('oss/', "${OSS.URL_PREFIX}/")
}

def urlToOSSPath(String url) {
    return url.replace('https://oss.mthreads.com', 'oss').replace('https://oss-cd.mthreads.com', 'oss').replace('https://swci-oss.mthreads.com', 'swci-oss')
}

def addMtServiceHosts() {
    mtServiceHosts.each { host, ip -> sh "echo ${ip} ${host} >> /etc/hosts" }
}

def getCommitIdfromciConfig(config, branchName='ddkBranch') {
    Map commitIdMap = [:]
    for (buildConfig in config.builds) {
        def _buildConfig = buildConfig
        def defaultParameters = _buildConfig.parameters ?: [:]
        def repoBranch = defaultParameters[branchName] ?: ''
        def repoName = branchName == 'ddkBranch' ? 'linux-ddk' : 'mtcc'
        if (repoBranch) {
            commitIdMap[repoBranch] = commitIdMap[repoBranch] ?: getLatestPackageCommitId(repoName, repoBranch, true)
        }
    }
    for (testConfig in config.tests) {
        def _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def repoBranch = defaultParameters[branchName] ?: ''
        def repoName = branchName == 'ddkBranch' ? 'linux-ddk' : 'mtcc'
        if (repoBranch) {
            commitIdMap[repoBranch] = commitIdMap[repoBranch] ?: getLatestPackageCommitId(repoName, repoBranch, true)
        }
    }
    return commitIdMap
}

def getLinuxDdkSubmoduleCommitInfo(String branch, String commitId=null, Boolean isLatest=true) {
    String content = sh(script: "curl --insecure ${genPackageUrl('linux-ddk', branch, commitId, 'commitInfo.txt', isLatest)}", returnStdout: true).trim()
    if (content =~ 'Error') {
        throw new Exception("No commitInfo found for linux-ddk/${branch}/${commit}!")
    }
    def contentMap = [:]
    print("content : ${content}")
    def matcher = content =~ /(\S+):\s+([0-9a-f]+)/
    matcher.each { match ->
        def key = match[1]
        def value = match[2]
        contentMap[key] = value
    }
    // TODO: split again by _, -, +, #
    return contentMap
}

def genCondaActivate(String envName) {
    def miniforgeExists = sh(script: '[ -d "/home/<USER>/miniforge" ]', returnStatus: true) == 0
    def anacondaExists  = sh(script: '[ -d "/home/<USER>/anaconda3" ]', returnStatus: true) == 0

    return miniforgeExists ?
        ". \"/home/<USER>/miniforge/etc/profile.d/conda.sh\" > /dev/null && conda activate ${envName}" :
        (anacondaExists ? ". \"/home/<USER>/anaconda3/etc/profile.d/conda.sh\" > /dev/null && conda activate ${envName}" : '')
}
