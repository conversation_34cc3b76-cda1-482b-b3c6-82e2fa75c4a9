import groovy.transform.Field
import groovy.json.JsonSlurperClassic

@Field def jenkinsUrl = 'http://sh-jenkins.mthreads.com'
@Field def username = 'swqa-gfx-ci'
@Field def apiToken = '11c2674c4bf21898c34c89c72767e7aa15'

def runJob(jobName, parameters) {
    def auth = "${username}:${apiToken}".bytes.encodeBase64().toString()

    // 首先检查任务是否存在
    if (!checkJobExists(jobName, auth)) {
        error("Job '${jobName}' does not exist or is not accessible. Please check the job name and permissions.")
    }

    def paramString = parameters.collect { k, v ->
        "${URLEncoder.encode(k, 'UTF-8')}=${URLEncoder.encode(v.toString(), 'UTF-8')}"
    }.join('&')

    // 获取令牌 jenkins-crumb
    def crumbField = ''
    def crumbValue = ''
    try {
        def crumbResponse = httpRequest(
            url: "${jenkinsUrl}/crumbIssuer/api/json",
            httpMode: 'GET',
            customHeaders: [
                [name: 'Authorization', value: "Basic ${auth}"]
            ],
            validResponseCodes: '200'
        )
        def crumbJson = new JsonSlurperClassic().parseText(crumbResponse.content)
        crumbField = crumbJson.crumbRequestField
        crumbValue = crumbJson.crumb
        echo "Got crumb: ${crumbField}=${crumbValue}"
    } catch (e) {
        echo "Crumb not required or failed to fetch: ${e}"
    }

    def headers = [
        [name: 'Authorization', value: "Basic ${auth}"]
    ]
    if (crumbField && crumbValue) {
        headers.add([name: crumbField, value: crumbValue])
    }

    // 尝试启动任务，对所有错误进行重试
    def triggerResponse = null
    def maxTriggerRetries = 3
    def triggerRetryCount = 0
    def lastError = null

    while (triggerRetryCount < maxTriggerRetries) {
        try {
            echo "Attempting to trigger job '${jobName}' (attempt ${triggerRetryCount + 1}/${maxTriggerRetries})"
            triggerResponse = httpRequest(
                url: "${jenkinsUrl}/job/${jobName}/buildWithParameters?${paramString}",
                httpMode: 'POST',
                customHeaders: headers,
                validResponseCodes: '201:202',  // 只接受成功状态码
                consoleLogResponseBody: true,
                quiet: true
            )

            // 如果成功，跳出重试循环
            echo "Job triggered successfully with status: ${triggerResponse.status}"
            break
        } catch (e) {
            lastError = e
            echo "Error triggering job (attempt ${triggerRetryCount + 1}/${maxTriggerRetries}): ${e.message}"
            triggerRetryCount++

            if (triggerRetryCount < maxTriggerRetries) {
                echo 'Retrying in 5 seconds...'
                sleep time: 5, unit: 'SECONDS'
            } else {
                error("Failed to trigger job '${jobName}' after ${maxTriggerRetries} attempts. Last error: ${lastError.message}")
            }
        }
    }

    def locationHeader = null
    if (triggerResponse.headers instanceof Map) {
        locationHeader = triggerResponse.headers.find { it.key == 'Location' }?.value
    } else if (triggerResponse.headers instanceof List) {
        def locationEntry = triggerResponse.headers.find { it.toString().startsWith('Location=') }
        if (locationEntry) {
            def parts = locationEntry.toString().split('=')
            if (parts.length > 1) {
                locationHeader = parts[1]
            }
        }
    }

    def buildNumber = null
    def maxRetries = 5 // 最多重试30次，每次10秒，总共5分钟

    if (locationHeader) {
        // 确保locationHeader是字符串类型
        if (locationHeader instanceof List) {
            locationHeader = locationHeader[0]
        }
        def queueUrl = locationHeader.toString() + 'api/json'
        echo "Job enqueued: ${queueUrl}"

        // 尝试从队列获取构建号
        for (int retry = 0; retry < maxRetries && buildNumber == null; retry++) {
            try {
                def queueResponse = httpRequest(
                    url: queueUrl,
                    httpMode: 'GET',
                    customHeaders: [
                        [name: 'Authorization', value: "Basic ${auth}"]
                    ],
                    validResponseCodes: '200,404',
                    quiet: true
                )

                if (queueResponse.status == 404) {
                    echo "Queue URL returned 404, but job might have started. Retrying... (${retry + 1}/${maxRetries})"
                    sleep time: 10, unit: 'SECONDS'
                    continue
                }

                def queueInfo = new JsonSlurperClassic().parseText(queueResponse.content)
                if (queueInfo.containsKey('executable') && queueInfo.executable != null) {
                    buildNumber = queueInfo.executable.number
                    echo "Got build number from queue: ${buildNumber}"
                    echo "build URL: ${jenkinsUrl}/job/${jobName}/${buildNumber}"
                } else {
                    echo "Waiting for build number... (${retry + 1}/${maxRetries})"
                    sleep time: 10, unit: 'SECONDS'
                }
            } catch (e) {
                echo "Error querying queue: ${e.message}. Retrying... (${retry + 1}/${maxRetries})"
                sleep time: 10, unit: 'SECONDS'
            }
        }
    } else {
        echo 'Warning: Cannot find Location header, but job might have been triggered successfully'
    }

    // 如果无法从队列获取构建号，尝试从最近的构建中查找
    if (buildNumber == null) {
        echo 'Could not get build number from queue, trying to find recent builds...'
        buildNumber = findRecentBuildNumber(jobName, auth)
    }

    if (buildNumber == null) {
        error("Could not determine build number for job: ${jobName}. Please check Jenkins manually.")
    }

    // 监控构建状态
    def buildCompleted = false
    def maxBuildWaitTime = 60 // 最多等待60次，每次120秒，总共2小时
    def maxStatusRetries = 3 // 每次检查状态时的重试次数

    for (int waitCount = 0; waitCount < maxBuildWaitTime && !buildCompleted; waitCount++) {
        def retryCount = 0
        def statusChecked = false
        while (retryCount < maxStatusRetries && !statusChecked) {
            try {
                def buildInfoResponse = httpRequest(
                    url: "${jenkinsUrl}/job/${jobName}/${buildNumber}/api/json",
                    httpMode: 'GET',
                    customHeaders: [
                        [name: 'Authorization', value: "Basic ${auth}"]
                    ],
                    validResponseCodes: '200,404',
                    quiet: true
                )

                if (buildInfoResponse.status == 404) {
                    // 如果构建号存在但返回404，说明构建可能被删除了，应该报错
                    error("Build ${buildNumber} not found (404). The build may have been deleted or failed to start properly.")
                }

                def buildInfo = new JsonSlurperClassic().parseText(buildInfoResponse.content)

                if (!buildInfo.building) {
                    echo "Build completed with result: ${buildInfo.result}"
                    if (buildInfo.result != 'SUCCESS') {
                        error("Remote build failed: ${buildInfo.result}")
                    }
                    buildCompleted = true
                } else {
                    echo "Waiting for build to finish... (${waitCount + 1}/${maxBuildWaitTime})"
                }
                statusChecked = true
            } catch (e) {
                retryCount++
                echo "Error checking build status (attempt ${retryCount}/${maxStatusRetries}): ${e.message}"
                if (retryCount >= maxStatusRetries) {
                    // 重试失败后报错
                    error("Failed to check build status after ${maxStatusRetries} attempts: ${e.message}")
                } else {
                    echo 'Retrying in 5 seconds...'
                    sleep time: 5, unit: 'SECONDS'
                }
            }
        }
        if (!buildCompleted) {
            sleep time: 120, unit: 'SECONDS'
        }
    }

    if (!buildCompleted) {
        error("Build monitoring timed out after ${maxBuildWaitTime * 120} seconds. Build may have hung or failed.")
    }
}

def checkJobExists(jobName, auth) {
    def maxRetries = 3
    def retryCount = 0

    while (retryCount < maxRetries) {
        try {
            def jobInfoResponse = httpRequest(
                url: "${jenkinsUrl}/job/${jobName}/api/json",
                httpMode: 'GET',
                customHeaders: [
                    [name: 'Authorization', value: "Basic ${auth}"]
                ],
                validResponseCodes: '200,404',
                quiet: true
            )

            if (jobInfoResponse.status == 404) {
                echo "Job '${jobName}' not found (404)"
                return false
            }

            def jobInfo = new JsonSlurperClassic().parseText(jobInfoResponse.content)
            if (jobInfo.buildable == false) {
                echo "Job '${jobName}' exists but is not buildable"
                return false
            }

            echo "Job '${jobName}' exists and is buildable"
            return true
        } catch (e) {
            echo "Error checking job existence (attempt ${retryCount + 1}/${maxRetries}): ${e.message}"
            retryCount++

            if (retryCount < maxRetries) {
                echo 'Retrying job existence check in 3 seconds...'
                sleep time: 3, unit: 'SECONDS'
            } else {
                echo "Failed to check job existence after ${maxRetries} attempts"
                return false
            }
        }
    }
    return false
}

def findRecentBuildNumber(jobName, auth) {
    def maxRetries = 3
    def retryCount = 0

    while (retryCount < maxRetries) {
        try {
            def jobInfoResponse = httpRequest(
                url: "${jenkinsUrl}/job/${jobName}/api/json",
                httpMode: 'GET',
                customHeaders: [
                    [name: 'Authorization', value: "Basic ${auth}"]
                ],
                validResponseCodes: '200',
                quiet: true
            )

            def jobInfo = new JsonSlurperClassic().parseText(jobInfoResponse.content)
            if (jobInfo.builds && jobInfo.builds.size() > 0) {
                def latestBuild = jobInfo.builds[0]
                def buildTime = latestBuild.timestamp
                def currentTime = System.currentTimeMillis()

                // 检查构建是否在最近5分钟内启动
                if (currentTime - buildTime < 300000) { // 5分钟 = 300000毫秒
                    echo "Found recent build: ${latestBuild.number}"
                    return latestBuild.number
                }
            }
            return null
        } catch (e) {
            echo "Error finding recent build (attempt ${retryCount + 1}/${maxRetries}): ${e.message}"
            retryCount++

            if (retryCount < maxRetries) {
                echo 'Retrying recent build search in 3 seconds...'
                sleep time: 3, unit: 'SECONDS'
            } else {
                echo "Failed to find recent build after ${maxRetries} attempts"
                return null
            }
        }
    }
    return null
}
