import groovy.transform.Field
import org.swqa.tools.common

@Field def commonLib = new common()

@Field String defaultExcludeFiles = '.*\\.h'
@Field Map excludeFiles = [
    'mt-audio-driver': defaultExcludeFiles,
    'mt-dcgm': '/workspaces/mt-dcgm/testing/.*|(.*\\.h)',
    'DirectStream': '.*/(common/thirdparty|third_lib/vaapi_pkgconfig)/.*|(.*\\.h)',
    'wave517_fw': defaultExcludeFiles,
    'wave627_fw': '.*/fw.*dec/.*',
    'gr-umd': '.*/(gr-umd/(binary|unittests/|opencl/|external/submodules/)|usr/include/|root/).*|(.*\\.h)',
    'mt-video-drv': '^(?!.*/mtvpu/).*|(.*\\.h)',
    'MTJPEGSDK': '.*/(mt_video|src/3rdparty|samples/(include|mtJPEG.*))/.*|(.*\\.h)',
    'mt-management-platform': '.*/mt-management-platform/(3rd|test)/.*|(.*\\.h)'
]

@Field String coverityPath = '/home/<USER>/cov-analysis-linux64-2022.3.1'
@Field String coverityBinPath = '/home/<USER>/cov-analysis-linux64-2022.3.1/bin'

def analyze(String binPath=coverityBinPath, String idir, Boolean isAccess=false, String stripPath=env.WORKSPACE, String excludeFiles=defaultExcludeFiles) {
    sh """
        sudo ${binPath}/cov-manage-emit --dir ${idir} reset-host-name
        sudo ${binPath}/cov-analyze --wait-for-license --dir ${idir} ${env.granularity} --parse-warnings-config ${coverityBinPath}/../config/parse_warnings.conf --strip-path ${stripPath}
    """
    if (isAccess) {
        sh """
            sudo ${binPath}/cov-format-errors --dir ${idir} --exclude-files "${excludeFiles}" --html-output html || true
        """
    }
}

def upstream(String binPath=coverityBinPath, String idir, String excludeFiles=defaultExcludeFiles, String stream, String description) {
    credentials.runWithCredential('COV') {
        sh """
            sudo ${binPath}/cov-commit-defects --dir ${idir} --exclude-files "${excludeFiles}" --url http://sh-coverity.mthreads.com:8080/ --user ${username} --password ${password} --stream ${stream} --description ${description}
        """
    }
}

def checkReport(String binPath=coverityBinPath, String idir, String excludeFiles=defaultExcludeFiles, String stream) {
    credentials.runWithCredential('COV') {
        sh """
            sudo ${binPath}/cov-commit-defects --dir ${idir} --exclude-files "${excludeFiles}" --url http://sh-coverity.mthreads.com:8080/ --user ${username} --password ${password} --stream ${stream} --preview-report-v2 preview-report.json || true
        """
    }
    if (fileExists('html/index.html') || env.check_switch == 'on') {
        sh "sudo cp -rf preview-report.json html/index.html ${idir}"
        commonLib.loadScript('coverity_check.py', 'coverity', false)
        sh 'sudo python3 ./coverity_check.py preview-report.json html/index.html'
        commonLib.loadScript('checkReport_access.sh', 'coverity')
        sh './checkReport_access.sh preview-report.json'
    }
}

def cleanIdir(String idir='') {
    def targetDir = idir ?: "/data/samba/sast/idir-${env.JOB_NAME}-${env.BUILD_NUMBER}"
    stage('del idir') {
        sh "sudo rm -rf ${targetDir} ||:"
    }
}
