import groovy.transform.Field
import groovy.json.JsonSlurper

@Field OSS_CI = 'https://swci-oss.mthreads.com'
@Field OSS_CI_USER = 'mtoss-swci'
@Field OSS_CI_PWD = 'H65KwcY1poayvgqTEReHBJu'
@Field OSS = 'https://oss.mthreads.com'
@Field OSS_USER = 'mtoss-swci'
@Field OSS_PWD = 'Z2Z4LWNp'

def verify() {
    try {
        sh 'mc -v'
        return true
    } catch (exc) {
        return false
    }
}

def checkOSSAlias(String host, String alias_name, String username, String password) {
    try {
        String content = utils.runCommandWithStdout("mc alias ls ${alias_name} --json")
        def lines = content.readLines()
        def jsonLine = lines.find { it.startsWith('{') }
        println(jsonLine)
        def jsonSlurper = new JsonSlurper()
        def data = jsonSlurper.parseText(jsonLine)
        return data.status == 'success' && data.URL == host && data.accessKey == username && data.secretKey == password
    } catch (exc) {
        return false
    }
}

def checkOSSAlias(String alias_name) {
    try {
        String content = utils.runCommandWithStdout("mc alias ls ${alias_name} --json")
        def lines = content.readLines()
        def jsonLine = lines.find { it.startsWith('{') }
        println(jsonLine)
        def jsonSlurper = new JsonSlurper()
        def data = jsonSlurper.parseText(jsonLine)
        return data.status == 'success'
    } catch (exc) {
        return false
    }
}

/**
 * Sets up MinIO client aliases for both OSS endpoints
 *
 * @param username Username for the OSS endpoint
 * @param password Password for the OSS endpoint
 * @return Map containing the status of each alias setup operation
 */
def setUp(String username=OSS_USER, String password=OSS_PWD) {
    def setupAlias = { String aliasName, String endpoint, String user, String pwd ->
        def cmd = "mc alias set ${aliasName} ${endpoint} ${user} ${pwd}"
        try {
            utils.retryWithRandomBackoff(600, 10) {
                utils.runCommand(cmd)
                echo "Successfully set alias for ${aliasName}"
            }
            return true
        } catch (e) {
            utils.showErrorMessage("Failed to set alias for ${aliasName} after multiple retries: ${e.message}")
            return false
        }
    }

    def ossResult = true
    def ossciResult = true
    // Execute both commands independently
    if (!checkOSSAlias(OSS, 'oss', username, password)) {
        ossResult = setupAlias('oss', OSS, username, password)
    }
    if (!checkOSSAlias(OSS_CI, 'swci-oss', OSS_CI_USER, OSS_CI_PWD)) {
        ossciResult = setupAlias('swci-oss', OSS_CI, OSS_CI_USER, OSS_CI_PWD)
    }

    // Return a map with the status of each operation
    return [
        'oss': ossResult,
        'swci-oss': ossciResult
    ]
}

def install(String username=OSS_USER, String password=OSS_PWD) {
    try {
        if (!verify()) {
            def arch = sh(script: 'arch', returnStdout: true).trim()
            def mcUrl = constants.mcUrl[arch]
            utils.retryWithRandomBackoff(600, 10, 180) {
                sh """
                    rm -rf /usr/local/bin/mc ||:
                    wget -q --no-check-certificate ${mcUrl} -O /usr/local/bin/mc
                    chmod 755 /usr/local/bin/mc
                """
            }
        }
        return setUp(username, password)
    } catch (exc) {
        utils.showErrorMessage("Failed to install mc: ${exc.message}")
        return false
    }
}

def getOssAlias(String ossPath) {
    def parts = ossPath.tokenize('/')
    if (parts && (parts[0] == 'oss' || parts[0] == 'swci-oss')) {
        return parts[0]
    }
    return null
}

def cp(String src, String dest = './', recursive = true, retries = 30) {
    // mc cp oss/xx//file will failed ...
    src = src.replaceAll('//+', '/')
    dest = dest.replaceAll('//+', '/')
    def attempt = 0
    int elapsed = 0
    int interval = 60
    def copyFailed = false
    def copyFinished = false
    def errorMessage = ''
    def ossAlias = getOssAlias(dest) ?: getOssAlias(src)

    utils.waitUntilTimeout(time: env.ossTimeout ?: 60, unit: 'MINUTES', initialRecurrencePeriod: 30000, quiet: true) {
        attempt++
        copyFailed = false
        try {
            parallel(
                cpTask: {
                    try {
                        println "🟢 Start copying from ${src} to ${dest}"
                        def cmd = recursive ? 'mc cp -r' : 'mc cp'
                        utils.runCommand("${cmd} ${src} ${dest}")
                        copyFinished = true
                        println '✅ Copy succeeded'
                    } catch (exc) {
                        println '❌ Copy failed'
                        copyFailed = true
                        throw exc
                    }
                },
                heartbeat: {
                    while (!copyFinished && !copyFailed) {
                        if (!checkOSSAlias(ossAlias)) {
                            utils.showErrorMessage("❌ OSS alias ${ossAlias} heartbeat failed at ${elapsed}s")
                            error "💔 OSS alias ${ossAlias} heartbeat failed"
                        }
                        println "💓 Heartbeat for ${ossAlias} passed at ${elapsed}s"
                        int slept = 0
                        while (slept < interval) {
                            if (copyFinished || copyFailed) {
                                return
                            }
                            sleep(time: 1, unit: 'SECONDS')
                            slept += 1
                        }
                        elapsed += interval
                    }
                },
                failFast: true
            )
        } catch (exc) {
            if (exc instanceof org.jenkinsci.plugins.workflow.steps.FlowInterruptedException
                || exc instanceof InterruptedException) {
                throw exc
                }
            utils.showErrorMessage("⚠️ Attempt ${attempt} failed: ${exc.message}")
            errorMessage = exc.message
        }
        return copyFinished || (attempt == retries)
    }
    if (!copyFinished) {
        error "❌ Failed after ${attempt} attempts: ${errorMessage}"
    }
}

def ls(String src) {
    utils.retryWithRandomBackoff(600, 10) {
        return utils.runCommandWithStdout("mc ls ${src}")
    }
}
