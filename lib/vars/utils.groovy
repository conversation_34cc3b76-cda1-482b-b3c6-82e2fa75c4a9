import java.security.SecureRandom
import org.apache.commons.lang3.StringEscapeUtils
import groovy.json.JsonSlurper

import org.swqa.tools.common

def listFirstFolderInTarFile(String tarFile) {
    return sh(script: "tar --list -f ${tarFile} | head -n 1", returnStdout: true).replace('/', '').trim()
}

def curl(String url) {
    return sh(script: "curl ${url}", returnStdout: true).trim()
}

def gitlabHttpRequest(Map config) {
    credentials.runWithCredential('API_GITLAB') {
        // 设置默认值
        def defaultConfig = [
            timeout: 10,
            validResponseCodes: '100:499',  // 可能会碰到409, conflict, 设置gitlab status的时候, 某些状态不能cancel, 但是我们目前代码中用法是不管直接设置, 所以这里添加4xx避免exception
            consoleLogResponseBody: true,
            quiet: true
        ]

        // 合并默认配置和用户配置
        def finalConfig = defaultConfig + config

        // 添加PRIVATE-TOKEN header
        finalConfig.customHeaders = finalConfig.customHeaders ?: []
        finalConfig.customHeaders.add([name: 'PRIVATE-TOKEN', value: API_TOKEN_GITLAB])

        def response = steps.httpRequest(finalConfig)
        log.debug("HTTP ${finalConfig.httpMode ?: 'GET'} ${finalConfig.url} - Status: ${response.status}")
        return response
    }
}

def retryClosure(Number seconds=60 * 10, Closure closure) {
    timeout(time: seconds, unit: 'SECONDS') {
        waitUntil {
            try {
                closure.call()
                true
            } catch (error) {
                input 'retry ?'
                false
            }
        }
    }
}

/**
 * waitUntilTimeout - 类似 Jenkins 的 waitUntil，但整体超时可生效
 *
 * 参数（Map，与 waitUntil 风格一致）:
 *   time                    - 整体超时数值（默认 60）
 *   unit                    - 'SECONDS' | 'MINUTES' | 'HOURS'（默认 'MINUTES'）
 *   initialRecurrencePeriod - 轮询间隔（毫秒，默认 1000）
 *   quiet                   - body 抛错时是否静默（默认 false）
 *   failFast                - body 抛非中断类异常时是否立刻失败（默认 false）
 *
 * 行为：
 *   - body 返回 true => 循环结束
 *   - body 返回 false/null/空 => 继续循环
 *   - body 抛异常 => quiet=false 打印异常，quiet=true 静默
 *   - failFast=true 时遇到异常立即失败，否则继续循环
 *   - 整体超时由外层 timeout 控制，保证能强制中止
 */
def waitUntilTimeout(Map args = [:], Closure body) {
    if (body == null) {
        error 'waitUntilTimeout: body closure is required'
    }

    int overallTimeoutValue = (args.time ?: 60) as int
    String overallTimeoutUnit = (args.unit ?: 'MINUTES').toString().toUpperCase()
    long recurrencePeriodMillis = (args.initialRecurrencePeriod ?: 1000L) as long
    boolean isQuietMode = (args.quiet ?: false) as boolean
    boolean isFailFastMode = (args.failFast ?: false) as boolean

    // 将毫秒转换为秒（至少 1 秒，避免 0 秒循环过快）
    int recurrencePeriodSeconds = Math.max(1, ((recurrencePeriodMillis + 999L) / 1000L) as int)

    timeout(time: overallTimeoutValue, unit: overallTimeoutUnit) {
        while (true) {
            boolean conditionSatisfied = false
            try {
                def bodyResult = body.call()
                // 将返回值强制转换为布尔值（null/空视为 false）
                conditionSatisfied = (bodyResult ? true : false)
            } catch (org.jenkinsci.plugins.workflow.steps.FlowInterruptedException flowInterrupted) {
                // 外层 timeout 或用户中断 => 必须抛出，不能吞掉
                throw flowInterrupted
            } catch (InterruptedException interrupted) {
                // 系统中断信号 => 必须抛出
                throw interrupted
            } catch (exc) {
                if (!isQuietMode) {
                    echo "waitUntilTimeout: body error: ${exc.message}"
                }
                if (isFailFastMode) {
                    throw exc
                }
            // 否则忽略错误，继续循环直到超时
            }

            if (conditionSatisfied) {
                return
            }

            try {
                sleep time: recurrencePeriodSeconds, unit: 'SECONDS'
            } catch (org.jenkinsci.plugins.workflow.steps.FlowInterruptedException flowInterruptedDuringSleep) {
                // timeout 可能在 sleep 触发 => 必须抛出
                throw flowInterruptedDuringSleep
            } catch (InterruptedException interruptedDuringSleep) {
                throw interruptedDuringSleep
            }
        }
    }
}

def catchErrorContinue(Closure closure) {
    try {
        closure.call()
    } catch (e) {
        echo "Error: ${e.message}. Ignoring error and continuing."
    }
}

def runCommand(String command) {
    if (isUnix()) { return sh(script: command) }
    return bat(script: command)
}

def runCommandWithStdout(String command) {
    if (isUnix()) { return sh(script: command, returnStdout: true).trim() }
    return bat(script: command, returnStdout: true).trim()
}

def runCommandWithStatus(String command) {
    if (isUnix()) { return sh(script: command, returnStatus: true) }
    return bat(script: command, returnStatus: true)
}

def installConda() {
    if (env.condaPackageUrl) {
        sh """
            wget -q ${env.condaPackageUrl}
            mkdir -p /home/<USER>
            tar -xf ${env.condaPackageUrl.split('/')[-1]} -C /home/<USER>
        """
    }else {
        print('no conda url do nothing')
    }
}

def checkJiraId() {
    log.info("gitlabMergeRequestTitle: ${env.gitlabMergeRequestTitle}")
    log.info("gitlabMergeRequestDescription: ${env.gitlabMergeRequestDescription}")
    def regex = /(?i)\b(SW|DIAGSYS|KUAE|COMPILER|ARCH|PRM|LS|PH\d+S?|PH\d+GPU|VPS|COMPILERIN|QY|HG|AP|AS|PERF|RAT|HS|MTVDI)\b-\d+/
    if (!env.gitlabMergeRequestTitle || env.gitlabSourceRepoName == 'SWQA-CI') { return }
    if (!(StringEscapeUtils.unescapeJava(env.gitlabMergeRequestTitle) =~ regex || StringEscapeUtils.unescapeJava(env.gitlabMergeRequestDescription) =~ regex)) {
        currentBuild.description = currentBuild.description ?: ''
        currentBuild.description += 'Merge request title|description must contain a jira id.<br>'
        currentBuild.description += "Current supported regex: ${regex}<br>"
        currentBuild.description += 'Add comment CIRERUN when you fix this.<br>'
        error 'Merge request title|description must contain a jira id.'
    }
}

def checkCard() {
    try {
        def result = sh(script: 'lspci | grep 1ed5', returnStdout: true)
        println result
    } catch (exc) {
        new common().toggleNodeOffline(env.NODE_NAME, true, 'Missing card')
        throw new Exception('Missing card!')
    }
}

def showErrorMessage(message) {
    catchError(buildResult: null, stageResult: null) {
        error(message)
    }
}

def uploadDbgSym(String workDir, String ossScriptPath, String extraArgs = '') {
    oss.install()
    def scriptsName = ossScriptPath.split('/')[-1]
    def localScriptPath = "/usr/local/bin/${scriptsName}"
    sh """
        mc cp ${ossScriptPath} ${localScriptPath}
        chmod 755 ${localScriptPath}
        find ${workDir} -name "*.dbg" -exec ${scriptsName} {} ${extraArgs} \\;
        find ${workDir} -name "*.dbg" -delete
    """
}

def isEmpty(obj) {
    obj == null || obj == 'null' || obj?.isEmpty() ?: false
}

def setStatus(name, status) {
    curl("http://swqa.mthreads.com:6379/SET/${name}/${status}")
}

def getStatus(name) {
    String jsonStr = curl("http://swqa.mthreads.com:6379/GET/${name}")
    def status = new JsonSlurper().parseText(jsonStr)
    return status.GET
}

def getEnvString() {
    def result = ''
    for (key in env.getEnvironment().keySet()) {
        result += "${key}=${env[key]}\n"
    }
    return result
}

/**
 * Retries a function with random wait times until it succeeds or the maximum retry time is reached.
 *
 * @param maxRetryTimeSeconds Maximum time in seconds to keep retrying
 * @param maxIntervalSeconds Maximum interval in seconds between retries (actual interval will be random between 1 and this value)
 * @param closure The function to retry
 * @return The result of the function if successful, or throws the last error if all retries fail
 */
def retryWithRandomBackoff(int maxRetryTimeSeconds = 600, int maxIntervalSeconds = 10, int perTryTimeoutSeconds = 60, Closure closure) {
    def attempt = 0
    def rng = new SecureRandom()

    timeout(time: maxRetryTimeSeconds, unit: 'SECONDS') {
        waitUntil(quiet: true) {
            attempt++
            try {
                // Avoid deadlock in closure
                timeout(time: perTryTimeoutSeconds, unit: 'SECONDS') {
                    closure.call()
                }
                echo "Function succeeded on attempt ${attempt}"
                return true
            } catch (exc) {
                def randomWaitSeconds = 1 + rng.nextInt(maxIntervalSeconds)
                echo "Attempt ${attempt} failed: ${exc.message}. Retrying in ${randomWaitSeconds} seconds..."
                sleep(time: randomWaitSeconds, unit: 'SECONDS')
                return false  // waitUntil
            }
        }
    }
}

def toStringParams(Map params) {
    def strParams = [:]
    params.each { k, v ->
        strParams[k] = v instanceof CharSequence ? v : groovy.json.JsonOutput.toJson(v)
    }
    return strParams
}

def runScripts(List<String> commands, boolean failFast = true, String label = 'Custom Scripts') {
    /* groovylint-disable-next-line UnnecessaryCollectCall */
    commands = commands.collect { it.replaceAll(/^(['"])(.*)\1$/, '$2') }
    def date = new Date().format('yyyy.MM.dd')
    def isNodeUnix = isUnix()
    def tempFileName = isNodeUnix ? '.temp_script.sh' : 'temp_script.bat'
    def runCmd

    if (isNodeUnix) {
        def header = """
            #!/bin/bash
            # ========================================
            # Auto-generated shell script: ${tempFileName}
            # Label: ${label}
            # Created by Jenkins on ${date}
            # ========================================
            set -e
        """.stripIndent()
        def scriptContent = [header.trim(), commands.join('\n')].join('\n\n')
        writeFile(file: tempFileName, text: scriptContent)
        runCmd = "chmod +x ${tempFileName}; ./${tempFileName}"
        echo '📜 Script Content:'
        sh "cat ${tempFileName}"
    } else {
        def header = """
            @echo off
            REM ========================================
            REM Auto-generated batch script: ${tempFileName}
            REM Label: ${label}
            REM Created by Jenkins on ${date}
            REM ========================================
        """.stripIndent()
        def echoLines = [header.trim(), commands.join('\r\n')].join('\r\n\r\n')
        writeFile(file: tempFileName, text: echoLines)
        runCmd = tempFileName
        echo '📜 Script Content:'
        bat "type ${tempFileName}"
    }

    try {
        echo "🚀 Running: ${label}"
        runCommand(runCmd)
        echo "✅ Script completed: ${label}"
    } catch (err) {
        echo "❌ Error in script '${label}': ${err.getMessage()}"
        if (failFast) {
            error('Stopping pipeline due to script failure.')
        }
    }
}

/**
 * 追加仓库版本信息到现有内容
 * @param versionContent 现有版本信息内容
 * @return 更新后的版本信息内容
 */
def appendRepoInfo(String versionContent, String repo, String branch, String commitId) {
    return versionContent + "${repo} | ${branch} | ${commitId}\n"
}

/**
 * 从OSS下载版本信息文件，若不存在则本地创建
 * @param ossPath OSS路径
 * @param versionFile 版本文件名
 */
def downloadVersionFileFromOSS(String ossPath, String versionFile = 'musa_sdk_commit.txt') {
    if (ossPath) {
        try {
            oss.install()
            oss.cp("${ossPath}/${versionFile}", './', recursive = true, retries = 3)
            println "从OSS下载版本信息文件成功: ${ossPath}/${versionFile}"
            return true
        } catch (e) {
            println "从OSS下载版本信息文件失败: ${e.getMessage()}"
            // 替换为 Jenkins 原生步骤创建文件
            if (!fileExists(versionFile)) {
                writeFile file: versionFile, text: 'repo | branch | commit\n' // 创建文件，写入表头
                println "本地创建版本信息文件成功: ${versionFile}"
            } else {
                println "本地文件已存在: ${versionFile}"
            }
            return false
        }
    }
    return false
}

/**
 * 上传版本信息文件到OSS
 */
def uploadVersionFileToOSS(String ossPath, String versionFile = 'musa_sdk_commit.txt') {
    if (ossPath && fileExists(versionFile)) {
        try {
            artifact.upload(versionFile, ossPath)
            println "上传版本信息文件到OSS成功: ${ossPath}/${versionFile}"
            return true
        } catch (e) {
            println "上传版本信息文件到OSS失败: ${e.getMessage()}"
            return false
        }
    }
    return false
}

/**
 * 更新版本信息文件，添加当前仓库的信息
 * @return 更新后的版本信息内容
 */
def updateVersionInfo(String repo, String branch, String commitId, String versionFile = 'musa_sdk_commit.txt') {
    def versionContent = ''

    // 如果版本文件已存在，则读取内容
    if (fileExists(versionFile)) {
        versionContent = readFile(file: versionFile)
        // 确保最后一行有换行符
        if (!versionContent.endsWith('\n')) {
            versionContent += '\n'
        }
    } else {
        println "不存在版本信息文件: ${versionFile}"
    }

    // 追加当前仓库信息
    versionContent = appendRepoInfo(versionContent, repo, branch, commitId)
    writeFile file: versionFile, text: versionContent
    println "已更新版本信息文件: ${versionFile}"
    return versionContent
}

/**
 * 追加当前仓库的版本信息到 musa_sdk_commit.txt
 */
def appendVersionInfo(String repo, String branch, String commitId, String ossPath, String versionFile = 'musa_sdk_commit.txt') {
    // 从OSS下载现有的版本信息文件
    downloadVersionFileFromOSS(ossPath, versionFile)

    // 更新版本信息文件，添加当前仓库信息
    updateVersionInfo(repo, branch, commitId, versionFile)

    // 上传版本信息文件
    uploadVersionFileToOSS(ossPath, versionFile)
}

/**
 * 合并参数映射
 * @param defaultParameters 默认参数
 * @param additionalParameters 额外参数
 * @return 合并后的参数映射
 */
def mergeParameters(defaultParameters, additionalParameters) {
    def parameters = [:]
    // 添加额外参数
    if (additionalParameters) {
        additionalParameters.each { key, value ->
            parameters[key] = value
        }
    }
    // 复制默认参数，不覆盖已存在的参数
    if (defaultParameters) {
        defaultParameters.each { key, value ->
            if (!parameters.containsKey(key)) {
                parameters[key] = value
            }
        }
    }
    return parameters
}

/**
 * 从节点选择器中提取GPU类型
 * @param podNodeSelector 节点选择器字符串
 * @return GPU类型字符串
 */
def getGpuTypeFromSelector(podNodeSelector) {
    def gpuType = 'unknown'
    if (podNodeSelector) {
        def matcher = podNodeSelector =~ /GPU_TYPE=(\w+)/
        if (matcher.find()) {
            gpuType = matcher.group(1)
        }
    }
    return gpuType
}

/**
 * 生成环境变量导出语句的通用函数
 * @param env 流水线环境变量对象（包含exports、gcov等属性）
 * @param includeGcov 是否需要根据gcov状态添加GCOV_TEST=ON（默认false）
 * @return 生成的export语句字符串
 */
def generateEnvExport(env, boolean includeGcov = false) {
    // 1. 处理原始exports字符串：拆分、去空白、过滤空元素
    List splitEnvs = env.exports?.split(';') ?: []  // 拆分字符串，兼容null
    List trimmedEnvs = []
    for (def item : splitEnvs) {                    // 手动trim，避开spread和collect
        trimmedEnvs.add(item.trim())
    }

    List envs = []
    for (def item : trimmedEnvs) {                  // 手动过滤空元素，避开findAll
        if (item) {  // 非空字符串才保留
            envs.add(item)
        }
    }

    // 2. 根据参数判断是否添加GCOV_TEST=ON
    if (includeGcov && env.gcov && env.gcov.toString().toUpperCase() == 'ON') {
        envs.add('GCOV_TEST=ON')
    }

    // 3. 生成最终的export语句
    def envExport = envs.size() > 0 ? "export ${envs.join(' && export ')}" : ''

    // 4. 打印结果（便于调试）
    println "Generated envExport: ${envExport}"

    return envExport
}
