import groovy.transform.Field
import groovy.json.JsonOutput

import org.swqa.tools.k8s
import org.swqa.tools.common
import org.swqa.tools.git

@Field def commonLib = new common()
@Field def gitLib = new git()

def *********************() {
    env.mergeRequestLink = "https://sh-code.mthreads.com/sw/${env.gitlabSourceRepoName}/merge_requests/${env.gitlabMergeRequestIid}"
    if (env.gitlabActionType == 'PUSH') {
        currentBuild.description = "Triggered by ${env.gitlabUserName ?: ''} Gitlab PUSH: ${env.gitlabSourceRepoName} | ${env.gitlabSourceBranch} | ${env.gitlabMergeRequestLastCommit}<br>" + currentBuild.description
    } else if (env.gitlabMergeRequestIid && env.gitlabSourceBranch && env.gitlabTargetBranch && !currentBuild.description.contains('Triggered by')) {
        currentBuild.description = "Triggered by ${env.gitlabUserName ?: ''} <a href=\"${env.mergeRequestLink}\">${env.gitlabSourceRepoName} #${env.gitlabMergeRequestIid}</a>: ${env.gitlabSourceBranch} => ${env.gitlabTargetBranch}<br>" + currentBuild.description
    }
}

def runInNode(Closure preClosure, Closure mainClosure, Closure postClosure) {
    node(env.nodeLabel) {
        env.buildRunningTime = System.currentTimeMillis()
        env.jenkinsNode = env.NODE_NAME
        env.nodeIP = commonLib.getNodeIP()
        currentBuild.description = currentBuild.description ?: ''
        currentBuild.description += "Node: ${env.NODE_NAME}<br>"
        utils.catchErrorContinue { apt.updateAptSourcesList() }
        utils.catchErrorContinue { timeout(5) { deleteDir() } }
        catchError(buildResult: null, stageResult: null) { sendDataToDashboard('RUNNING') }
        // cancel auto merge
        gitLib.cancelAutomerge()
        if (env.testLabel) {
            // set status to canceled because can not set status from running to pending
            gitLib.setGitlabStatus(env.testLabel, 'canceled')
            gitLib.setGitlabStatus(env.testLabel, 'running')
        }
        try {
            preClosure && preClosure()
            // mount umd, firmware
            insideParams = '-i -u 0:0 --privileged' + (env.mountParms ? " ${env.mountParms}" : '')
            // docker stop may timeout, ERROR: Timeout after 180 seconds, so postClosure won't get change to execute
            if (env.containerImage) {
                sh 'docker ps || systemctl restart docker.service'
                docker.image(env.containerImage).inside(insideParams) {
                    catchError(buildResult: null, stageResult: null) {
                        apt.updateAptSourcesList()
                    }
                    mainClosure()
                }
            } else {
                mainClosure()
            }
            postClosure && postClosure()
            if (env.testLabel) {
                gitLib.setGitlabStatus(env.testLabel, 'success')
            }
        } catch (exc) {
            if (env.testLabel) {
                gitLib.setGitlabStatus(env.testLabel, 'failed')
            }
            if (isUnix() && env.containerImage) { sh 'dmesg -T || :' }
            error(exc.message)
        } finally {
            if (env.JOB_NAME.contains('test.') && env.disableTearDown != 'true') {
                stage('test tear down') { commonLib.tearDown() }
            // if (!env.JOB_NAME.contains('vps')) {
            //     commonLib.reboot(env.NODE_NAME)
            // }
            }
        }
    }
}

def runInPod(Closure preClosure, Closure mainClosure, Closure postClosure) {
    def sshPort = "1${('111' + BUILD_NUMBER)[-4..-1]}"
    def containerConfig = [
        image: env.containerImage,
        nodeSelector: env.podNodeSelector,
        resources: env.podResources,
        sshPort: "${sshPort}"
    ]
    String podYaml = new k8s().genPodYaml(env.JOB_NAME, containerConfig)
    // there's always a jnlp container, worker is normally for build|test, jnlp is normally for workflow
    String containerName = env.containerImage ? 'worker' : 'jnlp'
    podTemplate(
        cloud: env.cluster,
        yaml: podYaml
    ) {
        node(POD_LABEL) {
            if (env.testLabel) {
                // set status to canceled because can not set status from running to pending
                gitLib.setGitlabStatus(env.testLabel, 'canceled')
                gitLib.setGitlabStatus(env.testLabel, 'running')
            }
            try {
                preClosure && container('jnlp') { preClosure() }
                container(containerName) {
                    env.buildRunningTime = System.currentTimeMillis()
                    catchError(buildResult: null, stageResult: null) {
                        apt.updateAptSourcesList()
                    }
                    env.jenkinsNode = sh(script: "echo \$K8S_NODE_NAME", returnStdout: true).trim()
                    currentBuild.description = currentBuild.description ?: ''
                    currentBuild.description += "Node: ${env.jenkinsNode}<br>"
                    catchError(buildResult: null, stageResult: null) { sendDataToDashboard('RUNNING') }
                    catchError(buildResult: null) {
                        sh '''
                            chmod 777 /tmp
                            printf "\nPort 22\nPermitRootLogin yes\n" >>/etc/ssh/sshd_config
                            printf "123456\n123456\n" |passwd root
                            chmod 600 /etc/ssh/* || :
                            /etc/init.d/ssh restart
                        '''
                        currentBuild.description += "Entering POD: sshpass -p '123456' ssh -o 'StrictHostKeyChecking=no' root@${env.jenkinsNode} -p ${sshPort}<br>"
                        log.info("Entering POD: sshpass -p '123456' ssh -o 'StrictHostKeyChecking=no' root@${env.jenkinsNode} -p ${sshPort}")
                        def mtgpuID = sh(script:'ls /dev/mtgpu* ||:', returnStdout:true).trim()
                        if (mtgpuID) { currentBuild.description += "mtgpuID:${mtgpuID}<br>" }
                        def gmiInfo = sh(script:'mthreads-gmi -L ||:', returnStdout:true).trim()
                        if (gmiInfo) { currentBuild.description += "mthreads-gmi -L :${gmiInfo}<br>" }
                    }

                    // cancel auto merge
                    gitLib.cancelAutomerge()
                    mainClosure()
                }
                postClosure && container('jnlp') { postClosure() }
                if (env.testLabel) {
                    gitLib.setGitlabStatus(env.testLabel, 'success')
                }
            } catch (exc) {
                if (env.testLabel) {
                    gitLib.setGitlabStatus(env.testLabel, 'failed')
                }
                error(exc.message)
            }
        }
    }
}

def runInVM(Closure preClosure, Closure mainClosure, Closure postClosure) {
    def containerConfig = [
        vmImage: env.vmImage,
        vmGpuRequest: env.vmGpuRequest,
        vmCpuRequest: env.vmCpuRequest ?: '16',
        vmMemoryRequest: env.vmMemoryRequest ?: '32Gi',
    ]
    String podYaml = new k8s().genVMPodYaml(env.JOB_NAME, containerConfig)
    // there's always a jnlp container, worker is normally for build|test, jnlp is normally for workflow
    podTemplate(
        cloud: env.cluster ?: 'gccFarm',
        yaml: podYaml
    ) {
        node(POD_LABEL) {
            env.buildRunningTime = System.currentTimeMillis()
            env.jenkinsNode = env.NODE_NAME
            catchError(buildResult: null, stageResult: null) { sendDataToDashboard('RUNNING') }
            // constants.addMtServiceHosts()
            apt.updateAptSourcesList()
            preClosure && preClosure()
            mainClosure && mainClosure()
            postClosure && postClosure()
        }
    }
}

def start(String choice, Map closures) {
    run(choice, closures.pre, closures.main, closures.post)
}

def start(String choice, Closure closure) {
    run(choice, null, closure, null)
}

def run(String choice, Closure preClosure, Closure mainClosure, Closure postClosure) {
    // TODO mainClosure required
    Exception caughtError = null
    def varAlias = [
        'pkgName': 'packageName'
    ]
    varAlias.each { name1, name2 ->
        if (!utils.isEmpty(env[name1]) || !utils.isEmpty(env[name2])) {
            env[name1] = utils.isEmpty(env[name1]) ? env[name2] : env[name1]
            env[name2] = utils.isEmpty(env[name2]) ? env[name1] : env[name2]
        }
    }
    env.buildRunningTime = currentBuild.startTimeInMillis
    env.startTime = new Date().format('yyyy-MM-dd HH:mm:ss')
    // in case we see description like "null<br>added description"
    if (currentBuild.description) {
        currentBuild.description += '<br>'
    } else {
        currentBuild.description = ''
    }
    // parse triggerInfo
    if (env.triggerInfo) {
        log.debug("triggerInfo: ${env.triggerInfo}")
        env.triggerInfo.split(constants.triggerInfoDelimiter).each { line ->
            if (line) {
                def (key, value) = line.trim().split(':', 2)
                if (!env."${key}") {
                    log.debug("Setting ${key} = ${value}")
                    env."${key}" = value
                } else {
                    log.debug("${key} exists, skip setting ${key} = ${value}")
                }
            }
        }
        env.branch = env.branch ?: env.gitlabSourceBranch
    }

    *********************()

    catchError(buildResult: null, stageResult: null) { sendDataToDashboard('PENDING') }
    // if (env.gitlabMergeRequestIid) {
    //     def mrState = gitLib.getGitlabMRStatus()
    //     if (mrState != 'opened') {
    //         currentBuild.result = 'ABORTED'
    //         error("This merge request of repo ${env.gitlabSourceRepoName} is not opened.")
    //     }
    // }

    def choices = [
        'node': { runInNode(preClosure, mainClosure, postClosure) },
        'pod': { runInPod(preClosure, mainClosure, postClosure) },
        'vm': { runInVM(preClosure, mainClosure, postClosure) },
    ]
    try {
        // if (env.pipelineTimeout) {
        //     timeout(time: env.pipelineTimeout, unit: 'MINUTES') { choices[choice]() }
        // } else {
        //     choices[choice]()
        // }
        if (env.skip_me == 'true') {
            node {
                gitLib.setGitlabStatus(env.testLabel, 'canceled')
                gitLib.setGitlabStatus(env.testLabel, 'skipped')
            }
        } else {
            choices[choice]()
        }
    } catch (exc) {
        caughtError = exc
        if (env.mailReceiver) {
            catchError(buildResult: null, stageResult: null) {
                emailext(
                    subject: env.mailSubject ?: constants.genDefaultMailSubject(),
                    body: env.mailContent ?: constants.genDefaultMailContent(),
                    to: env.mailReceiver,
                )
            }
        }
        error("Caught exception: ${exc.getClass()} - ${exc.getMessage()}")
    } finally {
        def status = getBuildStatus(caughtError)

        echo '🧾 Final Status:'
        echo "Result: ${status.result}"
        echo "Reason: ${status.reason}"

        switch (status.reason) {
            case 'success':
                echo '✅ Build succeeded'
                break
            case 'unstable':
                echo '⚠️ Build is unstable (e.g., tests failed)'
                break
            case 'timeout':
                echo '⏰ Build failed due to timeout'
                break
            case 'aborted':
                echo '🛑 Build was aborted by user'
                break
            case 'exception':
                echo '❌ Build failed due to error'
                break
            default:
                echo "ℹ️ Unknown status: ${status.result}"
        }
        currentBuild.result = status.result
        catchError(buildResult: null, stageResult: null) { sendDataToDashboard(currentBuild.result, caughtError?.message) }
    }
}

@NonCPS
def getBuildStatus(Exception err) {
    def status = [:]
    status.result = 'FAILURE'
    status.reason = 'unknown'

    if (err != null) {
        def className = err.getClass().toString()
        if (className.contains('FlowInterruptedException')) {
            def causes = err.causes?.toString()
            if (causes?.contains('TimeoutStepExecution')) {
                status.result = 'FAILURE'
                status.reason = 'timeout'
            } else {
                status.result = 'ABORTED'
                status.reason = 'aborted'
            }
        } else {
            status.result = 'FAILURE'
            status.reason = 'exception'
        }
    } else {
        // no exception occurred
        def rawResult = currentBuild.rawBuild.getResult()?.toString() ?: 'SUCCESS'
        status.result = rawResult

        switch (rawResult) {
            case 'SUCCESS':
                status.reason = 'success'
                break
            case 'UNSTABLE':
                status.reason = 'unstable'
                break
            default:
                status.reason = 'unknown'
        }
    }

    return status
}

def sendDataToDashboard(String status, String errorMessage=null) {
    def requestBody = [
        name: currentBuild.projectName,
        url: currentBuild.absoluteUrl,
        build_id: currentBuild.number,
        parameters: utils.getEnvString(),
        created: env.startTime,
        trigger_event: currentBuild.getBuildCauses()*.shortDescription.toString(),
        git_mr_id: env.gitlabMergeRequestIid,
        git_mr_repo: env.gitlabSourceRepoName,
        git_mr_author: env.gitlabUserEmail,
        git_mr_source_branch: env.gitlabSourceBranch,
        git_mr_target_branch: env.gitlabTargetBranch,
        git_mr_latest_commit: env.gitlabMergeRequestLastCommit,
        status: status,
        gitlab_status_label: env.testLabel,
        node_name: env.jenkinsNode,
        node_label: env.nodeLabel,
        updated: new Date().format('yyyy-MM-dd HH:mm:ss'),
        waiting_time: (new BigInteger(env.buildRunningTime) - currentBuild.startTimeInMillis).intdiv(1000),
        duration: currentBuild.duration.intdiv(1000),
        error_message: errorMessage ?: '',
    ]
    def url = "${constants.dashboardApiUrl}/jenkins/jobs"
    httpRequest(
        url: url,
        contentType: 'APPLICATION_JSON',
        httpMode: 'POST',
        requestBody: JsonOutput.toJson(requestBody),
        consoleLogResponseBody: true,
        timeout: 30,
    )
}
