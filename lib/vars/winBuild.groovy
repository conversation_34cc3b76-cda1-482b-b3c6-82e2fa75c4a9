import org.swqa.tools.git

def build_panel(Map ciConfig, String driverType = 'hw', boolean buildMtPanel = true, boolean buildDxcPanel = true, boolean buildOglPanel = true, String buildType = 'release') {
    def productPaths = ciConfig.product_paths
    def defaultDriverPath = formatPath("${getPath(productPaths.x64.common, buildType, productPaths.x64.common.release)}")

    if (driverType.contains('nohw')) {
        defaultDriverPath = formatPath("${getPath(productPaths.x64.nohw, buildType, productPaths.x64.nohw.release)}")
    }

    def panelBuildConfigs = [
        [enabled: buildMtPanel, buildFunc: this.&build_mtpanel, panelDir: 'wddm\\mtdxum\\tools\\mtPanel\\build\\Release', panelExe: 'mtpanel.exe', panelPdb: 'mtpanel.pdb'],
        [enabled: buildDxcPanel, buildFunc: this.&build_dxcPanel, panelDir: 'wddm\\dxc\\tool\\dxcPanel\\build\\Release', panelExe: 'dxcPanel.exe', panelPdb: 'dxcPanel.pdb'],
        [enabled: buildOglPanel, buildFunc: this.&build_oglpanel, panelDir: 'wddm\\ogl\\tools\\oglpanel\\build\\Release', panelExe: 'oglpanel.exe', panelPdb: 'oglpanel.pdb']
    ]

    panelBuildConfigs.each { config ->
        if (config.enabled) {
            config.buildFunc()
            retry(5) {
                try {
                    bat """
                        copy /Y "${config.panelDir}\\${config.panelExe}" "${defaultDriverPath}\\symbols"
                        copy /Y "${config.panelDir}\\${config.panelPdb}" "${defaultDriverPath}\\symbols"
                    """
                } catch (e) {
                    echo 'Attempt failed. Retrying in 10 seconds...'
                    sleep time: 60, unit: 'SECONDS'
                    throw e
                }
            }
        }
    }
}

def build_mtpanel() {
    dir('wddm\\mtdxum\\tools\\mtPanel') {
        bat '''
            call genSettings.bat
            cmake -S .\\ -B .\\build -A Win32
        '''
        dir('build') {
            bat 'cmake --build . --config Release'
        }
    }
}

def build_dxcPanel() {
    dir('wddm\\dxc\\tool\\dxcPanel') {
        bat '''
            cmake -S .\\ -B .\\build -A Win32
        '''

        dir('build') {
            bat 'cmake --build . --config Release'
        }
    }
}

def build_oglpanel() {
    def qtPath = 'D:\\Qt\\Qt5.9.9_win64_static'
    def qtUrl = 'https://oss.mthreads.com/dependency/QT-StaticLibrary/Qt5.9.9_win64_static.7z'
    def outputPath = "${qtPath}.7z"

    if (!fileExists(qtPath)) {
        dir('D:\\Qt') {
            try {
                bat """
                    curl -sS "${qtUrl}" --output "${outputPath}" --insecure && 7z x "${outputPath}"
                """
            } catch (e) {
                error "Error occurred while downloading or extracting the file: ${e}"
            }
        }
    }

    dir('wddm\\ogl\\tools\\oglpanel') {
        bat """
            set QTTOOLS=${qtPath}
            mkdir build
            cd build
            cmake -DSHARED_INCLUDE_PATH=../../../../shared_include ../
            cmake --build . --config Release -j 12
        """
    }
}

def build_m3d(String m3d_path = 'wddm\\mtdxum\\imported\\m3d', String sharedIncludePath = "${env.WORKSPACE}/wddm/shared_include") {
    dir("${m3d_path}\\test") {
        def cmakeOptions = '-DM3D_UNIFIED_FW=ON'
        if (sharedIncludePath) {
            cmakeOptions += " -DSHARED_INCLUDE_PATH=${sharedIncludePath}"
        } else {
            cmakeOptions += ' -DWDDMM3DCI=1'
        }

        bat """
            cmake -S .\\ -B .\\build ${cmakeOptions} || exit 1
        """
    }

    dir("${m3d_path}\\test\\build") {
        bat 'cmake --build . --config Debug'
    }
}

def getPath(Map paths, String key, String defaultPath) {
    return paths.containsKey(key) ? paths[key] : defaultPath
}

def formatPath(String path) {
    return path.replace('/', '\\')
}

def upload_m3d(String repo, String branch, String commitId, String m3dPath = '') {
    def defaultDriverPath = 'wddm\\mtdxum\\imported\\m3d\\test'
    if (env.gitlabTargetBranch == 'windows_9.0_release' || env.buildBranch == 'windows_9.0_release') {
        defaultDriverPath = 'wddm\\m3d_lib\\m3d\\test'
    }
    m3dPath = m3dPath ?: defaultDriverPath
    def shortGitSha = commitId.take(9)
    def package_name = "${shortGitSha}_m3dTest.tar.gz"
    def ossBranchPath = constants.genOssPath(repo, branch, shortGitSha)
    def prefix = constants.genOssPrefix(ossBranchPath)
    oss.setUp()

    def ossAlias = constants.genOssAlias(ossBranchPath)
    dir(m3dPath) {
        bat """
            copy run_m3d_list.bat .\\build
            copy run_m3d_list_hgvps.bat .\\build
            copy run_m3d_list_ph1vps.bat .\\build || exit 1
            tar -cvzf ${package_name}  build
            mc cp ${package_name}  ${ossAlias}/${ossBranchPath}/
        """
    }
    currentBuild.description += "Driver binary: ${prefix}/${ossBranchPath}/${package_name}<br>"
}

def upload_directstream(String repo, String branch, String commitId, String directstreamPath = '') {
    def defaultDriverPath = 'DirectStream'
    directstreamPath = directstreamPath ?: defaultDriverPath
    def shortGitSha = commitId.take(9)
    def package_name = "${shortGitSha}_directstreamTest.tar.gz"
    def ossBranchPath = constants.genOssPath(repo, branch, shortGitSha)
    def prefix = constants.genOssPrefix(ossBranchPath)
    oss.setUp()

    def ossAlias = constants.genOssAlias(ossBranchPath)
    dir(directstreamPath) {
        bat """
            md test\\x64
            copy /Y build\\MTEncodeBuild\\x64\\Release\\*.exe test\\x64
            copy /Y build\\MTEncodeBuild\\Release\\*.exe test\\
            tar -cvzf ${package_name}  test
            mc cp ${package_name}  ${ossAlias}/${ossBranchPath}/
        """
    }
    currentBuild.description += "Driver binary: ${prefix}/${ossBranchPath}/${package_name}<br>"
}

def signature(Map ciConfig, String driverType = 'hw', String version, String driverPath = '', int index = 0, String infPath = 'wddm\\build\\wddm\\mtgpu\\INFs_30', String buildType = 'release', String vram = null) {
    def builds = ciConfig.builds
    def productPaths = ciConfig.product_paths
    def driverMapping = builds[driverType]
    def infName = driverMapping.infFiles[0][index]

    def defaultDriverPath = formatPath("${getPath(productPaths.x64.common, buildType, productPaths.x64.common.release)}")
    if (driverType.contains('nohw')) {
        defaultDriverPath = formatPath("${getPath(productPaths.x64.nohw, buildType, productPaths.x64.nohw.release)}")
    }

    if (driverType.contains('nohw')) {
        infPath = 'wddm\\build\\wddm\\nohw'
    }else if (driverType.contains('hw_wddm32')) {
        infPath = 'wddm\\build\\wddm\\mtgpu\\INFs_32'
    }else if (env.gitlabTargetBranch == 'windows_9.0_release' || env.buildBranch == 'windows_9.0_release') {
        infPath = 'wddm\\build\\wddm\\mtgpu\\INFs'
    }
    driverPath = driverPath ?: defaultDriverPath

    def osParam = driverType.contains('arm') ? '10_NI_ARM64' : '10_x64'

    bat """
        copy /Y "${WORKSPACE}\\${infPath}\\${infName}.inf" "${driverPath}\\"
        sed -i "s|DriverVer\\s*=\\s*[0-9]\\{1,2\\}/[0-9]\\{1,2\\}/[0-9]\\{4\\},|DriverVer = ${version},|g" "${driverPath}\\${infName}.inf"
        for %%f in (${driverPath}\\*.dll) do (
            if "%%~nxf" neq "mtdxconv64.dll" if "%%~nxf" neq "mtdxconv32.dll" (
                signtool.exe sign /a /ph /fd sha256 "%%f"
            )
        )
        signtool.exe sign /a /ph /fd sha256 "${driverPath}\\*.sys"
        inf2cat.exe /uselocaltime /os:${osParam} /driver:${driverPath}
        signtool.exe sign /a /ph /fd sha256 "${driverPath}\\*.cat"
    """

    if (vram != null) {
        bat """
            sed -i "s|FF,FF,FF,FF,FF,FF,FF,FF|00,00,00,${vram},00,00,00,00|g" "${driverPath}\\${infName}.inf"
        """
    }
}

def upload(Map ciConfig, String repo, String branch, String commitId, String driverType = 'hw', String driverPath = '', int index = 0, String buildType = 'release', String fileExtension = '.tar.gz') {
    def builds = ciConfig.builds
    def productPaths = ciConfig.product_paths
    def driverMapping = builds[driverType]
    def shortGitSha = commitId.take(9)
    def package_name = "${shortGitSha}_" + driverMapping.pkgNames[0][index] + fileExtension

    def defaultDriverPath = formatPath("${getPath(productPaths.x64.common, buildType, productPaths.x64.common.release)}")
    if (driverType.contains('nohw')) {
        defaultDriverPath = formatPath("${getPath(productPaths.x64.nohw, buildType, productPaths.x64.nohw.release)}")
    }

    driverPath = driverPath ?: defaultDriverPath
    def ossBranchPath = constants.genOssPath(repo, branch, shortGitSha)
    def prefix = constants.genOssPrefix(ossBranchPath)
    oss.setUp()

    def ossAlias = constants.genOssAlias(ossBranchPath)
    bat """
        tar -cvzf ${package_name} -C ${driverPath} *
        mc cp ${package_name} ${ossAlias}/${ossBranchPath}/
    """

    currentBuild.description += "Driver binary: ${prefix}/${ossBranchPath}/${package_name}<br>"
}

def updateLatestTxt(String ossPath, String latestFileName = 'latest.txt') {
    def basePath = ossPath.substring(0, ossPath.lastIndexOf('/'))
    def latestPath = "https://oss.mthreads.com/${ossPath}"

    if (isUnix()) {
        oss.install()
        def filePath = "${env.WORKSPACE}/${latestFileName}"
        sh """
          echo ${latestPath} > ${filePath}
          echo "Contents of ${filePath}:"
          cat ${filePath}
          mc cp ${filePath} oss/${basePath}/
        """
    } else {
        oss.setUp()
        def filePath = "%WORKSPACE%\\\\${latestFileName}"
        bat """
          echo ${latestPath} > ${filePath}
          echo "Contents of ${filePath}:"
          type ${filePath}
          mc cp ${filePath} oss/${basePath}/
        """
    }
}

def symuploader(Map ciConfig, String driverType = 'hw', String buildType = 'release') {
    def symUploaderUrl = 'https://oss.mthreads.com/release-ci/ci_tool/symuploader.exe'
    def symUploaderPath = 'D:\\win_test'
    def uploadUrl = 'https://sh-symstore.mthreads.com/upload/'
    dir(symUploaderPath) {
        bat "wget -q ${symUploaderUrl} --no-check-certificate"
    }

    def productPaths = ciConfig.product_paths
    def targetDir = driverType.contains('nohw') ?
        formatPath("${getPath(productPaths.x64.nohw, buildType, productPaths.x64.nohw.release)}") :
        formatPath("${getPath(productPaths.x64.common, buildType, productPaths.x64.common.release)}")

    bat "${symUploaderPath}\\symuploader.exe -d ${targetDir} -u ${uploadUrl} -p release"
}

def fetchMtapi(String driverPath, String mtapi_url = 'https://oss.mthreads.com/release-ci/mtapi/release_0.2/MTAPI_bbd98b6a1.zip') {
    def mtapi_pkg_name = mtapi_url.split('/')[-1]

    dir('mtapi') {
        bat """
            wget -q "${mtapi_url}" --no-check-certificate
            unzip -o "${mtapi_pkg_name}" || exit 1
        """
    }

    def dllFiles = [
        'mtapi\\WINDOWS\\Win64\\Release\\lib\\mtapi64.dll',
        'mtapi\\WINDOWS\\Win32\\Release\\lib\\mtapi32.dll'
    ]

    dllFiles.each { dllFile ->
        def targetPath = "${WORKSPACE}\\${driverPath}\\"
        bat "copy /Y \"${dllFile}\" \"${targetPath}\""
    }

    bat """
        copy /Y ${WORKSPACE}\\wddm\\build\\wddm\\mtgpu\\output\\mt_kmd\\intermediates\\fre_win10_amd64\\rgx_firmware_vgpu\\rgxfirmware_t0.elf ${WORKSPACE}\\${driverPath}\\symbols\\ || true
        copy /Y ${WORKSPACE}\\wddm\\build\\wddm\\mtgpu\\output\\mt_kmd\\intermediates\\fre_win10_amd64\\mtfw_firmware_vgpu\\mtfirmware_t0.elf ${WORKSPACE}\\${driverPath}\\symbols\\ || true
    """
}

def fetchMtml(String driverPath, String mmtmlUrl = null) {
    def latest_url_mtml = 'https://oss.mthreads.com/release-ci/management/windows/release_2.0/latest.txt'
    def mtml_url = mmtmlUrl ?: sh(script: "curl --insecure ${latest_url_mtml}", returnStdout: true).trim()
    def mtml_pkg_name = mtml_url.split('/')[-1]

    dir('mtml') {
        bat """
            wget -q "${mtml_url}" --no-check-certificate
            tar xvzf ${mtml_pkg_name}
        """
    }

    def dllFiles = [
        'mtml\\mtml.dll'
    ]

    dllFiles.each { dllFile ->
        def targetPath = "${WORKSPACE}\\${driverPath}\\"
        bat "copy /Y \"${dllFile}\" \"${targetPath}\""
    }
}

def getCommitHistory(String repo, String commitId, String ossBranchPath, String outputFile = 'commit_msg_history.txt') {
    try {
        def prefix = constants.genOssPrefix(ossBranchPath)
        def ossAlias = constants.genOssAlias(ossBranchPath)
        def basePath = ossBranchPath.substring(0, ossBranchPath.lastIndexOf('/'))
        def baseUrl = "${prefix}/${basePath}"
        def ossUrl = "${baseUrl}/${outputFile}"
        sh """
            wget -q "${ossUrl}" --no-check-certificate || echo "No existing history file"
        """

        dir(repo) {
            def commitMsg = sh(
                script: """
                    git log -1 --pretty=format:"Commit: %h%nAuthor: %an%nDate: %ad%nMessage: %s%nChanged files:" ${commitId}
                    git log -1 --name-status ${commitId}
                """,
                returnStdout: true
            ).trim()

            def timestamp = new Date().format('yyyy-MM-dd HH:mm:ss')
            def contentToAppend = """
            ==================== ${timestamp} ====================
            ${commitMsg}
            ================================================

            """

            if (!fileExists(outputFile)) {
                writeFile file: outputFile, text: contentToAppend
            } else {
                writeFile file: outputFile, text: contentToAppend, append: true
            }

            oss.setUp()

            sh """
                mc cp ${outputFile} ${ossAlias}/${basePath}/
            """

            echo "Commit history written to ${outputFile}"
            echo commitMsg
            echo '===================='

            return commitMsg
        }
    } catch (e) {
        echo "Error writing commit history: ${e.message}"
    }
}
