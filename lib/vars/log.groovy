void debug(message) {
    echo "DEBUG: ${message}"
}

void info(message) {
    echo "INFO: ${message}"
}

void warning(message) {
    echo "WARNING: ${message}"
}

void success(message) {
    echo "SUCCESS: ${message}"
}

void error(message) {
    echo "ERROR: ${message}"
}

// our jenkins server does not install AnsiColor yet
// import org.gfx.ColoredFormatter

// void info(String message) {
//     echo ColoredFormatter.blue("INFO: ${message}"))
// }

// void warning(String message) {
//     echo ColoredFormatter.yellow("WARNING: ${message}"))
// }

// void success(String message) {
//     echo ColoredFormatter.green("SUCCESS: ${message}"))
// }

// void error(String message) {
//     echo ColoredFormatter.red("ERROR: ${message}"))
// }
