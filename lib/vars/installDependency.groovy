import groovy.transform.Field
import org.swqa.tools.git

@Field def gitLib = new git()
/**
* params[0] branch
* params[1] commitId
* params[2] envExport
*/
def compileM3d(params) {
    gitLib.fetchCode('m3d', params[0], params[1])
    dir('m3d') {
        sh 'apt install -y lcov ||:'
        sh """
            ${params[2]}
            mkdir build
            cd build
            cmake ..
            make -j1
        """
        sh """
            ${params[2]}
            mkdir test/build
            cd test/build
            cmake ..
            make -j1
        """
    }
}

// kmd umd libdrm musaRuntime mtcc musaToolkits
def call(dependencies) {
    dependencies.each { key, value ->
        switch (key) {
            case 'gr-kmd':
                ddk.installKmd(value[0], value[1])
                break
            case 'gr-umd':
                ddk.installUmd(value[0], value[1])
                break
            case 'libdrm-mt':
                ddk.installLibdrm(value[0], value[1])
                break
            case 'MUSA-Runtime':
                musa.installMusaRuntime(value)
                break
            case 'mtcc':
                musa.installMtcc(value)
                break
            case 'musa_toolkit':
                musa.installMusaToolkits(value)
                break
            case 'mt-media-driver':
                ddk.installMediaDriver(value)
                break
            case 'musify':
                musa.installMusify(value)
            case 'm3d':
                compileM3d(value)
                break
            case 'musa_cmake':
                musa.installMusaCmake(value)
                break
            default:
                println 'Error dependency'
        }
    }
}
