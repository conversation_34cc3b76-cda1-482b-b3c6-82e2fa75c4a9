/**
 * MTBios Sign and Repack Tool for Jenkins Pipeline
 *
 * This shared library provides convenient functions to use the Go-based MTBios signing tool
 * in <PERSON> pipelines. It automatically downloads the appropriate binary for the
 * current platform and provides modular functions for different operations.
 *
 * Main Functions:
 *   - checkTool(): Check tool availability and download if needed, returns binary path
 *   - signBinary(binaryPath, config): Execute signing command with given binary
 *   - login(username, password, department, cookieFile): Login and save credentials
 *   - signFile(filePath, outputDir, cookieFile): Sign a single file
 *   - signFiles(fileList, outputDir, cookieFile): Sign multiple files
 *   - signFirmwareInDirectory(searchPath, pattern, excludePattern, tempDir, cookieFile): Batch sign firmware files
 *   - loginAndSign(config): Complete workflow - login and sign
 *
 * Examples:
 *   // Simple login
 *   mtbiosSign.login('<EMAIL>', 'password', 'sw')
 *
 *   // Sign a single file
 *   mtbiosSign.signFile('firmware.bin', './')
 *
 *   // Batch sign firmware files in directory
 *   mtbiosSign.signFirmwareInDirectory('lib/firmware/mthreads')
 *
 *   // Manual control for performance
 *   def binaryPath = mtbiosSign.checkTool()
 *   if (binaryPath) {
 *       mtbiosSign.signBinary(binaryPath, [file: 'firmware.bin', output: './'])
 *   }
 */

/**
 * Determine the current platform
 */
def getPlatform() {
    if (isUnix()) {
        // Check for macOS (Darwin) or Linux
        def uname = sh(script: 'uname -s', returnStdout: true).trim().toLowerCase()
        return uname.contains('darwin') ? 'darwin' : 'linux'
    }
    return 'windows'
}

/**
 * Get binary name for platform
 */
def getBinaryName(platform) {
    return platform == 'windows' ? 'mtbios-sign-repack.exe' : 'mtbios-sign-repack'
}

/**
 * Build command line from configuration
 */
def buildCommand(binaryPath, config) {
    def cmd = binaryPath

    // Add mode
    if (config.mode) {
        cmd += " -m ${config.mode}"
    }

    // Add username
    if (config.username) {
        cmd += " -u ${config.username}"
    }

    // Add password
    if (config.password) {
        cmd += " -p ${config.password}"
    }

    // Add department
    if (config.department) {
        cmd += " -d ${config.department}"
    }

    // Add file
    if (config.file) {
        cmd += " -f ${config.file}"
    }

    // Add output
    if (config.output) {
        cmd += " -o ${config.output}"
    }

    // Add cookie file
    if (config.cookie) {
        cmd += " -c ${config.cookie}"
    }

    return cmd
}

/**
 * Login to MTBios signing server and save credentials
 *
 * @param username User email address
 * @param department Department name (optional, default: 'SW')
 * @param cookieFile Cookie file path (optional, default: './login.cookie')
 */
def login(String username, String password, String department = 'SW', String cookieFile = './login.cookie') {
    def binaryPath = checkTool()
    if (!binaryPath) {
        error 'MTBios signing tool is not available'
    }

    signBinary(binaryPath, [
        mode: 'login',
        username: username,
        password: password,
        department: department,
        cookie: cookieFile
    ])
}

/**
 * Sign a firmware file using saved credentials
 *
 * @param filePath Path to file to sign
 * @param outputDir Output directory
 * @param cookieFile Cookie file path (optional, default: './login.cookie')
 */
def signFile(String filePath, String outputDir, String cookieFile = './login.cookie') {
    def binaryPath = checkTool()
    if (!binaryPath) {
        error 'MTBios signing tool is not available'
    }

    signBinary(binaryPath, [
        file: filePath,
        output: outputDir,
        cookie: cookieFile
    ])
}

/**
 * Complete workflow: login and sign
 *
 * @param config Map containing all configuration
 */
def loginAndSign(Map config) {
    // First login
    login(config.username, config.password, config.department ?: 'SW', config.cookie ?: './login.cookie')

    // Then sign
    signFile(config.file, config.output, config.cookie ?: './login.cookie')
}

/**
 * Batch sign multiple files
 *
 * @param files List of file paths to sign
 * @param outputDir Output directory
 * @param cookieFile Cookie file path (optional)
 */
def signFiles(List files, String outputDir, String cookieFile = './login.cookie') {
    files.each { file ->
        echo "Signing file: ${file}"
        signFile(file, outputDir, cookieFile)
    }
}

/**
 * Check if signing tool is available and working
 * @return String Binary path if available, null if not available
 */
def checkTool() {
    def platform = getPlatform()
    def binaryName = getBinaryName(platform)
    def resourcePath = "mtbios-sign-repack/bin/${platform}/${binaryName}"

    try {
        // Use a consistent binary path for caching
        def tempBinary = "${WORKSPACE}/mtbios-sign-repack"

        echo "Using MTBios signing tool for platform: ${platform}"

        // Check if binary already exists and is executable
        if (fileExists(tempBinary)) {
            echo "Found existing binary: ${tempBinary}"

            // Test if the binary works
            def testResult = 0
            if (platform == 'windows') {
                testResult = bat(script: "${tempBinary} -h", returnStatus: true)
            } else {
                testResult = sh(script: "${tempBinary} -h 2>/dev/null", returnStatus: true)
            }

            if (testResult == 0) {
                echo 'Existing binary is working, skipping download'
                return tempBinary
            }
            echo 'Existing binary is not working, will re-download'
        }

        // Download binary only if needed
        echo 'Downloading MTBios signing tool from shared library...'
        def binaryContent = libraryResource(resourcePath)

        // Write binary file and set permissions
        writeFile file: tempBinary, text: binaryContent, encoding: 'Base64'

        if (platform != 'windows') {
            sh "chmod +x ${tempBinary}"
        }

        // Test the downloaded binary
        def testResult = 0
        if (platform == 'windows') {
            testResult = bat(script: "${tempBinary} -h", returnStatus: true)
        } else {
            testResult = sh(script: "${tempBinary} -h 2>/dev/null", returnStatus: true)
        }

        if (testResult == 0) {
            echo "Binary downloaded and ready: ${tempBinary}"
            return tempBinary
        }
        echo 'Downloaded binary is not working'
        if (platform == 'windows') {
            bat "del /f ${tempBinary}"
        } else {
            sh "rm -f ${tempBinary}"
        }
        return null
    } catch (e) {
        echo "Tool check failed: ${e.message}"
        return null
    }
}

/**
 * Execute signing command with the binary
 * @param binaryPath Path to the MTBios signing binary
 * @param config Configuration map for the signing operation
 */
def signBinary(String binaryPath, Map config) {
    def platform = getPlatform()

    try {
        // Build command
        def cmd = buildCommand(binaryPath, config)

        // Execute command
        if (platform == 'windows') {
            bat cmd
        } else {
            sh cmd
        }
    } catch (e) {
        // If there's an error, clean up the binary so it gets re-downloaded next time
        if (fileExists(binaryPath)) {
            if (platform == 'windows') {
                bat "del /f ${binaryPath}"
            } else {
                sh "rm -f ${binaryPath}"
            }
        }
        throw e
    }
}

/**
 * Find and sign all firmware files in a directory
 *
 * @param searchPath Directory to search for firmware files
 * @param pattern File pattern to match (default: 'mtfw-gen*.bin')
 * @param excludePattern Pattern to exclude (default: 'model')
 * @param tempOutputDir Temporary output directory
 * @param cookieFile Cookie file path
 * @return Map with results: [signedCount: int, failedFiles: List, totalFiles: int]
 */
def signFirmwareInDirectory(
    String searchPath, String pattern = 'mtfw-gen*.bin',
    String excludePattern = 'model', String tempOutputDir = './temp-signed-output',
    String cookieFile = './login.cookie'
) {

    echo "Searching for firmware files in: ${searchPath}"
    echo "Pattern: ${pattern}, Excluding: ${excludePattern}"

    // Check tool availability once at the beginning
    def binaryPath = checkTool()
    if (!binaryPath) {
        error 'MTBios signing tool is not available'
    }

    // Find firmware files
    def findCommand = """
        find ${searchPath}/ -iname '${pattern}' | grep -v ${excludePattern}
    """

    def firmwareFiles = sh(
        script: findCommand,
        returnStdout: true
    ).trim()

    if (!firmwareFiles) {
        error "No firmware files found matching pattern '${pattern}' (excluding ${excludePattern})"
    }

    // Convert to list
    def fileList = firmwareFiles.split('\n').findAll { it.trim() }

    echo "Found ${fileList.size()} firmware files to sign:"
    fileList.each { file ->
        echo "  - ${file}"
    }

    // Create temp directory
    sh "mkdir -p ${tempOutputDir}"

    // Sign each file
    def signedCount = 0
    def failedFiles = []

    fileList.each { firmwareFile ->
        try {
            echo "Signing file [${signedCount + 1}/${fileList.size()}]: ${firmwareFile}"

            // Get absolute paths
            def absoluteFirmwarePath = "${WORKSPACE}/${firmwareFile}"
            def absoluteTempDir = "${WORKSPACE}/${tempOutputDir}"

            // Sign the firmware file using signBinary
            signBinary(binaryPath, [
                file: absoluteFirmwarePath,
                output: absoluteTempDir,
                cookie: cookieFile
            ])

            // Get the signed file name (same as original)
            def fileName = sh(
                script: "basename ${firmwareFile}",
                returnStdout: true
            ).trim()

            def signedFile = "${absoluteTempDir}/${fileName}"

            // Verify signed file exists
            if (!fileExists(signedFile)) {
                throw new Exception("Signed file not found: ${signedFile}")
            }

            // Move signed file back to original location (overwrite)
            sh "mv ${signedFile} ${absoluteFirmwarePath}"

            // Verify the move was successful
            def newSize = sh(
                script: "stat -c%s ${absoluteFirmwarePath}",
                returnStdout: true
            ).trim()

            echo "✓ Successfully signed and replaced: ${firmwareFile} (${newSize} bytes)"
            signedCount++
        } catch (e) {
            echo "✗ Failed to sign ${firmwareFile}: ${e.getMessage()}"
            failedFiles.add(firmwareFile)
        }
    }

    // Summary
    echo '=== Signing Summary ==='
    echo "Total files processed: ${fileList.size()}"
    echo "Successfully signed: ${signedCount}"
    echo "Failed: ${failedFiles.size()}"

    if (failedFiles.size() > 0) {
        echo 'Failed files:'
        failedFiles.each { file ->
            echo "  - ${file}"
        }

        if (signedCount == 0) {
            error 'All firmware signing operations failed!'
        } else {
            echo 'Warning: Some files failed to sign, but continuing with partial success'
        }
    }

    return [
        signedCount: signedCount,
        failedFiles: failedFiles,
        totalFiles: fileList.size()
    ]
}
