# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: check-added-large-files
      - id: check-toml
      - id: check-yaml
        args:
          - --unsafe
      - id: end-of-file-fixer
      - id: trailing-whitespace
  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.3.0
    hooks:
      - id: ruff
        args:
          - --fix
      - id: ruff-format
  - repo: https://github.com/nvuillam/npm-groovy-lint
    rev: v15.1.0
    hooks:
      # - id: npm-groovy-lint
      #   name: Lint groovy files
      #   description: Groovy & Jenkinsfile Linter
      #   entry: npm-groovy-lint -i .history --output txt jenkinsfile lib
      #   language: node
      #   types: [groovy]
      # - id: format-npm-groovy-lint
      #   name: Format Lint groovy findings
      #   description: Groo<PERSON> & Jenkinsfile Formatter
      #   entry: npm-groovy-lint -i .history --format --output txt jenkinsfile lib
      #   language: node
      #   types: [groovy]
      - id: fix-npm-groovy-lint
        name: Fix Lint groovy findings
        description: Groovy & Jenkinsfile Auto-fixer
        entry: npm-groovy-lint -i .history --fix --output txt jenkinsfile lib
        language: node
        types: [groovy]
